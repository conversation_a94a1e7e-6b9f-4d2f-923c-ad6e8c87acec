import { NgModule, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { routing } from './alarmconfig.routing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { PermissionGuard } from '@communal/guard/permission.guard';
import { CoreModule } from '@app/core/core.module';
import { CommunalModule } from '@communal/communal.module';
import { DeviceCategoryService } from '@app/pages/monitor/services/deviceCategory.service';
import { AppTranslationModule } from '@app/app.translation.module';
import { SysConfigService } from '@backstage/misc/sysConfig/services/sysConfig.service';
import { BaseCRUDService } from '@communal/providers/baseCRUDService/baseCRUDService';
import { ToastService } from '@communal/providers/toast.service';
import { AlarmConfigComponent } from './alarmconfig.component';
import { AlarmAutoConfirmComponent } from './alarm-auto-confirm/alarm-auto-confirm.component';
import { SelectModule } from 'ng-select';
import { PersonGroupService } from '@app/pages/admin/person-group/services/person-group.service';
import { EventSeverityConfigComponent } from './event-severity-config/event-severity-config.component';
// import { ColorPickerModule } from 'ngx-color-picker';
import { ColorSketchModule } from 'ngx-color/sketch';
import { CoreeventseverityService } from '@app/communal/providers/coreeventseverity.service';
import { AlarmReasonConfigComponent } from './alarm-reason/alarm-reason.component';
import { Ng2SmartTableModule } from 'ng2-smart-table';
import { AlarmReasonConfigModalComponent } from './alarm-reason/alarm-reason-modal/alarm-reason-modal.component';

@NgModule({
    imports: [
        routing,
        CoreModule.forRoot(),
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        NgbModule,
        // ColorPickerModule,
        CommunalModule,
        AppTranslationModule,
        Ng2SmartTableModule,
        SelectModule,
        ColorSketchModule,
    ],
    declarations: [
        AlarmConfigComponent,
        AlarmAutoConfirmComponent,
        EventSeverityConfigComponent,
        AlarmReasonConfigComponent,
        AlarmReasonConfigModalComponent,
    ],
    providers: [
        DeviceCategoryService,
        SysConfigService,
        PermissionGuard,
        BaseCRUDService,
        ToastService,
        PersonGroupService,
        CoreeventseverityService
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AlarmconfigModule { }
