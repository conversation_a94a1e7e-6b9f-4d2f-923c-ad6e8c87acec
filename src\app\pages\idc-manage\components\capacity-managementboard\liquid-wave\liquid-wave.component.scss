@import "../../../../../core/scss/ng2-admin-scss/conf/conf.scss";
@import "../../../../../core/scss/phoenix-scss/conf/phoenix-conf.scss";
:host ::ng-deep {
  .liquid-wave-wrapper {
    padding: 10px;
    background-color: var(--pagination-bg);
  }
  
  .title-wrapper {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;

    .vertical-line {
      width: 5px;
      height: 15px;
      background-color: var(--link-active-color);
      margin-right: 8px;
      // border-radius: 2px;
    }

    .title {
      // font-weight: 600;
      color: var(--portal-tabs-text-color);
      font-size: 16px;
      margin: 0;
    }
  }

  .content-wrapper {
    display: flex;

    .canvas-container {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      canvas {
        width: 8rem;
        height: 8rem;
      }
    }

    .data-container {
      width: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      .data-entries {
        width: 100%;
        .data-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 1rem;
          
          .label, .value {
            color: var(--portal-text-color);
            opacity: 0.8;
          }

          .value {
            // font-weight: 500;
            // color: var(--portal-text-color);
            color: var(--portal-tabs-text-color);
          }
        }
      }
    }
  }
} 