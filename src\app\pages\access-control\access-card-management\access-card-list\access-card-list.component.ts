import {Component, OnInit, Injector, ViewChild} from '@angular/core';
import {BaseListViewComponent} from '@app/communal/components/base-list-view/base-list-view.component';
import {AccessCardManagementService} from '../../services/access-card-management.service';
import {clone} from 'lodash';
import {LocalDataSource, Ng2SmartTableComponent} from 'ng2-smart-table';
import {DoorCardInfo} from '../../model/door-card.model';
import {DoorAreaOperationRes} from '../../model/door-area.model';
import {ToastService} from '@app/communal/providers';
import {forkJoin} from 'rxjs';
import {SessionService} from '@app/core/services/session.service';
import {OperationPermession} from '@app/communal/models/operationPermession';
import {ToFaceCollectComponent} from '../to-face-collect.component';
import {ToFingerprintCollectComponent} from '../to-fingerprint-collect.component';
import {
    TableBaseCellComponent
} from '@app/communal/components/base-list-view/table-base-cell/table-base-cell.component';
import {CardColumns, CardColumnsLan} from "./card-excel.model";
import * as XLSX from "xlsx";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {SweetAlertResult} from "sweetalert2";
import {AccessCardLostCopyComponent} from "@app/pages/access-control/access-card-management/access-card-list/access-card-lost-copy/access-card-lost-copy.component";

@Component({
    selector: 'app-access-card-list',
    templateUrl: './access-card-list.component.html',
    styleUrls: ['./access-card-list.component.scss'],
})
export class AccessCardListComponent extends BaseListViewComponent implements OnInit {
    @ViewChild('table', {static: true}) ng2SmartTable: Ng2SmartTableComponent;
    DEFAULT_ID = 'cardId';
    DOOR_SYSTEM_TENCENT_TYPE = '12';
    langKeysPre = 'accessControl.cardManagement';
    selectMode = 'multi';
    groupId: number;
    groupNode = {
        groupName: '',
    };
    selectedList: DoorCardInfo[] = [];
    personList: any[] = [];
    searchText = '';
    hasControlPermission: boolean = true;
    isTencentDoorType: boolean = false;
    total:number=0;
    // 分页状态保存
    currentPage: number = 1;
    currentPerPage: number = 20;
    constructor(
        injector: Injector,
        private ngModal: NgbModal,
        private service: AccessCardManagementService,
        private toastService: ToastService,
        private sessionService: SessionService
    ) {
        super(injector, service);
        this.hasControlPermission = this.sessionService.hasPermissionEnum(
            OperationPermession.EquipmentControl
        );
        if (this.sessionService.getSysconfigByKey('door.system.type')) {
            this.isTencentDoorType =
                this.DOOR_SYSTEM_TENCENT_TYPE ===
                this.sessionService.getSysconfigByKey('door.system.type')
                    .systemConfigValue;
        }
    }

    ngOnInit(): void {
        this.activatedRoute.paramMap.subscribe((params) => {
            this.groupId = params.has('groupId') && Number(params.get('groupId'));
            this.getCardListForGroup(this.groupId);
        });

        // 监听查询参数以恢复分页状态
        this.activatedRoute.queryParams.subscribe(params => {
            if (params.page) {
                this.currentPage = parseInt(params.page, 10);
            }
            if (params.perPage) {
                this.currentPerPage = parseInt(params.perPage, 10);
            }
        });

        this.globalState.subscribe('accessControl.collect.modal.close', () => {
            this.getSource();
        })
    }

    getCardListForGroup(groupId?: number): void {
        this.baseListTableOptions.actions = {
            add: false,
            edit: true,
            delete: true,
            position: 'right',
        };

        this.baseListTableOptions.columns = [
            {name: 'cardName', title: 'cardName'},
            {name: 'cardCode', title: 'cardNo'},
            {name: 'cardCodeType', title: 'cardNoType'},
            {
                name: 'userId',
                title: 'cardHolder',
                type: 'html',
                valuePrepareFunction: (cell) => {
                    const person = this.personList.find(
                        (person) => person.personId === cell
                    );
                    return `${person ? person.name : ''}`;
                },
            },
            {
                name: 'cardGroupName',
                title: 'cardGroup',
                valuePrepareFunction: (cell) => cell,
            },
            {
                name: 'cardStatusName',
                title: 'useState',
                valuePrepareFunction: (cell) => cell,
            },
            {
                name: 'faceId',
                title: 'face',
                type: 'custom',
                valuePrepareFunction: (cell, row) => row,
                renderComponent: ToFaceCollectComponent as Component,
            },
            {
                name: 'fingerprintId',
                title: 'fingerprint',
                type: 'custom',
                valuePrepareFunction: (cell, row) => row,
                renderComponent: ToFingerprintCollectComponent as Component,
            },
        ];

        if (!this.isTencentDoorType) {
            this.baseListTableOptions.columns.push(
                {
                    name: 'startTime',
                    title: 'inDateStart',
                    valuePrepareFunction: (cell) => cell,
                    renderComponent: TableBaseCellComponent as Component,
                },
                {
                    name: 'endTime',
                    title: 'inDateEnd',
                    valuePrepareFunction: (cell) => cell,
                    renderComponent: TableBaseCellComponent as Component,
                }
            );
        }
        this.load();
    }

    getSource(): void {
        forkJoin([
            this.baseService['getDoorCardListByGroup'](this.groupId),
            this.service.get(null, 'persons'),
        ]).subscribe((res: any) => {
            if (!res || !res[0] || !res[0].data) {
                this.source = new LocalDataSource([]);
                return;
            }
            this.personList = res[1];
            this.currentDataList = res[0].data;
            const dataSource = clone(res[0].data);
            const orederBy = clone(this.orderBy);
            if (orederBy && orederBy.updateTime && dataSource) {
                dataSource.sort((a, b) => {
                    const at = new Date(clone(a.updateTime));
                    const bt = new Date(clone(b.updateTime));
                    return at > bt ? -1 : 1;
                });
            }
            this.source = new LocalDataSource(dataSource);
            this.source.onChanged().subscribe((data) => {
                this.total = this.source.count()
                this.setFrozenList();
            });

            // 延迟恢复分页状态，确保表格完全初始化
            setTimeout(() => {
                this.restorePagingState();
            }, 800);
        });
    }

    selectRows(event): void {
        this.selectedList = event.selected;
    }

    /**
     * 监听分页变化事件
     */
    onPageChange(event): void {
        console.log('分页变化事件:', event);
        if (event && event.paging) {
            this.currentPage = event.paging.page;
            this.currentPerPage = event.paging.perPage;
            console.log('更新分页状态:', { page: this.currentPage, perPage: this.currentPerPage });
        }
    }

    edit(event): void {
        const id = event.data[this.DEFAULT_ID];

        // 获取当前实际的分页状态
        if (this.ng2SmartTable && this.ng2SmartTable.source) {
            const paging = this.ng2SmartTable.source.getPaging();
            this.currentPage = paging.page;
            this.currentPerPage = paging.perPage;
        }

        console.log('编辑时的分页状态:', { page: this.currentPage, perPage: this.currentPerPage });

        // 保存到sessionStorage
        sessionStorage.setItem('access-card-list-page', this.currentPage.toString());
        sessionStorage.setItem('access-card-list-perPage', this.currentPerPage.toString());

        this.router.navigate(['./edit', id], {
            relativeTo: this.activatedRoute,
            queryParams: {
                page: this.currentPage,
                perPage: this.currentPerPage
            }
        });
    }

    deleteAll(isSingle?: boolean, event?: any, name: string = 'name'): void {
        if (!isSingle && (!this.selectedList || this.selectedList.length === 0)) {
            this.toastService.showInfoToast({
                msg: this.translateService.instant('accessControl.cardManagement.selectDeleteCard'),
                position: this.toastService.position.top_right,
            });
            return;
        }
        const ids = !isSingle ? this.selectedList.map((item) => item.cardId) : [event.data[this.DEFAULT_ID]];
        const langKey: string = isSingle ? 'accessControl.cardManagement.deleteConfirm' : 'accessControl.cardManagement.deleteAllConfirm';
        const singleName = isSingle ? `${event.data[name]}?` : '';
        const content = this.translateService.instant(langKey) + singleName;
        this.alertService.deleteConfirm(content).then((res: SweetAlertResult) => {
            if (res.value) {
                this.service.deleteCards(ids).subscribe((res: DoorAreaOperationRes) => {
                    if (res && res.data) {
                        this.getSource();
                        this.toastService.showSuccessToast({
                            msg: this.translateService.instant('accessControl.cardManagement.deleteCardsSuccess'),
                            position: this.toastService.position.top_right,
                        });
                    } else {
                        this.toastService.showErrorToast({
                            msg: this.translateService.instant('accessControl.cardManagement.deleteCardsFail'),
                            position: this.toastService.position.top_right,
                        });
                    }
                });
            }
        });
    }

    // 前端导出
    excelExport(): void {
        const fileName: string = `${this.translateService.instant('s2.accessControl.cardManagement')}.xlsx`;
        const xlsTemplateJson: string[][] = [CardColumnsLan().map(column => this.translateService.instant(column))];
        if (this.source['filteredAndSorted']) {
            this.source['filteredAndSorted'].forEach(element => {
                const sc: CardColumns = new CardColumns();
                const person = this.personList.find(person => person.personId === element.userId);

                sc.cardCode = element.cardCode;//卡号
                sc.cardName = element.cardName;//卡名称
                sc.cardCodeType = element.cardCodeType;//卡类型
                sc.cardHolder = person ? person.name : '';//持卡人
                sc.cardStatus = element.cardStatusName;//卡状态
                sc.cardGroup = element.cardGroupName;//卡分组
                sc.registerTime = element.registerTime;//注册时间
                sc.startTime = element.startTime;//有效开始
                sc.endTime = element.endTime;//有效结束
                sc.description = element.description;//备注

                xlsTemplateJson.push(Object.values(sc));
            });

            const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, this.translateService.instant('s2.accessControl.cardManagement'));
            XLSX.writeFile(wb, fileName);
        }
    }

    // 卡丢失 of 卡复制
    cardOperate(classify: string): void {
        const modal: NgbModalRef = this.ngModal.open(AccessCardLostCopyComponent, {
            backdrop: 'static',
            size: 'sm',
        })
        modal.componentInstance.classify = classify
        modal.result.then(res => {
            if (res) this.getSource()
        })
    }

    ngOnDestroy(): void {
        this.globalState.unSubscribe('accessControl.collect.modal.close')
        super.ngOnDestroy();
    }



    /**
     * 恢复分页状态
     */
    private restorePagingState(): void {
        // 从查询参数或sessionStorage获取分页状态
        const queryPage = this.activatedRoute.snapshot.queryParams.page;
        const queryPerPage = this.activatedRoute.snapshot.queryParams.perPage;
        const savedPage = sessionStorage.getItem('access-card-list-page');
        const savedPerPage = sessionStorage.getItem('access-card-list-perPage');

        const targetPage = queryPage ? parseInt(queryPage, 10) : (savedPage ? parseInt(savedPage, 10) : 1);
        const targetPerPage = queryPerPage ? parseInt(queryPerPage, 10) : (savedPerPage ? parseInt(savedPerPage, 10) : 20);

        console.log('准备恢复分页状态:', { targetPage, targetPerPage });

        if (this.ng2SmartTable && this.ng2SmartTable.source && (targetPage > 1 || targetPerPage !== 20)) {
            // 使用多次尝试确保恢复成功
            let attempts = 0;
            const maxAttempts = 5;

            const tryRestore = () => {
                attempts++;
                console.log(`第${attempts}次尝试恢复分页状态`);

                try {
                    // 先设置每页条数
                    if (targetPerPage !== 20) {
                        this.ng2SmartTable.source.setPaging(1, targetPerPage, false);
                    }

                    // 再设置当前页
                    if (targetPage > 1) {
                        this.ng2SmartTable.source.setPage(targetPage, false);
                    }

                    // 验证是否设置成功
                    const currentPaging = this.ng2SmartTable.source.getPaging();
                    console.log('当前分页状态:', currentPaging);

                    if (currentPaging.page === targetPage && currentPaging.perPage === targetPerPage) {
                        console.log('分页状态恢复成功');
                        // 清除临时数据
                        sessionStorage.removeItem('access-card-list-page');
                        sessionStorage.removeItem('access-card-list-perPage');
                        return;
                    }
                } catch (error) {
                    console.error('恢复分页状态时出错:', error);
                }

                // 如果没有成功且还有尝试次数，继续尝试
                if (attempts < maxAttempts) {
                    setTimeout(tryRestore, 500);
                } else {
                    console.log('分页状态恢复失败，已达到最大尝试次数');
                }
            };

            setTimeout(tryRestore, 500);
        } else {
            console.log('无需恢复分页状态或表格未初始化');
        }
    }
}
