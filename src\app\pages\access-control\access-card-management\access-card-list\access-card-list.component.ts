import {Component, OnInit, Injector, ViewChild} from '@angular/core';
import {BaseListViewComponent} from '@app/communal/components/base-list-view/base-list-view.component';
import {AccessCardManagementService} from '../../services/access-card-management.service';
import {clone} from 'lodash';
import {LocalDataSource, Ng2SmartTableComponent} from 'ng2-smart-table';
import {DoorCardInfo} from '../../model/door-card.model';
import {DoorAreaOperationRes} from '../../model/door-area.model';
import {ToastService} from '@app/communal/providers';
import {forkJoin} from 'rxjs';
import {SessionService} from '@app/core/services/session.service';
import {OperationPermession} from '@app/communal/models/operationPermession';
import {ToFaceCollectComponent} from '../to-face-collect.component';
import {ToFingerprintCollectComponent} from '../to-fingerprint-collect.component';
import {
    TableBaseCellComponent
} from '@app/communal/components/base-list-view/table-base-cell/table-base-cell.component';
import {CardColumns, CardColumnsLan} from "./card-excel.model";
import * as XLSX from "xlsx";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {SweetAlertResult} from "sweetalert2";
import {AccessCardLostCopyComponent} from "@app/pages/access-control/access-card-management/access-card-list/access-card-lost-copy/access-card-lost-copy.component";

@Component({
    selector: 'app-access-card-list',
    templateUrl: './access-card-list.component.html',
    styleUrls: ['./access-card-list.component.scss'],
})
export class AccessCardListComponent extends BaseListViewComponent implements OnInit {
    @ViewChild('table', {static: true}) ng2SmartTable: Ng2SmartTableComponent;
    DEFAULT_ID = 'cardId';
    DOOR_SYSTEM_TENCENT_TYPE = '12';
    langKeysPre = 'accessControl.cardManagement';
    selectMode = 'multi';
    groupId: number;
    groupNode = {
        groupName: '',
    };
    selectedList: DoorCardInfo[] = [];
    personList: any[] = [];
    searchText = '';
    hasControlPermission: boolean = true;
    isTencentDoorType: boolean = false;
    total:number=0;
    // 分页状态保存
    currentPage: number = 1;
    currentPerPage: number = 20;
    constructor(
        injector: Injector,
        private ngModal: NgbModal,
        private service: AccessCardManagementService,
        private toastService: ToastService,
        private sessionService: SessionService
    ) {
        super(injector, service);
        this.hasControlPermission = this.sessionService.hasPermissionEnum(
            OperationPermession.EquipmentControl
        );
        if (this.sessionService.getSysconfigByKey('door.system.type')) {
            this.isTencentDoorType =
                this.DOOR_SYSTEM_TENCENT_TYPE ===
                this.sessionService.getSysconfigByKey('door.system.type')
                    .systemConfigValue;
        }
    }

    ngOnInit(): void {
        this.activatedRoute.paramMap.subscribe((params) => {
            this.groupId = params.has('groupId') && Number(params.get('groupId'));
            this.getCardListForGroup(this.groupId);
        });

        // 监听查询参数以恢复分页状态
        this.activatedRoute.queryParams.subscribe(params => {
            if (params.page) {
                this.currentPage = parseInt(params.page, 10);
            }
            if (params.perPage) {
                this.currentPerPage = parseInt(params.perPage, 10);
            }
        });

        this.globalState.subscribe('accessControl.collect.modal.close', () => {
            this.getSource();
        })
    }

    getCardListForGroup(groupId?: number): void {
        this.baseListTableOptions.actions = {
            add: false,
            edit: true,
            delete: true,
            position: 'right',
        };

        this.baseListTableOptions.columns = [
            {name: 'cardName', title: 'cardName'},
            {name: 'cardCode', title: 'cardNo'},
            {name: 'cardCodeType', title: 'cardNoType'},
            {
                name: 'userId',
                title: 'cardHolder',
                type: 'html',
                valuePrepareFunction: (cell) => {
                    const person = this.personList.find(
                        (person) => person.personId === cell
                    );
                    return `${person ? person.name : ''}`;
                },
            },
            {
                name: 'cardGroupName',
                title: 'cardGroup',
                valuePrepareFunction: (cell) => cell,
            },
            {
                name: 'cardStatusName',
                title: 'useState',
                valuePrepareFunction: (cell) => cell,
            },
            {
                name: 'faceId',
                title: 'face',
                type: 'custom',
                valuePrepareFunction: (cell, row) => row,
                renderComponent: ToFaceCollectComponent as Component,
            },
            {
                name: 'fingerprintId',
                title: 'fingerprint',
                type: 'custom',
                valuePrepareFunction: (cell, row) => row,
                renderComponent: ToFingerprintCollectComponent as Component,
            },
        ];

        if (!this.isTencentDoorType) {
            this.baseListTableOptions.columns.push(
                {
                    name: 'startTime',
                    title: 'inDateStart',
                    valuePrepareFunction: (cell) => cell,
                    renderComponent: TableBaseCellComponent as Component,
                },
                {
                    name: 'endTime',
                    title: 'inDateEnd',
                    valuePrepareFunction: (cell) => cell,
                    renderComponent: TableBaseCellComponent as Component,
                }
            );
        }
        this.load();

        // 在表格设置完成后恢复分页状态
        setTimeout(() => {
            this.restorePagingState();
        }, 300);
    }

    getSource(): void {
        forkJoin([
            this.baseService['getDoorCardListByGroup'](this.groupId),
            this.service.get(null, 'persons'),
        ]).subscribe((res: any) => {
            if (!res || !res[0] || !res[0].data) {
                this.source = new LocalDataSource([]);
                return;
            }
            this.personList = res[1];
            this.currentDataList = res[0].data;
            const dataSource = clone(res[0].data);
            const orederBy = clone(this.orderBy);
            if (orederBy && orederBy.updateTime && dataSource) {
                dataSource.sort((a, b) => {
                    const at = new Date(clone(a.updateTime));
                    const bt = new Date(clone(b.updateTime));
                    return at > bt ? -1 : 1;
                });
            }
            this.source = new LocalDataSource(dataSource);
            this.source.onChanged().subscribe((data) => {
                this.total = this.source.count()
                this.setFrozenList();
            });

            // 恢复分页状态
            this.restorePagingState();
        });
    }

    selectRows(event): void {
        this.selectedList = event.selected;
    }

    edit(event): void {
        const id = event.data[this.DEFAULT_ID];
        // 保存当前分页状态
        this.savePagingState();
        this.router.navigate(['./edit', id], {
            relativeTo: this.activatedRoute,
            queryParams: {
                page: this.currentPage,
                perPage: this.currentPerPage
            }
        });
    }

    deleteAll(isSingle?: boolean, event?: any, name: string = 'name'): void {
        if (!isSingle && (!this.selectedList || this.selectedList.length === 0)) {
            this.toastService.showInfoToast({
                msg: this.translateService.instant('accessControl.cardManagement.selectDeleteCard'),
                position: this.toastService.position.top_right,
            });
            return;
        }
        const ids = !isSingle ? this.selectedList.map((item) => item.cardId) : [event.data[this.DEFAULT_ID]];
        const langKey: string = isSingle ? 'accessControl.cardManagement.deleteConfirm' : 'accessControl.cardManagement.deleteAllConfirm';
        const singleName = isSingle ? `${event.data[name]}?` : '';
        const content = this.translateService.instant(langKey) + singleName;
        this.alertService.deleteConfirm(content).then((res: SweetAlertResult) => {
            if (res.value) {
                this.service.deleteCards(ids).subscribe((res: DoorAreaOperationRes) => {
                    if (res && res.data) {
                        this.getSource();
                        this.toastService.showSuccessToast({
                            msg: this.translateService.instant('accessControl.cardManagement.deleteCardsSuccess'),
                            position: this.toastService.position.top_right,
                        });
                    } else {
                        this.toastService.showErrorToast({
                            msg: this.translateService.instant('accessControl.cardManagement.deleteCardsFail'),
                            position: this.toastService.position.top_right,
                        });
                    }
                });
            }
        });
    }

    // 前端导出
    excelExport(): void {
        const fileName: string = `${this.translateService.instant('s2.accessControl.cardManagement')}.xlsx`;
        const xlsTemplateJson: string[][] = [CardColumnsLan().map(column => this.translateService.instant(column))];
        if (this.source['filteredAndSorted']) {
            this.source['filteredAndSorted'].forEach(element => {
                const sc: CardColumns = new CardColumns();
                const person = this.personList.find(person => person.personId === element.userId);

                sc.cardCode = element.cardCode;//卡号
                sc.cardName = element.cardName;//卡名称
                sc.cardCodeType = element.cardCodeType;//卡类型
                sc.cardHolder = person ? person.name : '';//持卡人
                sc.cardStatus = element.cardStatusName;//卡状态
                sc.cardGroup = element.cardGroupName;//卡分组
                sc.registerTime = element.registerTime;//注册时间
                sc.startTime = element.startTime;//有效开始
                sc.endTime = element.endTime;//有效结束
                sc.description = element.description;//备注

                xlsTemplateJson.push(Object.values(sc));
            });

            const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson);
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, this.translateService.instant('s2.accessControl.cardManagement'));
            XLSX.writeFile(wb, fileName);
        }
    }

    // 卡丢失 of 卡复制
    cardOperate(classify: string): void {
        const modal: NgbModalRef = this.ngModal.open(AccessCardLostCopyComponent, {
            backdrop: 'static',
            size: 'sm',
        })
        modal.componentInstance.classify = classify
        modal.result.then(res => {
            if (res) this.getSource()
        })
    }

    ngOnDestroy(): void {
        this.globalState.unSubscribe('accessControl.collect.modal.close')
        super.ngOnDestroy();
    }

    /**
     * 保存当前分页状态
     */
    private savePagingState(): void {
        if (this.ng2SmartTable && this.ng2SmartTable.source) {
            const paging = this.ng2SmartTable.source.getPaging();
            this.currentPage = paging.page;
            this.currentPerPage = paging.perPage;
        }
    }

    /**
     * 恢复分页状态
     */
    private restorePagingState(): void {
        if (this.ng2SmartTable && this.ng2SmartTable.source) {
            setTimeout(() => {
                // 设置每页条数
                if (this.currentPerPage && this.currentPerPage !== 20) {
                    const paging = this.ng2SmartTable.source.getPaging();
                    paging.perPage = this.currentPerPage;
                    this.ng2SmartTable.source.setPaging(1, this.currentPerPage, false);
                }

                // 设置当前页
                if (this.currentPage && this.currentPage > 1) {
                    this.ng2SmartTable.source.setPage(this.currentPage, false);
                }
            }, 200);
        }
    }
}
