import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import * as echarts from 'echarts';
import { ThemeService } from '@app/theme/theme.service';
import { CssVariableService } from '@app/theme/theme.css-variable.service';

import { NzTreeComponent, NzTreeNode } from 'ng-zorro-antd/tree';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { GlobalResourceService } from '@app/communal/providers/global-resource.service';
import { SessionService } from '@core/services/session.service';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import { ToastService } from '@communal/providers';


@Component({
    selector: 'app-it-cabinet',
    templateUrl: './it-cabinet.component.html',
    styleUrls: ['./it-cabinet.component.scss']
})
export class ITCabinetComponent implements OnInit {
    @ViewChild('nzTree') nzTree!: NzTreeComponent;
    defaultRange = DateUtil.dateFormat(new Date(), 'YYYY');

    selectedNode: any = {
        title:''
    };
    nodes: any[] = [];
    searchText: string = '';
    inputSearchText: string = '';
    searchTimer: any;
    dateType: string = 'year';
    powerLoadRate: number = null;
    ratedPower: number = null;
    currentPower: number = null;
    uPositionRate: number = 60;
    defaultExpandedKeys: string[] = [];
    colors: any[] = ['#F8DB76', '#9673FF', '#00BFFF', '#00D7A3', '#FF9255', '#265CFF', '#5C26FF'];
    dateList = []
    noFilter:any
    defaultSelectedKeys = [];
    formateParams:any = { startTime: '', endTime: '' };
    powerBarChart: any
    powerTrendChart: any
    loadRateChart: any
    powerLoadChart:  any
    nzTreeHeight
    constructor(
        private translate: TranslateService,
        private themeService: ThemeService,
        private cssVariableService: CssVariableService,
        private globalResourceService: GlobalResourceService,
        private sessionService: SessionService,
        private baseService: BaseCRUDService,
         private toastService: ToastService
    ) { }

    ngOnInit() {
        this.dateList = [
            { label: this.translateKey('energyPanorama.totalAnalysis.year'), value: 'year' },
            { label: this.translateKey('energyPanorama.totalAnalysis.month'), value: 'month' },
            { label: this.translateKey('energyPanorama.totalAnalysis.quarter'), value: 'quarter' },
            { label: this.translateKey('energyPanorama.totalAnalysis.self'), value: 'self' },
        ]
        this.nzTreeHeight = (document.body.offsetHeight - 145).toString() + 'px';
        this.getTreeData()
    }
    async getTreeData() {
        this.globalResourceService.clearCache();
        const userId: string = this.sessionService.getUserId();
        let allTreeData = await this.globalResourceService.getNodeTreesByUserId(null, userId);
        
        const { resultNodes, expandedKeys, selectedKeys, selectedRows } = this.transformAndFilterTreeData(allTreeData);
        this.nodes = resultNodes
        this.defaultExpandedKeys = expandedKeys;
        this.defaultSelectedKeys = selectedKeys
        if (selectedRows && selectedRows.length > 0) {
             this.selectedNode = selectedRows[0]
        } else {
            this.selectedNode = {title: ''}
        }
        this.getRangeVal(null)
    }
    transformAndFilterTreeData(nodes): { resultNodes: any, expandedKeys: any, selectedKeys: any, selectedRows: any } {
        if (!nodes || !nodes.length)
            return { resultNodes: [], expandedKeys: [], selectedKeys: [], selectedRows: [] };

        const expandedKeys = [];  // 用于存储需要展开的节点key
        const selectedKeys = [];  // 用于存储选中的节点key
        let selectedRows = []


        let firstFloorExpanded = false;  // 标记是否展开了第一个楼层
        let firstRoomExpanded = false;
        let firstRack = false
        const traverse = (nodes, parentNode = null) => {
            return nodes.reduce((filtered, node) => {

                let children = [];
                if (node.children && node.children.length) {
                    children = traverse(node.children,node);
                }
                if (node.objectTypeId === 9 || children.length > 0) {
                    const newNode = {
                        key: node.objectId,
                        title: node.resourceName,
                        objectTypeId: node.objectTypeId,
                        isLeaf: !children.length,
                        disabled: node.objectTypeId !== 9
                    };
                    if (children.length) {
                        newNode['children'] = children;
                    }
                   
                    if (!firstFloorExpanded && node.objectTypeId === 3) {// 3大楼
                        if (parentNode) {
                            expandedKeys.push(parentNode.objectId);  
                        }
                        expandedKeys.push(node.objectId); 
                        firstFloorExpanded = true;
                    }

                    if (!firstRoomExpanded && node.objectTypeId === 4) { // 4楼层
                        expandedKeys.push(node.objectId);  
                        firstRoomExpanded = true; 
                    }
                    if (!firstRack && node.objectTypeId === 9) { // 选中第一个机架
                        selectedKeys.push(node.objectId); 
                        let selectedObj = {
                            ...node,
                            key: node.objectId, 
                            isLeaf: !node.children.length,
                            disabled: false,
                            title: node.resourceName,
                            objectTypeId: node.objectTypeId
                        }
                        selectedRows.push(selectedObj);
                        firstRack = true
                    }
                    filtered.push(newNode);
                }

                return filtered;
            }, []);
        };

        const resultNodes = traverse(nodes);

        return { resultNodes, expandedKeys, selectedKeys, selectedRows };
    }
   

    translateKey(key) {
        return this.translate.instant(key)
    }
    getRangeVal(val) {
        if (val) {
            const { startTime, endTime } = val
            if (this.dateType === 'self') {
                let startYear = startTime.split('-')[0]
                let endYear = endTime.split('-')[0]
                if (startYear !== endYear) {
                    this.toastService.showToast('warning', '时间范围不允许跨年,请重新选择', null, { timeOut: 3000});
                    return
                }
            }
            this.formateParams = this.interfaceParams(startTime, endTime)
        } else {
            this.formateParams = this.interfaceParams(this.defaultRange, this.defaultRange)
        }
        if (Object.keys(this.selectedNode).length === 0) {
            return
        }
        this.initCharts()
    }
    // getQuarterVal(val) {
    //     const { startTime, endTime } = val
    //     this.formateParams = this.interfaceParams(startTime, endTime)
    //     this.initCharts()
    // }
    interfaceParams(startVal, endVal?): any {
        let startTime, endTime
        switch (this.dateType) {
            case 'year':
                startTime = `${startVal}-01-01 00:00:00`
                endTime = `${startVal}-12-31 23:59:59`;
                break;
            case 'month':
                let curMonthDays = new Date(new Date(startVal).getFullYear(), new Date(startVal).getMonth() + 1, 0).getDate()
                startTime = `${startVal}-01 00:00:00`
                endTime = `${startVal}-${curMonthDays} 23:59:59`
                break;

            case 'self':
                startTime = `${startVal} 00:00:00`
                endTime = `${endVal} 23:59:59`
                break;
            case 'quarter':
                 let year = startVal.split('-')[0]
                let quarter = startVal.split('-')[1]
                let { start, end } = this.getQuarterStartEnd(year, quarter)
                startTime = start
                endTime = end
                // startTime = `${startVal}-01-01 00:00:00`
                // endTime = `${startVal}-12-31 23:59:59`;
                break;
            default:
                break;
        }
        return {startTime,endTime}
    }
     getQuarterStartEnd(year, quarter) {
        let startTime;
        let endTime;
 
        switch (quarter) {
            case 'Q1':
                startTime = new Date(year, 0, 1, 0, 0, 0, 0);
                endTime = new Date(year, 2, 31, 23, 59, 59, 999);
                break;
            case 'Q2':
                startTime = new Date(year, 3, 1, 0, 0, 0, 0);
                endTime = new Date(year, 5, 30, 23, 59, 59, 999);
                break;
            case 'Q3':
                startTime = new Date(year, 6, 1, 0, 0, 0, 0);
                endTime = new Date(year, 8, 30, 23, 59, 59, 999);
                break;
            case 'Q4':
                startTime = new Date(year, 9, 1, 0, 0, 0, 0);
                endTime = new Date(year, 11, 31, 23, 59, 59, 999);
                break;
        }
        const formatDate = (date: Date) => {
            const year = date.getFullYear();
            const month = (date.getMonth() + 1).toString().padStart(2, '0');
            const day = date.getDate().toString().padStart(2, '0');
            const hours = date.getHours().toString().padStart(2, '0');
            const minutes = date.getMinutes().toString().padStart(2, '0');
            const seconds = date.getSeconds().toString().padStart(2, '0');
            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        };
 
        return {
            start: formatDate(startTime),
            end: formatDate(endTime)
        };
    }
    initCharts() {
        this.getPowerData()
        this.getPowerBarChart();
        this.getPowerTrendChart();
        this.getLoadRateChart();
    }

    getPowerData() {
        this.baseService.getAll(`computerrackspowerloadrate?rackId=${this.selectedNode?.key}`).subscribe(res => {
            if (res) {
                this.powerLoadRate = res.powerLoadRate
                this.ratedPower = res.ratedPower
                this.currentPower = res.currentActivePower
                this.initPowerLoadChart();
            }

        })
    }

    search() {
        if(this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        this.searchTimer = setTimeout(() => {
            this.searchText = this.inputSearchText;
        }, 1000)
    }

    getPowerBarChart() {
        let mapType = {
            'year': 1,
            'month': 2,
            // 'quarter': 3,
            'quarter': 5,
            'self': 4
        }
        let params = {
            rackId: this.selectedNode?.key,
            dataType: mapType[this.dateType],
            startTime: this.formateParams.startTime,
            endTime: this.formateParams.endTime
        }
        this.baseService.get(params, 'computerrackpower').subscribe(res => {
            if (res && res.length > 0) {
                let xAxisData = res.map(item => item.name)
                let sourceData = res.map(item => item.value)
                this.powerBarChart = this.initBarChart(xAxisData, sourceData)
            }
        })
    }

    getPowerTrendChart() {
        let mapType = {
            'year': 1,
            'month': 2,
            // 'quarter': 3,
            'quarter': 5,
            'self': 4
        }
        let params = {
            rackId: this.selectedNode?.key,
            dataType: mapType[this.dateType],
            startTime: this.formateParams.startTime,
            endTime: this.formateParams.endTime
        }
        this.baseService.get(params, 'computerrackpowertrend').subscribe(res => {
            if (res && res.length > 0) {
                const xAxisData = res.map(item => item.name)
                const seriesData = [{
                    type: 'line',
                    smooth: true,
                    data: res.map(item => item.value),
                    itemStyle: {
                        color: '#1890ff'
                    }
                }];
                this.powerTrendChart = this.initLine(xAxisData, seriesData,'机柜功率值')
            }
        })
    }
    getLoadRateChart() {
        let mapType = {
            'year': 1,
            'month': 2,
            // 'quarter': 3,
            'quarter': 5,
            'self': 4
        }
        let params = {
            rackId: this.selectedNode?.key,
            dataType: mapType[this.dateType],
            startTime: this.formateParams.startTime,
            endTime: this.formateParams.endTime
        }
        this.baseService.get(params, 'computerrackpowerloadrate').subscribe(res => {
            if (res && res.length > 0) {
                const xAxisData = res.map(item => item.name)
                const seriesData = [{
                    type: 'line',
                    smooth: true,
                    data: res.map(item => item.value),
                    itemStyle: {
                        color: '#1890ff'
                    },
                    areaStyle: {
                        opacity: 0.1,
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                            { offset: 1, color: 'rgba(24, 144, 255, 0)' }
                        ])
                    }
                }];
                this.loadRateChart = this.initLine( xAxisData,seriesData,'负载率值');
            }
        })

    }
    // 初始化电力负载率环形图
    initPowerLoadChart() {
        const option = {
            series: [{
                type: 'gauge',
                startAngle: 90,
                endAngle: -270,
                center: ['50%', '50%'],
                radius: '100%',
                pointer: {
                    show: false
                },
                progress: {
                    show: true,
                    overlap: false,
                    roundCap: true,
                    clip: false,
                    itemStyle: {
                        color: '#007bff'
                    }
                },
                axisLine: {
                    lineStyle: {
                        width: 10,
                        color: [
                            [this.powerLoadRate / 100, '#007fff'],
                            [1, '#0A3389']
                        ]
                    }
                },
                splitLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    show: false
                },
                data: [{
                    value: this.powerLoadRate,
                    detail: {
                        valueAnimation: true,
                        offsetCenter: ['0%', '0%'],
                    }
                }],
                detail: {
                    width: 50,
                    height: 18,
                    fontSize: 18,
                    fontWeight: 'bold',
                    formatter: '{value}%',
                    color: this.cssVariableService.getCssVariableValue('--portal-text-color'),
                    fontFamily: 'Arial'
                }
            }]
        };
        this.powerLoadChart = option
    }
    initBarChart( xAxisData: any, sourceData: any[]) { // 柱状图
        const option = {
            grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: 'transparent'
                    }
                },
                textStyle: {
                    color: '#bfdfff',
                    fontSize: 12
                },
                backgroundColor: 'rgba(4,17,78,0.5)',
                borderWidth: 1,
                borderColor: 'rgba(0,127,255,0.5)',
                borderRadius: 4,
                formatter: function (params) {
                    const xLabel = params[0].name;
                    const value = params[0].value!== undefined ? params[0].value : '';
                    const color = (typeof params[0].color === 'object' && params[0].color.colorStops)
                        ? params[0].color.colorStops[0].color  // 取第一个渐变色作为圆点色
                        : params[0].color;
                    return `
                  <div style=" color: '#bfdfff'">
                   <div>功率值</div>
                   <div style="display: flex; justify-content: space-between; align-items: center;margin-top:10px">
                    <span style="color:${color}; display:inline-block; margin-right:4px;font-size:14px">●</span> ${xLabel}</span>
                    <span style="font-weight: bold;margin-left:10px ">${value}</span>
                   </div>
                  </div>
               `;
                },
            },
            xAxis: {
                type: 'category',
                data: xAxisData,
                axisLabel: {
                    textStyle: {
                        color: this.cssVariableService.getCssVariableValue('--portal-text-color'),
                        fontSize: '12px',
                        fontFamily: 'Arial'
                    },
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: true,
                    lineStyle: { color: '#BFDFFF', width: '1', opacity: 0.2 }
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: true,
                    lineStyle: { color: '#BFDFFF', width: '1', opacity: 0.2 }
                },
                axisLabel: {
                    textStyle: {
                        color: this.cssVariableService.getCssVariableValue('--portal-text-color'),
                        fontSize: '12px',
                        fontFamily: 'Arial'
                    }
                },
                splitLine: { show: false }
            },
            series: [{
                data: sourceData,
                type: 'bar',
                barMaxWidth: 20,
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 0, 1,
                        [
                            { offset: 0, color: '#4D7AFF' },
                            { offset: 0.5, color: '#02BEFF' },
                            { offset: 1, color: '#9673FF' }
                        ]
                    )
                },
                emphasis: {
                    itemStyle: {
                        color: new echarts.graphic.LinearGradient(
                            0, 0, 0, 1,
                            [
                                { offset: 0, color: '#4D7AFF' },
                                { offset: 0.7, color: '#02BEFF' },
                                { offset: 1, color: '#9673FF' }
                            ]
                        )
                    }
                },
            }]
        };
        return option
    }
    initLine( xAxisData: any, sourceData: any,tooltipTitle:string) {
        const option:any = {
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow',
                    shadowStyle: {
                        color: 'transparent'
                    }
                },
                textStyle: {
                    color: '#bfdfff',
                    fontSize: 12
                },
                backgroundColor: 'rgba(4,17,78,0.5)',
                borderWidth: 1,
                borderColor: 'rgba(0,127,255,0.5)',
                borderRadius: 4,
                formatter: function (params) {
                    const xLabel = params[0].name;
                    const value = params[0].value !== undefined? params[0].value : '';
                    const color = (typeof params[0].color === 'object' && params[0].color.colorStops)
                        ? params[0].color.colorStops[0].color  // 取第一个渐变色作为圆点色
                        : params[0].color;
                    return `
                  <div style=" color: '#bfdfff'">
                   <div>${tooltipTitle}</div>
                   <div style="display: flex; justify-content: space-between; align-items: center;margin-top:10px">
                    <span style="color:${color}; display:inline-block; margin-right:4px;font-size:14px">●</span> ${xLabel}</span>
                    <span style="font-weight: bold;margin-left:10px ">${value}</span>
                   </div>
                  </div>
               `;
                },
            },
            legend: {
                bottom: '2%',
                textStyle: {
                    color: this.cssVariableService.getCssVariableValue('--portal-text-color'),
                    fontSize: '14px',
                },
            },
            grid: {
                left: '3%',
                right: '3%',
                bottom: '12%',
                top: '10%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: xAxisData,
                axisLabel: {
                    textStyle: {
                        color: this.cssVariableService.getCssVariableValue('--portal-text-color'),
                        fontSize: '12px',
                        fontFamily: 'Arial'
                    },
                    // interval: 0
                },
                axisTick: {
                    show: false
                },
                axisLine: {
                    show: true,
                    lineStyle: { color: '#BFDFFF', width: '1', opacity: 0.2 }
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    show: true,
                    lineStyle: { color: '#BFDFFF', width: '1', opacity: 0.2 }
                },
                axisLabel: {
                    textStyle: {
                        color: this.cssVariableService.getCssVariableValue('--portal-text-color'),
                        fontSize: '12px',
                        fontFamily: 'Arial'
                    },
                },
                splitLine: { show: false }
            },
            color: this.colors,
            series: sourceData
        };
        if (this.dateType !== 'quarter') {
            option.dataZoom = [
                {
                    type: 'inside',
                    xAxisIndex: 0,
                    filterMode: 'none'
                }
            ]
        } else {
           option.dataZoom =[
                {
                    type: 'inside',  // 开启鼠标滚轮缩放
                    start: 0,        // 默认起始位置
                    end: 100         // 默认结束位置
               },
               {
                   type: 'slider',
                   xAxisIndex: 0,
                   filterMode: 'filter',
                   height: 16,
                   bottom: 10,
                   start: 0,
                   end: 100,
                   handleStyle: {
                       color: '#007FFF',
                       borderWidth: 1,
                       borderColor: '#007FFF'
                   },
                   // 根据图片的颜色设置
                   dataBackground: {
                       lineStyle: {
                           color: '#253cA3',
                           width: 1
                       },
                       areaStyle: {
                           color: 'rgba(24, 144, 255, 0.2)'
                       }
                   },
                   selectedDataBackground: {
                       lineStyle: {
                           color: '#007FFF',
                           width: 1
                       },
                       areaStyle: {
                           color: 'transparent'
                       },
                      },
                    moveHandleStyle: {
                        color: 'rgba(24, 144, 255,0.5)',
                        borderColor: 'rgba(24, 144, 255,0.5)',
                        height: '6px',
                        opacity: 1
                        
                      },
                   backgroundColor: 'transparent',
                   fillerColor: 'rgba(24, 144, 255, 0.2)',
                   borderColor: '#253cA3',
                   textStyle: {
                       color: '#fff'
                   }
                  }
            ]
        }
       return option
    }
    
    changeDate(val) {
        switch (val) {
            case 'year':
                this.defaultRange = DateUtil.dateFormat(new Date(), 'YYYY');
                break;
            case 'month':
                this.defaultRange = DateUtil.dateFormat(new Date(), 'YYYY-MM');
                break;
            case 'self':
                this.defaultRange = `${DateUtil.dateFormat(new Date(), 'YYYY-MM-DD')}`
                break;
            case 'quarter':
                // this.defaultRange = DateUtil.dateFormat(new Date(), 'YYYY');
                this.defaultRange = `${DateUtil.dateFormat(new Date(), 'YYYY')}-Q1`
                break;
            default:
                this.defaultRange = DateUtil.dateFormat(new Date(), 'YYYY');
                break;
                
        }
        this.getRangeVal(null)
    }

    onEvent(event: any): void {
        if (event.node.origin.disabled)
            return 
        this.selectedNode = event.node.origin;
        this.initCharts()
    }
} 