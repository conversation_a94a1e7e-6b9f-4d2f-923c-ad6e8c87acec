import { PermissionGuard } from '@communal/guard/permission.guard';
import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/core';
import { AlarmConfigComponent } from './alarmconfig.component';
import { AlarmAutoConfirmComponent } from './alarm-auto-confirm/alarm-auto-confirm.component';
import { EventSeverityConfigComponent } from './event-severity-config/event-severity-config.component';
import { AlarmReasonConfigComponent } from './alarm-reason/alarm-reason.component';


export const routes: Routes = [
  {
    path: '',
    data: {
      title: 'featuremanage.alarmconfig.alarm'
    },
    canActivateChild: [PermissionGuard],
    component: AlarmConfigComponent,
    children: [
      {
        path: '',
        data: {
          title: ''
        },
        component: EventSeverityConfigComponent
      },
      {
        path: 'alarm-reason',
        data: {
          title: ''
        },
        component: AlarmReasonConfigComponent
      }
    ]
  }
];

export const routing: ModuleWithProviders<RouterModule> = RouterModule.forChild(routes);
