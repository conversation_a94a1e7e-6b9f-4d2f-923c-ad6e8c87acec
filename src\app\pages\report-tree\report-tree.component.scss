:host ::ng-deep {
    .nz_tree {
        padding: 0 16px;
        width: 100%;
        overflow-x: hidden;

        .cdk-virtual-scroll-viewport,
        .ant-tree.ant-tree-block-node .ant-tree-list-holder-inner .ant-tree-node-content-wrapper,
        .nz-tree-node-title {
            overflow-x: hidden;
        }

        .cdk-virtual-scroll-content-wrapper {
            overflow-x: hidden;
            width: 100%;
        }

        .ant-tree-node-content-wrapper {
            display: flex;
            align-items: center;
        }

        .tree-node-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            overflow: hidden;
        }

        .node-title {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .node-actions {
            margin-left: 8px;
        }
    }

    .tree-actions {
        position: sticky;
        bottom: 0;
        padding: 12px;
        background: var(--card-bg);
        border-top: 1px solid var(--border-color);
        display: flex;
        justify-content: center;
        z-index: 1;

        .btn {
            padding: 4px 12px;
        }
    }

    .report-tree-container {
        height: calc(100% - 32px);
        overflow: hidden;

        .ant-layout-sider {
            position: relative;

            .sider-collapse-btn {
                position: absolute;
                right: -12px;
                top: 130px;
                transform: translateY(-50%);
                z-index: 1;
                width: 14px;
                height: 48px;
                border-radius: 0 4px 4px 0;
                box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                background-color: var(--portal-active-color);
                opacity: 0.5;

                &:hover {
                    opacity: 1;
                }

                i {
                    font-size: 12px;
                    color: var(--text-color);
                    transition: transform 0.3s ease;

                    &.unfold {
                        transform: rotate(-90deg);
                    }

                    &.fold {
                        transform: rotate(90deg);
                    }
                }
            }
        }

        .d-flex.justify-content-between {
            position: relative;
            z-index: 2; // 提高按钮组的z-index
        }

    }

    .right_content {
        flex-grow: 1;
    }

}

.ant-layout-sider {
    background: transparent;
    border-right: 1px solid var(--border-color);
}

::ng-deep {
    .cdk-overlay-container {
        z-index: 1000000;
    }

    .ant-select-tree .ant-select-tree-node-content-wrapper.ant-select-tree-node-selected {
        background-color: var(--portal-select-option-active-bg-color) !important;
    }
}