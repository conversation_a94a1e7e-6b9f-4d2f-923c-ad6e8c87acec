import { Component, OnInit, ViewChild } from '@angular/core';
import { ConvergenceEvent } from '../alarm-fitting.model';
import { FiltrationEventQueryService } from '../services/filtration-event-query.service';
import {
	NgbDate,
	NgbDateParserFormatter,
	NgbCalendar,
} from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { Page } from '@app/communal/models/page.model';
import * as moment from 'moment';

@Component({
	selector: 'app-filtration-event-query',
	templateUrl: './filtration-event-query.component.html',
	styleUrls: ['./filtration-event-query.component.scss'],
})
export class FiltrationEventQueryComponent implements OnInit {
	@ViewChild('table') table: any;
	startTime: any;
	endTime: any;
	hoveredDate: NgbDate | null = null;
	page = new Page();
	rows = new Array<ConvergenceEvent>();
	messages: any = {};
	columns: any = [];
	searchText = '';

	options: any = {
		singleDatePicker: true,
		timePicker: true,
		timePicker24Hour: true,
		timePickerSeconds: true,
		autoUpdateInput: false,
		showDropdowns: true,
		locale: {
			format: 'YYYY-MM-DD HH:mm:ss',
		},
	};

	constructor(
		private service: FiltrationEventQueryService,
		private translateService: TranslateService,
		private calendar: NgbCalendar,
		public formatter: NgbDateParserFormatter
	) {
		this.startTime = calendar.getPrev(calendar.getToday(), 'd', 6);
		this.endTime = calendar.getToday();
		this.page.pageNumber = 0;
		this.page.size = 19;
	}

	ngOnInit() {
		this.setColumns();
		this.setPage({ offset: 0 });
		let lang = localStorage.getItem('lang');
		if (lang == 'zh') {
			this.options = DateUtil.getDatePickerOptions();
		} else {
			this.options = DateUtil.getTranslateDatePickerOptions(this.translateService);
		}
		this.startTime = moment().subtract(7, 'days').format('YYYY-MM-DD 00:00:00');
		this.endTime = DateUtil.dateFormat(new Date(), 'YYYY-MM-DD') + ' 00:00:00';
	}

	setColumns() {
		this.messages.emptyMessage = this.translateService.instant(
			'general.common.noData'
		);
		this.columns = [
			{
				name: this.translateService.instant(
					'alarmFitting.convergenceEvent.birthTime'
				),
				prop: 'birthTime',
			},
			{
				name: this.translateService.instant(
					'alarmFitting.convergenceEvent.endTime'
				),
				prop: 'endTime',
			},
			{
				name: this.translateService.instant(
					'alarmFitting.filtrationEventQuery.associatedDevice'
				),
				prop: 'equipmentName',
			},
			{
				name: this.translateService.instant(
					'alarmFitting.filtrationEventQuery.eventName'
				),
				prop: 'eventName',
			},
			{
				name: this.translateService.instant(
					'alarmFitting.filtrationEventQuery.eventDescription'
				),
				prop: 'meanings',
			},
			{
				name: this.translateService.instant(
					'alarmFitting.filtrationEventQuery.eventType'
				),
				prop: 'eventType',
			},
			// { name: this.translateService.instant(''), prop: 'baseTypeName' },
		];
	}

	selectTime(value: any, state: string) {
		const date = state === 'startTime' ? 'startDate' : 'endDate';
		const val = value.picker[date]['_d'];
		const time = DateUtil.dateToDayLongString(val);
		if (state === 'startTime') {
			this.startTime = time;
		} else {
			this.endTime = time;
		}
	}

	getTableSource(sortable?: { dir: string; prop: string }) {
		const startTime = DateUtil.formatNgbDate(this.startTime);
		const endTime = DateUtil.formatNgbDateToEndTime(this.endTime);
		this.service
			.getServerPagingData(
				startTime,
				endTime,
				this.searchText,
				this.page,
				sortable ? sortable : null
			)
			.subscribe((res) => {
				if (!res || !Array.isArray(res.content)) {
					return;
				}
				this.rows = res.content;
				this.page.pageNumber = res.number;
				this.page.totalElements = res.totalElements;
				this.page.totalPages = res.number;
			});
	}

	setPage(pageInfo) {
		this.page.pageNumber = pageInfo.offset;
		this.getTableSource();
	}

	onSort(event) {
		if (
			!event.sorts ||
			!Array.isArray(event.sorts) ||
			event.sorts.length === 0
		) {
			return;
		}
		const prop = event.sorts[0].prop;
		const dir = event.sorts[0].dir;
		this.getTableSource({ dir, prop });
	}
}
