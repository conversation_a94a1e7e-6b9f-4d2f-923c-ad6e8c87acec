<div class="auth-main">
  <span class="auth-block">
    <form novalidate class="phoenix-form" #ngForm="ngForm">
      <div class="form-group row forget-title">
        <div style="margin-bottom: 12px;">{{ 'general.login.register' | translate }}</div>
      </div>
      <div class="form-group row">
        <label for="inputpersonName" class="col-sm-2 form-control-label">{{
          'general.login.personName' | translate
          }}</label>
        <div class="col-sm-10">
          <input [(ngModel)]="account.personName" type="user" name="personName" class="form-control username"
            #personName="ngModel" [ngClass]="{'errors':personName.errors && (personName.touched || !ngFormValid)}"
            required placeholder="{{ 'general.login.register' | translate }}" />
        </div>
        <span class="col-sm-2 forget-placeholder"></span>
        <span *ngIf="personName.errors?.required && (personName.touched || submitted === false)">
          <span class="star"></span>
          <span class="error">{{langMap.get('admin.account.nameNotEmpty')}}</span>
        </span>
      </div>
      <div class="form-group row">
        <label for="inputUser" class="col-sm-2 form-control-label">{{
          'general.login.usrname' | translate
          }}</label>
        <div class="col-sm-10">
          <input [(ngModel)]="account.name" type="user" name="accountName" class="form-control username"
            #accountName="ngModel" [ngClass]="{'errors':accountName.errors && (accountName.touched || !ngFormValid)}"
            required placeholder="{{ 'general.login.usrname' | translate }}" />
        </div>
        <span class="col-sm-2 forget-placeholder"></span>
        <span *ngIf="accountName.errors?.required && (accountName.touched || submitted === false)">
          <span class="star"></span>
          <span class="error">{{langMap.get('admin.account.nameNotEmpty')}}</span>
        </span>
      </div>
      <div class="form-group row">
        <label for="accountPassword"
          class="col-sm-2 form-control-label">{{langMap.get('admin.account.password')}}</label>
        <div class="col-sm-10">
          <input name="account.password" type="password" class="form-control"
            [ngClass]="{'errors':accountPassword.errors && (accountPassword.touched || !ngFormValid)}" required
            minlength="8" maxlength="50" #accountPassword="ngModel" [(ngModel)]="account.password"
            placeholder="{{langMap.get('admin.account.pwdrequire')}}" (input)="NewcheckRegxp()">
        </div>
        <span class="col-sm-2 forget-placeholder"></span>
        <span>
          <div class="grade" *ngIf="grade" [ngClass]="{'low':grade === 1,'middle':grade === 2,'high':grade === 3}">
            <span>{{gradeText}}</span>
          </div>
          <span *ngIf="accountPassword.errors?.required && (accountPassword.touched || submitted === false)">
            <span class="star"></span>
            <span class="error">{{langMap.get('admin.account.passwordNotEmpty')}}</span>
          </span>
          <span *ngIf="accountPassword.errors?.minlength && accountPassword.touched">
            <span class="star"></span>
            <span class="error">{{langMap.get('admin.account.passwordNotLessThan')}}</span>
          </span>
        </span>
      </div>
      <div class="form-group row">
        <label for="inputPhone" class="col-sm-2 form-control-label">{{
          'general.login.telephone' | translate
          }}</label>
        <div class="col-sm-10">
          <input [(ngModel)]="account.phone" type="text" name="accountPhone" class="form-control username"
            #accountPhone="ngModel" [ngClass]="{'errors':accountPhone.errors && (accountPhone.touched || !ngFormValid)}"
            required pattern="^1[0-9]{10}$" placeholder="{{ 'general.login.telephone' | translate }}"
             />
        </div>
        <span class="col-sm-2 forget-placeholder"></span>
        <span *ngIf="accountPhone.errors?.required && (accountPhone.touched || submitted === false)">
          <span class="star"></span>
          <span class="error">{{langMap.get('admin.account.telephoneNotEmpty')}}</span>
        </span>
        <span *ngIf="accountPhone.errors?.pattern && accountPhone.touched">
          <span class="star"></span>
          <span class="error">{{langMap.get('admin.account.phoneFormatError')}}</span>
        </span>
        <span *ngIf="phoneValidationMessage" class="phone-validation-message">
          <span class="star"></span>
          <span [ngClass]="{'error': phoneValidationMessage.type === 'error', 'success': phoneValidationMessage.type === 'success'}">
            {{phoneValidationMessage.message}}
          </span>
        </span>
      </div>
      <div class="form-group row">
        <label for="inputdescribe" class="col-sm-2 form-control-label">{{
          'general.login.describe' | translate
          }}</label>
        <div class="col-sm-10">
          <input [(ngModel)]="account.describe" type="text" name="accountDescribe" class="form-control username"
            #accountDescribe="ngModel" [ngClass]="{'errors':accountDescribe.errors && (accountDescribe.touched || !ngFormValid)}"
            required placeholder="{{ 'general.login.registerDescribe' | translate }}" />
        </div>
        <span class="col-sm-2 forget-placeholder"></span>
        <span *ngIf="accountDescribe.errors?.required && (accountDescribe.touched || submitted === false)">
          <span class="star"></span>
          <span class="error">{{langMap.get('admin.account.describeNotEmpty')}}</span>
        </span>
      </div>

      <!-- 附件上传功能 -->
      <div class="form-group row">
        <label for="attachmentUpload" class="col-sm-2 form-control-label">{{
          'general.login.attachment' | translate
          }}</label>
        <div class="col-sm-10">
          <div class="attachment-upload-container">
            <input type="file" #fileInput (change)="onFileSelected($event)"
                   accept=".jpg,.jpeg,.png,.gif,.doc,.docx,.xls,.xlsx"
                   style="display: none;" multiple />
            <div class="input-group">
              <input type="text" class="form-control" readonly
                     [value]="getSelectedFilesText()"
                     placeholder="{{ 'general.login.attachmentPlaceholder' | translate }}" />
              <span class="input-group-btn">
                <button type="button" class="btn btn-primary" (click)="fileInput.click()">
                  {{ 'general.common.select' | translate }}
                </button>
                <button type="button" class="btn btn-primary" (click)="fileInputclick()">
                  {{ 'general.common.select' | translate }}
                </button>
              </span>
            </div>
            <div class="file-list" *ngIf="selectedFiles.length > 0">
              <div class="file-item" *ngFor="let file of selectedFiles; let i = index">
                <span class="file-name">{{file.name}}</span>
                <span class="file-size">({{formatFileSize(file.size)}})</span>
                <button type="button" class="btn btn-sm btn-danger" (click)="removeFile(i)">
                  ✕
                </button>
              </div>
            </div>
            <div class="file-validation-info">
              <small class="text-muted">
                {{ 'general.login.attachmentInfo' | translate }}
              </small>
            </div>
            <!-- 上传状态显示 -->
            <div *ngIf="isUploading" class="upload-status">
              <div class="d-flex align-items-center">
                <div class="spinner-border spinner-border-sm text-primary mr-2" role="status">
                  <span class="sr-only">上传中...</span>
                </div>
                <span class="text-primary">正在上传文件，请稍候...</span>
              </div>
            </div>
          </div>
        </div>
        <span class="col-sm-2 forget-placeholder"></span>
        <span *ngIf="fileValidationMessage" class="file-validation-message">
          <span class="star"></span>
          <span class="error">{{fileValidationMessage}}</span>
        </span>
      </div>

      <div class="modal-footer">
        <div class="offset-sm-2">
          <button type="button" class="btn btn-primary" debounceDirective
            (debounceClick)="submit(this.submitted = true && ngForm.valid)"
            [disabled]="!( !accountPassword.errors ) || isUploading">
            <span *ngIf="isUploading" class="spinner-border spinner-border-sm mr-1" role="status"></span>
            {{isUploading ? '上传中...' : this.langMap.get('general.common.ok')}}
          </button>
        </div>
        <div style="padding-left: 8px;">
          <button type="button" class="btn btn-secondary"
            (click)="this.router.navigateByUrl('/signin')">{{'general.common.cancel' | translate}}</button>
        </div>
      </div>
    </form>
  </span>
</div>