import { Component, OnInit, ElementRef, ViewChild, Input, AfterViewInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BaseCRUDService } from '@communal/providers/baseCRUDService/baseCRUDService';
import { HttpClient } from '@angular/common/http';
import { ToastService } from '@communal/providers/toast.service';
import { TranslateService } from '@ngx-translate/core';
import { SessionService } from '@core/services/session.service';
import * as _ from 'lodash';
import { AlarmListService } from '@app/pages/monitor/alarm-list/alarm-list.service';
import { AlarmCommonCommentComponent } from '../alarm-commoncomment';
import { GlobalState } from '@app/global.state';
import { Router } from '@angular/router';
import { AuthenticationService } from '@app/core/services/authentication.service';

@Component({
  selector: 'app-global-alarm-window',
  templateUrl: './global-alarm-window.component.html',
  styleUrls: ['./global-alarm-window.scss']
})
export class GlobalAlarmWindowComponent extends BaseCRUDService implements OnInit, AfterViewInit {
  theme: string;
  isBackColorAlarm = false;
  noPop = false;
  @Input('noPop')
  set start(value: boolean) {
    this.noPop = value;
    if (this.noPop) {
      this.noPoplabel = this.translateService.instant('general.alarm.PopInThisLogin');
      this.popEm?.nativeElement.classList.remove('icon-cancle');
      this.popEm?.nativeElement.classList.add('icon-gonggao');
    } else {
      this.noPoplabel = this.translateService.instant('general.alarm.noPopInThisLogin');
      this.popEm?.nativeElement.classList.remove('icon-gonggao');
      this.popEm?.nativeElement.classList.add('icon-cancle');
    }
  }
  allSelectedItems = [];
  levels = [];
  showLevelColors = [];
  levelNames: any = [];
  scrollTop: number;
  adjStartTime: any;
  level = '';
  lockName = this.translateService.instant('general.alarm.lock');
  lockTimer: any;
  lockSpan = 30000;
  isPageLocked = false;
  alive = true;
  intervalId: any;
  noPoplabel: any;
  refreshSpan = 3000;
  refreshStandardSpan = 3000;
  subscribeOfAlarm: any;
  showButton = false;
  hasFilter = false;
  pageable = {
    last: false,
    number: 0,
    size: 50
  };
  allSelected = false;
  items: any = [];
  tempItems: any = [];
  currentItems: any = [];
  filteredItems: any = [];
  searchText = '';
  @ViewChild('tableCasePop') tableCase: ElementRef;
  @ViewChild('lockEm') lockEm: ElementRef;
  @ViewChild('popEm', { static: true }) popEm: ElementRef;
  @Input() pressedTime = 100;           // 鼠标按压时间
  isPressed = false;   // 鼠标是否按下的标记
  posX = 0;            // 悬浮球的x轴位置
  posY = 0;            // 悬浮球的y轴位置
  lastMousePos = {    // 记录鼠标按下时的坐标
    x: 0,
    y: 0
  };
  mouseOffsetX = 0;    // 鼠标X偏移量
  mouseOffsetY = 0;    // 鼠标X偏移量
  elementOffsetX = 0;  // 悬浮球容器的X偏移量
  elementOffsetY = 0;  // 悬浮球容器的Y偏移量
  url = 'unconfirmactiveeventdtos';
  cronFlag = false;
  private timer: any;
  currentCursorStyle = 'default';
  private cursorStyle = { default: 'default', moved: 'move' };
  isByteDance = false;
  alarmReason: any = [];
  constructor(private http: HttpClient, private translateService: TranslateService,
    private ngbModal: NgbModal,
    private _state: GlobalState,
    private router: Router,
    private authService: AuthenticationService,
    private service: AlarmListService,
    private toastService: ToastService,
    public sessionService: SessionService) {
    super(http, 'unconfirmactiveeventdtos');

  }
  ngOnDestroy(): void {
    this.clearTimer();
    if (this.subscribeOfAlarm) {
      this.subscribeOfAlarm.unsubscribe();
    }
    clearTimeout(this.lockTimer);
    this.alive = false;
  }
  ngAfterViewInit() {
    // return;
    const rootNode = document.getElementById('my-batch-alaram-window');  // 获取容器元素
    const myAlarmBatchHeader = document.getElementById('my-alarm-batch-header');  // 获取拖动的容器header元素
    // const viewWidth = window.innerWidth;    // 获取窗口宽度
    // const viewHeight = window.innerHeight;  // 获取窗口宽度
    rootNode.addEventListener('mousedown', (event) => {
      if (event.target !== myAlarmBatchHeader) {
        return;
      }
      this.timer = setTimeout(() => {
        this.isPressed = true; // 确认鼠标按下
        this.openMovedCursor(); // 打开可移动光标
      }, this.pressedTime);
      this.lastMousePos.x = event.clientX; // 记录鼠标当前的x坐标
      this.lastMousePos.y = event.clientY; // 记录鼠标当前的y坐标
      this.elementOffsetX = rootNode.offsetLeft; // 记录容器元素当时的左偏移量
      this.elementOffsetY = rootNode.offsetTop; // 记录容器元素的上偏移量
      event.preventDefault();                   // 取消其他事件
    }, false);
    // 此处必须挂载在document上，否则会发生鼠标移动过快停止
    document.addEventListener('mousemove', (event) => {
      if (this.isPressed) {// 如果是鼠标按下则继续执行
        this.mouseOffsetX = event.clientX - this.lastMousePos.x; // 记录在鼠标x轴移动的数据
        this.mouseOffsetY = event.clientY - this.lastMousePos.y; // 记录在鼠标y轴移动的数据
        this.posX = this.elementOffsetX + this.mouseOffsetX; // 容器在x轴的偏移量加上鼠标在x轴移动的距离
        this.posY = this.elementOffsetY + this.mouseOffsetY; // 容器在y轴的偏移量加上鼠标在y轴移动的距离
        rootNode.classList.remove('batch-alarm-window-hierarchy');
        rootNode.style.left = this.posX + 'px';
        rootNode.style.top = this.posY + 'px';
      }
    }, true);
    window.addEventListener('resize', () => {
      // rootNode.classList.remove('batch-alarm-window-hierarchy');
      // if (this.posX > window.innerWidth - 1800) {
      //   this.posX = window.innerWidth - 1912;
      //   rootNode.style.left = this.posX + 'px';
      // }
      // if (this.posX < - 1800) {
      //   this.posX = window.innerWidth - 1912;
      //   rootNode.style.left = this.posX + 'px';
      // }
      // if (this.posY > window.innerHeight - 380) {
      //   this.posY = window.innerHeight - 435;
      //   rootNode.style.top = this.posY + 'px';
      // }
      // if (this.posY < 0) {
      //   this.posY = window.innerHeight - 435;
      //   rootNode.style.top = this.posY + 'px';
      // }
    }, false);
    // 鼠标释放时候的函数
    document.addEventListener('mouseup', () => {
      this.isPressed = false;
      this.closeMovedCursor();
      clearTimeout(this.timer);  // 释放定时器
    }, false);
    rootNode.addEventListener('touchmove', (event) => {
      event.preventDefault(); // 阻止其他事件
      if (event.targetTouches.length === 1) {
        const touch = event.targetTouches[0]; // 把元素放在手指所在的位置
        this.posX = touch.pageX; // 存储x坐标
        this.posY = touch.pageY; // 存储Y坐标
        rootNode.style.left = this.posX + 'px';
        rootNode.style.top = this.posY + 'px';
      }
    });
  }
  openMovedCursor(): void {
    if (this.currentCursorStyle === this.cursorStyle.moved) {
      return;
    }
    this.currentCursorStyle = this.cursorStyle.moved;
  }
  closeMovedCursor(): void {
    if (this.currentCursorStyle === this.cursorStyle.default) {
      return;
    }
    this.currentCursorStyle = this.cursorStyle.default;
  }
  lockPage() {
    if (this.isPageLocked === false) {
      this.lockEm.nativeElement.classList.remove('icon-suoding');
      this.lockEm.nativeElement.classList.add('icon-jiesuo');
      this.isPageLocked = true;
      this.lockName = this.translateService.instant('general.alarm.unlock');
      this.lockTimer = setTimeout(() => {
        this.clearLockPage();
      }, this.lockSpan);
    } else {
      this.clearLockPage();
    }
  }
  clearLockPage() {
    this.isPageLocked = false;
    this.lockEm.nativeElement.classList.remove('icon-jiesuo');
    this.lockEm.nativeElement.classList.add('icon-suoding');
    this.lockName = this.translateService.instant('general.alarm.lock');
    clearTimeout(this.lockTimer);
    this.items = this.tempItems;
    if (!this.allSelected) {
      this.setSelected();
    } else {
      this.items.forEach(element => {
        element.selected = this.allSelected;
      });
    }
    this.commonFilterItems();
    this.scrollTop = -1;
    this.tableCase.nativeElement.scrollTop = 0;
  }
  ngOnInit() {
    this.authService.getAuthenticationJson().subscribe(resJson => {
      this.isByteDance = resJson['bytedace.web.enable'];
    });
    this.theme = localStorage.getItem('theme');
    if (this.sessionService.getSysconfigByKey('alarmlist.backcolor.severityshow') &&
      this.sessionService.getSysconfigByKey('alarmlist.backcolor.severityshow').systemConfigValue === 'true') {
      this.isBackColorAlarm = true;
    }
    if (this.sessionService.getSysconfigByKey('end.alarm.pop.trigger.cron') &&
      this.sessionService.getSysconfigByKey('end.alarm.pop.trigger.cron').systemConfigValue) {
      this.url = 'unendactiveeventdtos';
      this.cronFlag = true;
    }
    // this.levelNames = this.sessionService.getAlarmSeverity();
    this.authService.get('', 'coreeventseverities').subscribe(
      res => {
        res.forEach(s => {
          this.showLevelColors[s.eventLevel] = s.displayColor;
        })
        this.levelNames = res;
        this.levels = [{ value: '', label: this.translateService.instant('notifyManagement.notifyServer.all') }];
        const configdLevels: string = this.sessionService.getBatchAlarmLevel();
        this.levelNames.forEach(s => {
          if (configdLevels.indexOf(s.eventLevel.toString()) !== -1 || configdLevels === '') {
            this.levels.push({ value: s.eventLevel.toString(), label: s.severityName });
          }
        });
        this.refreshData();
      });

    this.service.getAlarmReasonList().subscribe(res => {
      this.alarmReason = res;
    })
  }
  getAPageData(pageNo, allItems) {
    let isLast = false;
    if (this.pageable.size * (pageNo + 1) >= allItems.length) {
      isLast = true;
    }
    this.setPageble(
      {
        last: isLast,
        number: pageNo
      },
      this.pageable
    );
    const start = pageNo * this.pageable.size;
    const end =
      allItems.length >= start + this.pageable.size
        ? start + this.pageable.size
        : allItems.length;
    const newItems = [];
    for (let i = start; i < end; i++) {
      allItems[i].levelName = this.levelNames.find(s => s.eventLevel.toString() === allItems[i].eventLevel.toString()).severityName;
      newItems.push(allItems[i]);
    }
    newItems.forEach(newitem => {
      this.currentItems.push(newitem);
    });
  }
  setPageble(from, to) {
    to.last = from.last;
    to.number = from.number;
    // to.size = from.size;
  }
  refreshData() {
    if (this.alive) {
      this.clearTimer();
      this.getData();
    }
  }
  clearTimer() {
    if (this.intervalId) {
      clearTimeout(this.intervalId);
    }
  }
  getData() {
    this.subscribeOfAlarm = this.getAll(this.url).subscribe((result) => {
      let sortedResult = [];
      if (result)
        sortedResult = result.sort((x, y) => Date.parse(y.startTime) - Date.parse(x.startTime));
      this.tempItems = sortedResult;
      if (!this.isPageLocked) {
        this.items = sortedResult;
        if (!this.allSelected) {
          this.setSelected();
        } else {
          this.items.forEach(element => {
            element.selected = this.allSelected;
          });
        }
        this.commonFilterItems();
        this.scrollTop = -1;
        this.tableCase.nativeElement.scrollTop = 0;
      }
      this.adjStartTime = new Date().getTime();
      this.intervalId = setTimeout(() => {
        const offset = new Date().getTime() - (this.adjStartTime + this.refreshStandardSpan);
        if (offset > 0) {
          this.refreshSpan = 1000; // 如果超过刷新间隔，应该立刻取，但为了服务器压力，还是设置为1秒再取
        } else {
          this.refreshSpan = this.refreshStandardSpan - offset;
        }
        this.refreshData();
      }, this.refreshSpan);
    });
  }
  Changelevel(newValue) {
    if (this.level !== newValue) {
      this.level = newValue;
      this.commonFilterItems();
    }
  }
  commonFilterItems() {
    this.filteredItems = this.items.filter(item => {
      if (this.searchText && this.searchText !== '') {
        this.hasFilter = true;
      }
      return (
        (item.equipmentName.toLowerCase().indexOf(this.searchText.toLowerCase()) > -1 ||
          item.equipmentPosition.toLowerCase().indexOf(this.searchText.toLowerCase()) > -1 ||
          item.eventName.toLowerCase().indexOf(this.searchText.toLowerCase()) > -1 ||
          item.eventLevel.toString().indexOf(this.searchText.toLowerCase()) > -1 ||
          item.startTime.toLowerCase().indexOf(this.searchText.toLowerCase()) > -1 ||
          (item.endTime && item.endTime.toLowerCase().indexOf(this.searchText.toLowerCase()) > -1)) &&
        item.eventLevel.toString().indexOf(this.level.toLowerCase()) > -1
      );
    });
    this.setPageble(
      {
        last: this.filteredItems.length <= this.pageable.size,
        number: 0
      },
      this.pageable
    );
    this.currentItems = [];
    this.getAPageData(0, this.filteredItems);
  }
  scrolledChanged(el) {
    if (el.scrollTop > this.scrollTop) {
      if (this.pageable.last) {
        return;
      }
      this.pageable.number++;
      let allItems = this.items;
      if (this.hasFilter) {
        allItems = this.filteredItems;
      }
      this.getAPageData(this.pageable.number, allItems);
    }
    this.scrollTop = el.scrollTop;
  }
  changeAllSelected() {
    this.allSelected = !this.allSelected;
    this.filteredItems.forEach(element => {
      element.selected = this.allSelected;
    });
    this.showButton = this.allSelected;
    this.allSelectedItems = [];
  }
  changeSelected(e, item) {
    if (e.currentTarget.checked) {
      if (!this.allSelectedItems.find(o => o.sequenceId === item.sequenceId)) {
        this.allSelectedItems.push(item);
      }
      this.showButton = true;
      return;
    } else {
      const theIndex = this.allSelectedItems.findIndex(s => s.sequenceId === item.sequenceId);
      if (theIndex !== -1) {
        this.allSelectedItems.splice(theIndex, 1);
      }
    }
    setTimeout(() => {
      for (let i = 0; i < this.filteredItems.length; i++) {
        if (this.filteredItems[i].selected) {
          this.showButton = true;
          return;
        }
      }
      this.showButton = false;
    }, 0);
  }
  setSelected() {
    _.map(this.items, i => {
      i.selected = this.allSelectedItems.findIndex(s => s.sequenceId === i.sequenceId) !== -1;
    });
  }
  confirm(isall: boolean) {
    if (isall) {
      this.service.selectedItems = this.items;
      if (this.isByteDance) {
        let canList = this.items.filter(s => this.sessionService.hasPermission(130 + s.eventLevel))
        if (canList.length == 0) {
          this.toastService.showToast('error', this.translateService.instant('general.alarm.hasConfirmed'), '', { timeOut: 3000 });
          return;
        }
        if (this.items.length != canList.length) {
          this.toastService.showToast('warning', this.translateService.instant('general.alarm.hasConfirmed'), '', { timeOut: 3000 });
          this.service.selectedItems = canList;
        }
      }
    } else {
      const selItems = this.filteredItems.filter(element => element.selected);
      this.service.selectedItems = selItems;
      if (this.isByteDance) {
        let canList = selItems.filter(s => this.sessionService.hasPermission(130 + s.eventLevel))
        if (canList.length == 0) {
          this.toastService.showToast('error', this.translateService.instant('general.alarm.hasConfirmed'), '', { timeOut: 3000 });
          return;
        }
        if (selItems.length != canList.length) {
          this.toastService.showToast('warning', this.translateService.instant('general.alarm.hasConfirmed'), '', { timeOut: 3000 });
          this.service.selectedItems = canList;
        }
      }
    }

    const modal = this.ngbModal.open(AlarmCommonCommentComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.opType = 'confirm';
    modal.componentInstance.reasonList = this.alarmReason;
    modal.componentInstance.isBatch = true;
    modal.componentInstance.theTitle = this.translateService.instant('general.common.confirm');

    modal.result.then(res => {
      if (res && res.ok) {
        this.closeOrg(false);
      }
    });
  }
  closeOrg(noPop, toAlarm = false) {
    this._state.notifyDataChanged('batchAlarm.hidden', { toAlarm: toAlarm, noPop: noPop });
  }
  changeNoPop() {
    this.noPop = !this.noPop;
    this._state.notifyDataChanged('batchAlarm.hidden', { toAlarm: false, noPop: this.noPop });
  }
  gotoDetail(item) {
    if (!item) {
      return;
    }
    this.router.navigate(['/entry/alarms/detail/'], {
      queryParams: {
        resourceStructureId: item.resourceStructureId,
        sequenceId: item.sequenceId,
      }
    });
  }
}
