import { Component, OnInit, ViewChild, Template<PERSON>ef, On<PERSON><PERSON>roy } from '@angular/core';
import { ConvergenceEventService } from '../services/convergence-event.service';
import { EchartsOptionsService } from '../services/echarts-options.service';
import { SessionService } from '@app/core/services/session.service';
import { ToastService } from '@app/communal/providers';
import {
	NgbDate,
	NgbDateParserFormatter,
	NgbCalendar,
	NgbModal,
} from '@ng-bootstrap/ng-bootstrap';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { EventTopoModalComponent } from './event-topo-modal/event-topo-modal.component';
import { Page } from '@app/communal/models/page.model';
import { TranslateService } from '@ngx-translate/core';
import { startWith, switchMap, takeWhile } from 'rxjs/operators';
import { interval } from 'rxjs';
import { ExportExcelFileService } from '@app/communal/providers/exportExcelFile.service';
import { values } from 'lodash';
import * as XLSX from 'xlsx';
import * as moment from 'moment';

@Component({
	selector: 'app-convergence-event',
	templateUrl: './convergence-event.component.html',
	styleUrls: ['./convergence-event.component.scss'],
})
export class ConvergenceEventComponent
	implements OnInit, OnDestroy {
	@ViewChild('table') table: any;
	@ViewChild('eventName', { static: true }) eventNameRef: TemplateRef<any>;
	@ViewChild('status', { static: true }) statusRef: TemplateRef<any>;
	@ViewChild('impactRange', { static: true }) impactRangeRef: TemplateRef<any>;
	@ViewChild('export', { static: true }) exportRef: TemplateRef<any>;

	DEFAULT_PAGE_SIZE = 7;
	TIMEOUT = 15000;

	// ngx-datatable
	page = new Page();
	rows = [];
	messages = {};
	columns = [];
	selected = [];
	eventList: any[] = [];
	eventTitle = '';
	langKeysPre = 'alarmFitting.convergenceEvent';
	eventTitleSuffix = 'alarmFitting.convergenceEvent.eventTimeLine';
	intervalId: any;

	startTime: any;
	endTime: any;
	hoveredDate: NgbDate | null = null;

	statistics: { [key: string]: number };
	alarmLevelList: any[] = [];
	selectedConvergenceEventId = null;
	selectedConvergenceEventIdList = [];
	chartOption: any;	// 点阵
	pieChartOption: any;	// 统计饼图
	// 状态翻译
	statusMap = new Map([
		[1, 'UnDealWith'],
		[2, 'hasDealWith'],
	]);
	// 实时状态
	alive = true;

	options: any = {
		singleDatePicker: true,
		timePicker: true,
		timePicker24Hour: true,
		timePickerSeconds: true,
		autoUpdateInput: false,
		showDropdowns: true,
		locale: {
			format: 'YYYY-MM-DD HH:mm:ss',
		},
	};


	constructor(
		private service: ConvergenceEventService,
		private optionsService: EchartsOptionsService,
		private excelExportService: ExportExcelFileService,
		private translateService: TranslateService,
		private toastService: ToastService,
		private sessionService: SessionService,
		private calendar: NgbCalendar,
		private modal: NgbModal,
		public formatter: NgbDateParserFormatter,
	) {
		this.startTime = moment().subtract(7, 'days').format('YYYY-MM-DD 00:00:00');
		this.endTime = DateUtil.dateFormat(new Date(), 'YYYY-MM-DD') + ' 00:00:00';
		this.page.pageNumber = 0;
		this.page.size = this.DEFAULT_PAGE_SIZE;
	}

	ngOnInit(): void {
		this.initTable();
		this.initAlarmLevel();
		let lang = localStorage.getItem('lang');
		if (lang == 'zh') {
			this.options = DateUtil.getDatePickerOptions();
		} else {
			this.options = DateUtil.getTranslateDatePickerOptions(this.translateService);
		}
		// this.openTopo(516002064);
	}

	ngOnDestroy(): void {
		if (this.alive) {
			this.alive = false;
		}
		if (this.intervalId) {
			this.intervalId.unsubscribe();
		}
	}

	/**
	 * 获取告警等级
	 */
	initAlarmLevel(): void {
		this.service.getAll('coreeventseverities').subscribe((res) => {
			if (res && Array.isArray(res)) {
				this.alarmLevelList = res;
			}
		});
	}

	/**
	 * 收敛事件分析表格
	 */
	initTable(): void {
		this.setColumns();
		this.setPage({ offset: 0 });
	}

	/**
	 * 设置列
	 */
	setColumns(): void {
		this.messages = this.translateService.instant('general.common.noData');

		this.columns = [
			{ minWidth: 56, maxWidth: 56, sortable: false, resizeable: false, headerCheckboxable: true, checkboxable: true },
			{ maxWidth: 100, name: this.translateService.instant(`${this.langKeysPre}.num`), prop: 'id' },
			{ maxWidth: 200, name: this.translateService.instant(`${this.langKeysPre}.birthTime`), prop: 'birthTime' },
			{ name: this.translateService.instant(`${this.langKeysPre}.rootCauses`), prop: 'possibleCauses' },
			{ name: this.translateService.instant(`${this.langKeysPre}.convergenceEvent`), prop: 'eventName', cellTemplate: this.eventNameRef },
			{ maxWidth: 200, name: this.translateService.instant(`${this.langKeysPre}.cenvergenceNums`), prop: 'convergenceCount' },
			{ maxWidth: 200, name: this.translateService.instant(`${this.langKeysPre}.liveCounts`), prop: 'liveCount' },
			{ maxWidth: 120, name: this.translateService.instant(`general.common.status`), prop: 'status', cellTemplate: this.statusRef },
			{ maxWidth: 200, name: this.translateService.instant(`${this.langKeysPre}.impactRangeAnalysis`), prop: 'convergenceType', cellTemplate: this.impactRangeRef },
			{ maxWidth: 60, name: this.translateService.instant(`general.common.export`), prop: 'id', cellTemplate: this.exportRef },
		];
	}

	setPage(pageInfo: { offset: any; }): void {
		this.page.pageNumber = pageInfo.offset;
		this.getTableSource();
	}

	selectTime(value: any, state: string) {
		const date = state === 'startTime' ? 'startDate' : 'endDate';
		const val = value.picker[date]['_d'];
		const time = DateUtil.shortDateToString(val);
		if (state === 'startTime') {
			this.startTime = time;
		} else {
			this.endTime = time;
		}
	}

	/**
	 * 后端分页获取数据
	 * @param sortable 排序方式
	 */
	getTableSource(sortable?: { dir: string; prop: string }): void {
		const timeStr = '23:59:59';
		const startTime = DateUtil.formatNgbDate(this.startTime);
		const endTime = DateUtil.formatNgbDateToEndTime(this.endTime) + ` ${timeStr}`;;
		if (this.intervalId) {
			this.intervalId.unsubscribe();
		}
		this.intervalId = interval(this.TIMEOUT).pipe(takeWhile(() => this.alive)).pipe(startWith(0), switchMap(() => this.service.getConvergenceEvent(startTime, endTime, this.page))).subscribe((res: any) => {
			if (!res || !Array.isArray(res.content)) {
				return;
			}
			this.rows = res.content;
			this.page.pageNumber = res.number;
			this.page.totalElements = res.totalElements;
			this.page.totalPages = res.totalPages;
		}, () => {
			this.alive = false;
		})
	}

	displayCheck(row): boolean {
		return true;
	}

	/**
	 * 设置唯一key，checkbox 选中记录
	 * @param row 
	 * @returns 
	 */
	setRowIdentity(row): number {
		return row.id;
	}

	onActivate(event): void {
		if (event.type === 'click' && !event.column.checkboxable) {
			this.initEventList(event.row);
		}
	}

	onSelect({ selected }): void {
		this.selected.splice(0, this.selected.length);
		this.selected.push(...selected);
		this.selectedConvergenceEventIdList = this.selected;
	}

	/**
	 * 时间段查询
	 */
	searchData(): void {
		if (!this.startTime || !this.endTime) {
			return;
		}
		this.getTableSource();
	}

	updateSelectedList(event?: any): void {
		this.selectedConvergenceEventIdList = event.selected;
	}

	exportExcel(row: any): void {
		const event = window.event;
		event.stopPropagation();
		const id = row.id;
		let resultList = [];
		this.service
			.getEventTimeListById(id).subscribe((res: any) => {
				if (res && Array.isArray(res.eventList)) {
					resultList = res.eventList;
					const fileName = row.possibleCauses || '--';
					this.writeExcel(resultList, fileName);
				}
			});
	}

	/**
	 * 封装 excel 数据
	 * @param data 数据源
	 * @returns excel数据
	 */
	getExcelData(data: any[]) {
		console.log(data)
		const xlsTemplateJson = [];
		const dict = {
			baseTypeName: this.translateService.instant('alarmFitting.convergenceEvent.eventName'),
			equipmentName: this.translateService.instant('alarmFitting.convergenceEvent.equipmentName'),
			deviceCategoryName: this.translateService.instant('alarmFitting.convergenceEvent.equipmentType'),
			birthTime: this.translateService.instant('alarmFitting.convergenceEvent.birthTime'),
			endTime: this.translateService.instant('alarmFitting.convergenceEvent.endTime'),
			occurRemark: this.translateService.instant('alarmFitting.convergenceEvent.occurRemark')
		};
		if (data && data.length) {
			const title = [
				this.translateService.instant('alarmFitting.convergenceEvent.eventName'),
				this.translateService.instant('alarmFitting.convergenceEvent.equipmentName'),
				this.translateService.instant('alarmFitting.convergenceEvent.equipmentType'),
				this.translateService.instant('alarmFitting.convergenceEvent.birthTime'),
				this.translateService.instant('alarmFitting.convergenceEvent.endTime'),
				this.translateService.instant('alarmFitting.convergenceEvent.occurRemark')];
			xlsTemplateJson.push(title);
			data.forEach(item => {
				const obj = {};
				Object.keys(dict).forEach(key => {
					obj[key] = item[key];
				});
				xlsTemplateJson.push(values(obj));
			});
		}
		return xlsTemplateJson;
	}

	/**
	 * 下载 excel 文件
	 * @param data 数据源
	 * @param name 文件名
	 * @returns excel 文件
	 */
	writeExcel(data: any[], name: string): void {
		if (!data) {
			return;
		}
		const fileName = name + '.xlsx';
		const xlsTemplateJson = this.getExcelData(data);
		const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson);
		const cols = [];
		xlsTemplateJson[0].forEach(() => {
			cols.push({ wch: 20 });
		})
		ws["!cols"] = cols;
		const wb: XLSX.WorkBook = XLSX.utils.book_new();
		XLSX.utils.book_append_sheet(wb, ws, name);
		XLSX.writeFile(wb, fileName);
	}

	/**
	 * 点击，初始化事件列表
	 */
	initEventList(event?: any): void {
		if (!event || !event.id) {
			return;
		}
		this.eventTitle = `[${event.possibleCauses || '--'}] ${this.translateService.instant(this.eventTitleSuffix)}`;
		this.selectedConvergenceEventId = event.id;
		if (!this.selectedConvergenceEventId) {
			this.eventList = [];
			this.setEchartOptions();
			return;
		}
		this.service
			.getEventTimeListById(this.selectedConvergenceEventId)
			.subscribe((res: any) => {
				if (!res || !Array.isArray(res.eventList)) {
					return;
				}
				this.eventList = res.eventList || [];
				this.statistics = res.statistics;
				this.setEchartOptions();
				this.buildPieChartOptions();
			});
	}

	/**
	 * 告警等级点阵配置
	 */
	buildChartOptions(): void {
		this.chartOption = {
			xAxis: { type: 'time' },
			yAxis: { type: 'category' },
			series: [],
		};
	}

	buildPieChartOptions(): void {
		let options = {
			xAxis: { show: false },
			yAxis: { show: false },
			legend: {
				bottom: '2%',
				left: 'center'
			},
			tooltip: {
				trigger: 'item'
			},
			series: []
		};
		options = this.optionsService.initOptions(options);
		if (this.statistics) {
			const data = [];
			for (const [key, value] of Object.entries(this.statistics)) {
				data.push({
					name: key,
					value: value
				});
			}
			options.series.push(
				this.optionsService.setSeries('pie', data, {
					avoidLabelOberlap: true,
					itemStyle: {
						borderRadius: 10,
						borderColor: 'transparent',
						borderWidth: 2
					},
					label: {
						formatter: '{b}: {c}',
						color: this.optionsService.styleObj.textColor
					}
				})
			)
		};
		this.pieChartOption = options;
	}

	/**
	 * 批量确认
	 */
	batchConfirm(): void {
		if (
			!this.selectedConvergenceEventIdList ||
			!Array.isArray(this.selectedConvergenceEventIdList) ||
			this.selectedConvergenceEventIdList.length === 0
		) {
			this.toastService.showWarningToast({
				msg: this.translateService.instant(
					'alarmFitting.convergenceEvent.selectedEventToConfirm'
				),
				position: this.toastService.position.top_right,
			});
			return;
		}
		const userId = this.sessionService.getPersonId();
		const userName = this.sessionService.getUserName();
		const eventIds = this.selectedConvergenceEventIdList.map((item) => item.id);
		this.service
			.batchConfirmEvents({
				userId,
				userName,
				eventIds,
			})
			.subscribe(() => {
				this.toastService.showSuccessToast({
					msg: this.translateService.instant(
						'alarmFitting.convergenceEvent.batchConfrimEventSuccess'
					),
					position: this.toastService.position.top_right,
				});
			});
	}

	/**
	 * 打开影响范围拓扑图
	 */
	openTopo(row): void {
		window.event.stopPropagation();
		// window.event.preventDefault();
		let id = row.equipmentId;
		if (!id) {
			return;
		}
		this.service.getDeviceNodeRelations(id).subscribe((res) => {
			if (!res || !Array.isArray(res.equipmentNodes) || !Array.isArray(res.connectionList) || !res.equipmentNodes.length || !res.connectionList.length) {
				return;
			}
			const modal = this.modal.open(EventTopoModalComponent, {
				backdrop: 'static',
				size: 'lg',
			});
			modal.componentInstance.relations = res.connectionList;
			modal.componentInstance.nodes = res.equipmentNodes;
			modal.componentInstance.rootId = id;
		});
	}

	setEchartOptions(): void {
		let options: any = {
			legend: {
				show: true,
				top: '0%'
			},
			xAxis: {
				type: 'time',
				splitLine: {
					show: false,
				},
				nameLocation: 'end',
				axisLabel: {
					interval: 0,
					rotate: '45'
				},
			},
			yAxis: {
				type: 'value',
				inverse: true,
				interval: 1,
				min: 1,
				axisLabel: {
					formatter: (value) => {
						const level = this.alarmLevelList.find(
							(level) => level.eventSeverityId === value
						);
						if (level) {
							return level.severityName.slice(0, 2);
						}
						return value;
					},
				},
			},
			tooltip: {
				type: 'item',
				enterable: true,
				confine: true,
				formatter: (params) => {
					return this.formatterTooltip(params);
				},
			},
			dataZoom: {
				type: 'slider',
			},
			visualMap: [
				{
					show: false,
					categories: [],
				},
			],
			series: [],
		};
		if (this.alarmLevelList && this.alarmLevelList.length !== 0) {
			options.yAxis['data'] = this.alarmLevelList
				.map((item) => item.severityName)
				.reverse();
			options.visualMap[0].categories = options.yAxis['data'];
			options.visualMap[0]['inRange'] = {
				symbolSize: 10,
				color: this.alarmLevelList.map((item) => item.displayColor).reverse(),
			};
			options.visualMap[0]['dimension'] = 2;
		}

		if (this.eventList && this.eventList.length !== 0) {
			// 格式化数据
			const data = this.eventList.map((item) => {
				return [
					item.birthTime,
					item.severityId,
					item.severityName,
					item.convergenceCount,
				];
			});
			options = this.optionsService.initOptions(options);
			options.dataZoom = this.optionsService.setDataZoom();
			options['series'].push(this.optionsService.setSeries('scatter', data));
		} else {
			options['series'] = [];
		}
		this.chartOption = options;
	}

	/**
	 * 格式化 Tooltip 显示样式
	 * @param params series data
	 */
	formatterTooltip(params: any): void | string {
		if (!this.eventList || !Array.isArray(this.eventList)) {
			return;
		}
		const showList = this.eventList.filter(
			(item) =>
				item.birthTime === params.data[0] && item.severityId === params.data[1]
		);
		let text = '';
		if (Array.isArray(showList)) {
			showList.forEach((item) => {
				const lineText = `<span>${item.equipmentName}${item.eventName}: ${item.occurRemark}</span><br/>`;
				text += lineText;
			});
		}
		return '<div class="convergence-tooltip-content">' + text + '</div>';
	}

	// validateInput(currentValue: NgbDate | null, input: string): NgbDate | null {
	// 	const parsed = this.formatter.parse(input);
	// 	return parsed && this.calendar.isValid(NgbDate.from(parsed))
	// 		? NgbDate.from(parsed)
	// 		: currentValue;
	// }

	formatDate(str: string, index: number, symbolMark: string): string {
		return str.slice(0, index) + symbolMark + str.substr(index);
	}
}
