import {forkJoin,  Observable } from 'rxjs';
import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FindReportCompService } from '../service/find-report-comp.service';
import { Location } from '@angular/common';
import { ReportModel } from '../model/report.model';
import { SessionService } from '@app/core/services/session.service';
import { DateUtil } from '@utils/DateUtil';
import { ReportParameterPreset } from '../model/reportParameterPreset.model';
import * as _ from 'lodash';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import { HttpClient } from '@angular/common/http';
import { IOption } from 'ng-select';
import { ReportTemplateModel } from '../model/reportTemplate.model';
import { ToastService } from '@app/communal/providers';
import { TranslateService } from '@ngx-translate/core';
import { UntypedFormGroup, AbstractControl, UntypedFormBuilder, Validators } from '@angular/forms';
import { TreeSelectorComponent } from '@app/communal/components/tree-selector/tree-selector.component';
import { TreeEventSelectorComponent } from '@app/communal/components/tree-event-selector/tree-event-selector.component';
import { IndicatorSelectorComponent } from '@app/communal/components/indicator-selector/indicator-selector.component';
import { ReportExportParameterPreset } from '../model/reportExportParameterPreset.model';
import { RackPointSelector } from '@app/communal/components/rack-point-selector/rack-point-selector.component';
import { NewRackSelectorComponent } from '@app/communal/components/new-rack-selector/new-rack-selector.component';
import { DevicePointSelectorComponent } from '@app/communal/components/device-point-selector/device-point-selector.component';
import { TreeEquipmentSelectorComponent } from '@app/communal/components/tree-equipment-selector/tree-equipment-selector.component';
import { BasicSignalSelectorComponent } from '@app/communal/components/basic-signal-selector/basic-signal-selector.component';
import { TreeAirSelectorComponent } from '@app/communal/components/tree-aircondition-selector/tree-aircondition-selector.component'
import { HierarchySelectorComponent } from '@app/communal/components/hierarchy-selector/hierarchy-selector.component';
import { isArray } from 'jquery';
import { EquipmentCategorySelectorComponent } from '@app/communal/components/equipment-category-selector/equipment-category-selector.component';

@Component({
    selector: 'report-create',
    templateUrl: 'report-create.component.html',
    styleUrls: ['report-create.component.scss']
})

export class ReportCreateComponent extends BaseCRUDService implements OnInit {
    // 查询参数parameterControlId与控件对应关系
    // 1 -- 开始时间日期选择控件  2 -- 结束时间日期选择控件  3 -- 下拉选择控件  4 -- 设备选择控件  5-- 测点选择控件

    report: ReportModel = new ReportModel();
    reportTempData: ReportTemplateModel = new ReportTemplateModel();
    options: any;
    langKeys: Array<string> = [
        'ngForm.create.success',
        'ngForm.create.error'
    ];
    langValues: any;
    form: UntypedFormGroup;
    startTimeControl: AbstractControl;
    endTimeControl: AbstractControl;
    selectOptionDataObj: any;
    backendSelectorOption: any;
    selectRequestOriginList: Array<any>;
    reportName: string;
    reportDescription: string;
    selectDeviceList: Array<any> = [];
    selectEqCategoryList: Array<any> = [];
    selectAirGroupList:Array<any> = []
    selectAirStationList:Array<any> = [];
    selectCorePointList: Array<any> = [];
    selectIndicatorList: Array<any> = [];
    selectRackPointList: Array<any> = [];
    selectRackList: Array<any> = [];
    selectEventList : Array<any> = [];
    selectBasicTypeList: Array<any> = [];
    ngFormValid = true;
    parameterId: number;
    formControls: any = {};
    selectedData: any = {};

    reportOpen = true;
    //监听报表15的搜索模式来切换显示内容
    showHierarchy: boolean = false;
    showEquipment: boolean = false;
    showEquipmentCategory: boolean = false;
    eqcIDs: any;

    regex = /^(?!'|\u2018|\u2019)(^((?!\\|\/|:|\?|\*|\[|\]).){1,30}$)(?<!'|\u2018|\u2019)$/;

    constructor(private router: Router,
        private activatedRoute: ActivatedRoute,
        private modal: NgbModal,
        private reportService: FindReportCompService,
        private sessionService: SessionService,
        private location: Location,
        private http: HttpClient,
        private toast: ToastService,
        private translate: TranslateService,
        private formBuilder: UntypedFormBuilder) {
        super(http, 'reports')
    }

    ngOnInit() {
        this.initTranslate();
        this.initData();
    }

    initTranslate() {
        this.translate.get(this.langKeys).subscribe(values => {
            this.langValues = values;
        })
    }

    initControlOptions(timeList) {
        const obj = {};
        timeList.forEach((item) => {
            Object.assign(obj, {[item['reportSchemaQueryParameterName']]: ['', Validators.compose([Validators.required])]});
        });
        this.form = this.formBuilder.group(obj);
        timeList.forEach((item) => {
            this.formControls[item['reportSchemaQueryParameterName']] = this.form.controls[item['reportSchemaQueryParameterName']];
        });
        let lang = localStorage.getItem('lang');
        if(lang == 'zh') {
            this.options = DateUtil.getDatePickerOptions();
        } else {
            this.options = DateUtil.getTranslateDatePickerOptions(this.translate);
        }
    }

    initData() {
        this.reportTempData = _.cloneDeep(this.reportService.getReportTempData());
        this.reportTempData.reportSchemaQueryParameterList.forEach(item => {
            let name = 'reportSchema.queryParameter.param' + item['reportSchemaQueryParameterId'];
            item['reportSchemaQueryParameterTitle'] = this.translate.instant(name);
        })
        if(this.reportTempData.reportSchemaExportParameterList.length > 0){
            this.reportTempData.reportSchemaExportParameterList.forEach(item => {
                let name = 'reportSchema.exportParameter.param' + item['reportSchemaExportParameterId'];
                item['reportSchemaExportParameterTitle'] = this.translate.instant(name);
            })
        }
        const timeList = this.reportTempData.reportSchemaQueryParameterList.filter(obj => obj.parameterControlId === '1' || obj.parameterControlId === '2');
        if (timeList.length > 0) {this.initControlOptions(timeList);};
        this.report = this.initReportData(this.reportTempData);
        this.getSelectRequestList(this.reportTempData);
    }

    // 获取下拉数据
    getSelectRequestList(data) {
        if (data && data.reportSchemaQueryParameterList) {
            const selectWidgetData = {},
                arr = [];
            const selectWidgetList = data.reportSchemaQueryParameterList.filter(item => +item.parameterControlId === 3);
            if (selectWidgetList && selectWidgetList.length > 0) {
                selectWidgetList.forEach(param => {
                    const obj = {};
                    obj['multipleBool'] = false;
                    obj['value'] = param.value && param.value.split(',');
                    const dataSourceExpression = param.dataSourceExpression,
                        labelValue = JSON.parse(param.dataSourceReturnTableName);
                    const pairs: Array<string> = dataSourceExpression.split(";");
                    let dataSource: string;
                    pairs.map((item: string) => {
                        if (item === "type=multiple") {
                            obj['multipleBool'] = true;
                        } else {
                            dataSource = item;
                        }
                    })
                    const index = dataSource.indexOf('=');
                    const sourceStr = dataSource.split('=');
                    obj['sourceType'] = sourceStr[0] || '';
                    if(sourceStr[0] === 'json'){
                        let name = 'reportSchema.selectOption.option' + param.reportSchemaQueryParameterId;
                        let translateStr = this.translate.instant(name);
                        sourceStr[1] = translateStr.replaceAll("\'","\"");
                    }else {
                        sourceStr[1] = dataSource.slice(index + 1);
                    }
                    obj['labelKey'] = labelValue['display'] || '';
                    obj['valueKey'] = labelValue['value'] || '';
                    obj['dataSource'] = obj['sourceType'] === 'json' ? this.parseSelectOptionData(JSON.parse(sourceStr[1]), obj['valueKey'], obj['labelKey']) : sourceStr[1];
                    obj['name'] = param.reportSchemaQueryParameterName;
                    if(obj['name'] === "timeGranularity"){
                        if(this.reportTempData.reportSchemaId === 3 || this.reportTempData.reportSchemaId === 28){
                            obj['value'] = "1h-start";
                        }else {
                            obj['value'] = "1h";
                        }
                    }
                    selectWidgetData[param.reportSchemaQueryParameterName] = obj;
                    arr.push(obj);
                })
                this.selectRequestOriginList = arr.filter(item => item.sourceType === 'api');
                if (this.selectRequestOriginList && this.selectRequestOriginList.length > 0) {
                    const requestList = this.selectRequestOriginList.map(item => this.getAll(item.dataSource));
                    this.requestSelectData(requestList, selectWidgetData);
                } else {
                    this.selectOptionDataObj = selectWidgetData;
                }
            }
            const selectorBeta = data.reportSchemaQueryParameterList.filter(item => +item.parameterControlId === 18);
            if (selectorBeta && selectorBeta.length > 0) {
                this.http.get('dataitems?entryId=7').subscribe(res => {
                    let list = [];
                    if(res && isArray(res) && res.length > 0) {
                        res.forEach(item => {
                            const obj = {
                                label: item.itemValue,
                                value: item.itemId
                            }
                            list.push(obj);
                        })
                    }
                    this.backendSelectorOption = list;
                })
            }
            const selectDevice = data.reportParameterPresetList.filter(item => +item.reportSchemaQueryParameter.parameterControlId === 4);
            if (selectDevice && selectDevice.length > 0) {
                this.selectDeviceList = selectDevice[0].value ? JSON.parse(selectDevice[0].value) : [];
            }
        }
    }

    updateEqc(item) {
        if(item.value.length > 0) {
            this.eqcIDs = item.value.join(',');
        } else {
            this.eqcIDs = '';
        }
    }

    checkType(item) {
        if(this.reportTempData.reportSchemaId !== 15) return;
        if(item.value === '1') {
            this.showHierarchy = true;
            this.showEquipment = false;
            this.showEquipmentCategory = false;
        }
        if(item.value === '2') {
            this.showHierarchy = false;
            this.showEquipment = true;
            this.showEquipmentCategory = false;
        }
        if(item.value === '3') {
            this.showHierarchy = false;
            this.showEquipment = false;
            this.showEquipmentCategory = true;
        }
        if(!item.value) {
            this.showHierarchy = false;
            this.showEquipment = false;
            this.showEquipmentCategory = false;
        }
    }

    requestSelectData(list: any[], data) {
        forkJoin(list).subscribe(res => {
            res.forEach((item, index) => {
                this.selectRequestOriginList[index].dataSource = item;
            })
            this.setSelectOptionDataList(this.selectRequestOriginList, data);
        })
    }

    setSelectOptionDataList(list: any[], data) {
        for (const item in data) {
            const findData = list.find(s => s.name === data[item].name);
            if (findData) {
                data[item].dataSource = findData['dataSource'];
                data[item].dataSource = this.parseSelectOptionData(data[item].dataSource, data[item].valueKey, data[item].labelKey)
            }
        }
        this.selectOptionDataObj = data;
    }


    parseSelectOptionData(data, valueStr: string, lableStr: string) {
        const sources = Array<IOption>();
        if (data) {
            data.forEach(element => {
                const sourceItem: IOption = {
                    value: String(element[valueStr]),
                    label: element[lableStr]
                };
                sources.push(sourceItem);
            });
        }
        return sources;
    }

    initReportData(tempData: ReportTemplateModel) {
        const data: ReportModel = new ReportModel();
        if (tempData) {
            data.reportSchemaId = tempData['reportSchemaId'];
            data.reportSchemaCategoryId = tempData['reportSchemaCategoryId'];
            data.dataSourceId = tempData['dataSourceId'];
            data.updateUserId = this.sessionService.getUserId();
            data.updateTime = DateUtil.currentDateToString();
            if (tempData.reportSchemaQueryParameterList && tempData.reportSchemaQueryParameterList.length > 0) {
                const paramList = [];
                tempData.reportSchemaQueryParameterList.forEach(item => {
                    //初始化开始结束时间
                    let today = ''
                    if(item['parameterControlId'] == '1') {
                        today = DateUtil.currentShotDateToString();
                        today = today.concat(' 00:00:00');
                    }
                    if(item['parameterControlId'] == '2') {
                        today = DateUtil.currentShotDateToString();
                        today = today.concat(' 23:59:59');
                    }
                    item['value'] = today;
                    item['display'] = true;
                    const obj = new ReportParameterPreset();
                    obj.reportSchemaQueryParameterId = item['reportSchemaQueryParameterId'];
                    obj.value = '';
                    paramList.push(obj);
                })
                data.reportParameterPresetList = paramList;
            }
            if (tempData.reportSchemaExportParameterList && tempData.reportSchemaExportParameterList.length > 0) {
                const exportParamList = [];
                tempData.reportSchemaExportParameterList.forEach(item => {
                    item['display'] = true;
                    const obj = new ReportExportParameterPreset();
                    obj.reportSchemaExportParameterId = item['reportSchemaExportParameterId'];
                    exportParamList.push(obj);
                })
                data.reportExportParameterPresetList = exportParamList;
            }
        }
        return data;
    }

    setTime(value, param) {
        param.value = DateUtil.dateToString(value.picker.startDate['_d']);

        if(this.reportTempData.maxQueryInterval !== null && this.reportTempData.maxQueryInterval !== undefined){
            let startDate = new Date(this.reportTempData.reportSchemaQueryParameterList[0].value);
            let endDate = new Date(this.reportTempData.reportSchemaQueryParameterList[1].value);
            let diffHour = (endDate.getTime() - startDate.getTime())/1000/60/60;

            if(!Number.isNaN(diffHour)){
                if(this.reportTempData.maxQueryInterval < Math.abs(diffHour)){
                    this.toast.showToast('error', this.translate.instant('ngForm.create.timeIntervalError') + '(' + this.reportTempData.maxQueryInterval + this.translate.instant('ngForm.create.hour') + ')', null);
                    param.value = '';
                }
            }
        }
    }

    clearTime(param) {
        param.value = null;
    }

    getCurrentParamVal(paramName: string) {
        let value = null;
        if (this.selectOptionDataObj) {
            const param = this.selectOptionDataObj[paramName];
            value = param && param.value;
        }
        return value;
    }

    selectEquipment(param) {
        let modal;
        if (param.dataSourceExpression && param.dataSourceExpression.indexOf('tree') > -1) {
            modal = this.modal.open(DevicePointSelectorComponent, { backdrop: 'static' });
        } else {
            modal = this.modal.open(TreeEquipmentSelectorComponent, { backdrop: 'static' });
            modal.componentInstance.multipleBool = param.dataSourceExpression && param.dataSourceExpression.indexOf('multiple') > -1 ? true : false;
            if(this.report.reportSchemaId == 3 || this.report.reportSchemaId == 39) {
                modal.componentInstance.baseSignalTypes = this.selectBasicTypeList;
            }
            const deviceCategoryId = this.getCurrentParamVal('baseEquipmentIds');
            if (deviceCategoryId) {
                modal.componentInstance.filterDeviceCategoryId = +deviceCategoryId;
            }
        }
        modal.componentInstance.selectDeviceList = this.selectDeviceList;

        modal.result.then((res) => {
            if (res) {
                this.selectDeviceList = res;
                const list = [];
                this.selectDeviceList.forEach(item => {
                    const obj = {
                        eqId: item.eqId,
                        eqName: item.eqName,
                        roomName: item.roomName,
                        position: item.position,
                    };
                    list.push(obj);
                })
                param.value = res.length > 0 ? JSON.stringify(list) : null;
            }
        });
    }

    clearDeviceList(param) {
        this.selectDeviceList = [];
        param.value = null;
    }
    selectEquipmentCategory(param) {
        const modal = this.modal.open(EquipmentCategorySelectorComponent, { backdrop: 'static' });
        modal.componentInstance.multipleBool = true;
        modal.componentInstance.equipmentCategories = this.selectEqCategoryList && this.selectEqCategoryList
        modal.result.then((res) => {
            if (res) {
                this.selectEqCategoryList = res;
                param.value = res && res.length > 0 ? JSON.stringify(res) : null;
            }
        })
    }
    clearEquipmentCategoryList(param) {
        this.selectEqCategoryList = [];
        param.value = null;
    }
    selectGroup(param,parameterControlId) {
        let parameterId = Number(parameterControlId)
        parameterId === 15 && this.setSelectGroup(param,'selectAirGroupList',parameterId,'communalComponent.pointSelector.selectDevice','virtualEquipmentId','groupName')
        parameterId === 16 && this.setSelectGroup(param,'selectAirStationList',parameterId,'communalComponent.pointSelector.selectStation','stationId','stationName')
    }
    setSelectGroup(param,selectListName,parameterControlId,title,chosedChildId,chosedChildName) { // 群控分组控件
        let  modal = this.modal.open(TreeAirSelectorComponent, { backdrop: 'static' });
        modal.componentInstance.airSelectorTitle = title
        modal.componentInstance.chosedChildId = chosedChildId
        modal.componentInstance.chosedChildName = chosedChildName
        modal.componentInstance.multipleBool = param.dataSourceExpression && param.dataSourceExpression.indexOf('multiple') > -1 ? true : false;
        modal.componentInstance.parameterControlId = parameterControlId
        modal.componentInstance.selectAirGroupList = this[selectListName];
        modal.componentInstance.saveParentNodeIdsNames = parameterControlId === 15 ?'saveDeviceList': 'saveStationList'
        // console.log(this[selectListName],'回显的');
        modal.result.then((res) => {
            if (res) {
                this[selectListName] = res;
                const list = [];
                this[selectListName].forEach(item => {
                    const obj = {
                        nodeId:item.nodeId,
                        nodeName:item.nodeName,
                        position: item.position,
                        positionId:item.positionId
                    };
                    obj[chosedChildId] = item[chosedChildId]
                    obj[chosedChildName] = item[chosedChildName]
                    if(parameterControlId === 15) {
                        obj['nodeIdStr'] = item.nodeIdStr
                    }
                    list.push(obj);
                })
                param.value = res.length > 0 ? JSON.stringify(list) : null;
            }
        });
    }
    clearGroupList(param,parameterControlId) {
        Number(parameterControlId) === 15 ?this.selectAirGroupList = []:this.selectAirStationList = []
        param.value = null
    }
    selectPoint(param) {
        let modal;
        if (param.dataSourceExpression && param.dataSourceExpression.indexOf('tree') > -1) {
            modal = this.modal.open(DevicePointSelectorComponent, { backdrop: 'static' });
        } else {
            modal = this.modal.open(TreeSelectorComponent, { backdrop: 'static' });
            if (param.dataSourceExpression && param.dataSourceExpression.length > 0) {
                const strList = param.dataSourceExpression.split('&');
                strList.forEach(str => {
                    if (str.indexOf('standardId') > -1) {
                        const standardId = str.split('=')[1];
                        modal.componentInstance.standardId = standardId;
                    }
                })
                modal.componentInstance.expandDeviceBol = param.dataSourceExpression.indexOf('expand') > -1 ? false : true;
            }
        }
        modal.componentInstance.selectCorepointList = this.selectCorePointList;

        modal.result.then(data => {
            if (data) {
                this.selectCorePointList = data;
                const corePointList = [];
                data.forEach(item => {
                    const obj = {
                        signalId: item.signalId,
                        signalName: item.signalName,
                        equipmentName: item.equipmentName,
                        position: item.position,
                        positionId: item.positionId,
                        unit: item.unit,
                        equipmentId: item.equipmentId,
                        eqId: item.equipmentId,
                    };
                    corePointList.push(obj);
                })
                param.value = data.length > 0 ? JSON.stringify(corePointList) : null;
            }
        })
    }


    clearCorePointList(param) {
        param.value = null;
        this.selectCorePointList = [];
    }

    selectEvent(param) {
        let modal;
            modal = this.modal.open(TreeEventSelectorComponent, { backdrop: 'static' });
            if (param.dataSourceExpression && param.dataSourceExpression.length > 0) {
                const strList = param.dataSourceExpression.split('&');
                strList.forEach(str => {
                    if (str.indexOf('standardId') > -1) {
                        const standardId = str.split('=')[1];
                        modal.componentInstance.standardId = standardId;
                    }
                })
            }
        modal.componentInstance.selectEventList = this.selectEventList;
        modal.result.then(data => {
            if (data) {
                this.selectEventList = data;
                const eventList = [];
                data.forEach(item => {
                    const obj = {
                        eventId: item.eventId,
                        eventIds: item.eventIds,
                        eventName: item.eventName,
                        eqId: item.equipmentId,
                        eqName: item.equipmentName,
                        equipmentId: item.equipmentId,
                        equipmentName: item.equipmentName,
                        title: item.title,
                        position: item.position,
                        positionId: item.positionId,
                    };
                    eventList.push(obj);
                })
                param.value = data.length > 0 ? JSON.stringify(eventList) : null;
            }
        })
    }

    clearEventList(param) {
        param.value = null;
        this.selectEventList = [];
    }

    selectIndicator(param) {
        const modal = this.modal.open(IndicatorSelectorComponent, { backdrop: 'static' });
        modal.componentInstance.selectedIndicatorList = this.selectIndicatorList;
        modal.result.then(data => {
            if (data) {
                this.selectIndicatorList = data;
                // const list = [];
                // data.forEach(item => {
                //     const obj = {
                //         complexIndexId: item.complexIndexId,
                //         complexIndexName: item.complexIndexName,
                //         position: item.position,
                //         unit: item.unit
                //     };
                //     list.push(obj);
                // })
                param.value = data.length > 0 ? JSON.stringify(data) : null;
            }
        })
    }

    clearIndicatorList(param) {
        param.value = null;
        this.selectIndicatorList = [];
    }

    selelctRackPoint(param) {
        const modal = this.modal.open(RackPointSelector, { backdrop: 'static' });
        modal.componentInstance.selectedPointList = this.selectRackPointList;
        modal.componentInstance.selectCloumnCabinetBol = param.dataSourceExpression && param.dataSourceExpression.indexOf('columnCabinet') > -1 ? true : false;
        modal.result.then(data => {
            if (data) {
                this.selectRackPointList = data;
                const list = [];
                data.forEach(item => {
                    const obj = {
                        corePointId: item.corePointId,
                        pointName: item.pointName
                    };
                    list.push(obj);
                })
                param.value = data.length > 0 ? JSON.stringify(list) : null;
            }
        })
    }

    clearRackPointList(param) {
        param.value = null;
        this.selectRackPointList = [];
    }

    selectRack(param) {
        const modal = this.modal.open(NewRackSelectorComponent, { backdrop: 'static' });
        modal.componentInstance.selectedList = this.selectRackList;
        modal.result.then(data => {
            if (data) {
                this.selectRackList = data;
                param.value = data.length > 0 ? JSON.stringify(data) : null;
            }
        })
    }

    clearRackList(param) {
        param.value = null;
        this.selectRackList = [];
    }

    selectBasicSignal(param){
        const modal = this.modal.open(BasicSignalSelectorComponent, { backdrop: 'static' });
        let baseEquipmentIds;
        if (this.selectDeviceList.length) {
            const ids = this.selectDeviceList.flatMap(({ eqId }) => eqId !== null ? [eqId] : []).join(',');
            this.http.get(`equipmentbasetypes?equipmentIds=${ids}`).subscribe(
                (res: any) => {
                    baseEquipmentIds = res.flatMap(({ baseEquipmentId }) => baseEquipmentId !== null ? [baseEquipmentId] : []);
                    modal.componentInstance.filterBaseEquipmentList = [...new Set(baseEquipmentIds)];
                });
        }
        if (param.value) {
            this.selectBasicTypeList = JSON.parse(param.value);
        }
        modal.componentInstance.selectedList = this.selectBasicTypeList;
        modal.componentInstance.isReport = true;
        modal.componentInstance.multipleBool = true;
        modal.result.then(data => {
            if (data) {
                this.selectBasicTypeList = data;
                param.value = data.length > 0 ? JSON.stringify(data) : null;
            }
        })
    }

    clearBasicSignal(param){
        param.value = null;
        this.selectBasicTypeList = [];
    }

    selectHierarchy(param) {
        this.selectedData = param.value;
        const modal = this.modal.open(HierarchySelectorComponent, { backdrop: 'static' });
        modal.componentInstance.multipleBool = true;
        modal.componentInstance.objectId = this.selectedData && this.selectedData['objectId'] || [];
        modal.componentInstance.objectType = this.selectedData && this.selectedData['objectType'] || [];
        modal.componentInstance.objectPosition = this.selectedData && this.selectedData['objectPosition'] || [];
        modal.componentInstance.objectName = this.selectedData && this.selectedData['objectName'] || [];

        modal.result.then((res: {
            objectId: any[],
            objectType: number[],
            objectPosition: any[],
            objectName: any[],
            objectResult: any
        }) => {
            if (res) {
                // console.log(res)
                this.selectedData = res;
                param.value = res && res['objectName'].length > 0 ? JSON.stringify(res) : null;
            }
        })
    }

    clearHierarchy(param){
        param.value = null;
        this.selectedData = {};
    }

    getFileObj(param): any {
        let fileObj: any = null;
        if (param.value) {
            const url = param.value.split('/');
            fileObj = {};
            fileObj['filePath'] = url[0];
            fileObj['fileName'] = url[1];
        }
        return fileObj;
    }

    getFilePath(file, param) {
        if (!file) return;
        param.value = file.data['filePath'] + '/' + file.data['fileName'];
    }

    clearFile(param) {
        param.value = null;
    }

    returnPrevious() {
        this.location.back();
    }

    isValidOfQueryParam(): boolean {
        let valid = true;
        for (const item of this.reportTempData.reportSchemaQueryParameterList) {
            if ((!item.isNull) && (!item.value) && (!item.display)) {
                valid = false;
                break;
            }
        }
        return valid;
    }

    submit(formValid) {
        this.ngFormValid = formValid;
        if (!this.ngFormValid || !this.isValidOfQueryParam()) {
            return;
        }
        if(!this.regex.test(this.report.reportName)){
            this.toast.showToast('error', this.translate.instant('ngForm.create.illegalName'), null)
            return;
        }
        this.report['reportDataSourceId'] = this.reportTempData.reportDataSourceId;
        this.report['maxQueryInterval'] = this.reportTempData.maxQueryInterval;
        this.reportTempData.reportSchemaQueryParameterList.forEach((item)=>{
            // 数据传输前的格式化
            if (item.parameterControlId === '3') {
                const value = this.selectOptionDataObj && this.selectOptionDataObj[item.reportSchemaQueryParameterName] && this.selectOptionDataObj[item.reportSchemaQueryParameterName].value ;
                if (Array.isArray(value)) {
                    item.value = value.join(',');
                } else {
                    item.value = value;
                }
            }
        })
        this.report.reportParameterPresetList.forEach(item => {
            const data = this.reportTempData.reportSchemaQueryParameterList.find(i => i['reportSchemaQueryParameterId'] === item['reportSchemaQueryParameterId']);
            if (data) {
                item.value = data.value;
                item.display = data.display;
            }
        })
        if (this.report.reportExportParameterPresetList) {
            this.report.reportExportParameterPresetList.forEach(item => {
                const data = this.reportTempData.reportSchemaExportParameterList.find(i => i['reportSchemaExportParameterId'] === item['reportSchemaExportParameterId']);
                if (data) {
                    item.display = data.display;
                }
            })
        }

        const id = this.sessionService.getUserId();
        this.report.createUserId = id;
        if(!this.reportOpen) {
            this.report.overt = false;
        } else {
            this.report.overt = true;
        }
        if(this.report.reportSchemaId === 15) {
            let index = this.report.reportParameterPresetList.findIndex(item => item.reportSchemaQueryParameterId === 282);
            if(index >= 0) {
                if(this.eqcIDs && this.eqcIDs.length > 0) {
                    this.report.reportParameterPresetList[index].value = JSON.stringify({
                        objectId: this.eqcIDs
                    })
                } else {
                    this.report.reportParameterPresetList[index].value = JSON.stringify({
                        objectId: []
                    })
                }
            }
        }

        this.createReport(this.report);
        // if(this.report['maxQueryInterval'] !== null){
        //     let startDate = new Date(this.report.reportParameterPresetList[0].value);
        //     let endDate = new Date(this.report.reportParameterPresetList[1].value);
        //     let diffHour = (endDate.getTime() - startDate.getTime())/1000/60/60;
        //     if(this.report.maxQueryInterval < diffHour || Number.isNaN(diffHour)){
        //         this.toast.showToast('error', this.translate.instant('ngForm.create.timeIntervalError') + '(' + this.report.maxQueryInterval + ' hours' + ')', null)
        //     }else{
        //         this.createReport(this.report);
        //     }
        // }else{
        //     this.createReport(this.report);
        // }

    }

    createReport(data: ReportModel) {
        this.create(this.report).subscribe(res => {
            this.toast.showToast('success', this.langValues['ngForm.create.success'], null);
            this.location.back();
        }, err => {
            this.toast.showToast('error', this.langValues['ngForm.create.error'], null)
        })
    }

}
