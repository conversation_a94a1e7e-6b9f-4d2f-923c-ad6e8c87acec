import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CommunalModule } from '@app/communal/communal.module';
import { FormsModule } from '@angular/forms';
import { AppTranslationModule } from '@app/app.translation.module';
import { Ng2SmartTableModule } from 'ng2-smart-table';
import { routing } from './asset-device.routing';
import { CoreModule } from '@app/core/core.module';
import { AssetModule } from '../asset.module';
import { AssetDeviceListComponent } from './asset-device-list/asset-device-list.component';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { ExportExcelService } from './export-excel.service';
import { AssetDeviceAddComponent } from './asset-device-add/asset-device-add.component';
import { AssetDeviceService } from './asset-device.service';
import { AssetDeviceEditComponent } from './asset-device-edit/asset-device-edit.component';
import { SelectModule } from 'ng-select';
import { ImportTypeComponent } from './import-type/import-type.component';
import { Daterangepicker } from 'ng2-daterangepicker';
import { DataTableModule } from 'ng-devui'; 
import { CustomColumnsModule } from '@app/pages/site/components/custom-columns/custom-columns.module'
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzModalModule } from 'ng-zorro-antd/modal';

@NgModule({
    imports: [
        CommonModule,
        CommunalModule.forRoot(),
        CoreModule.forRoot(),
        routing,
        FormsModule,
        AppTranslationModule,
        Ng2SmartTableModule,
        AssetModule,
        SweetAlert2Module,
        SelectModule,
        Daterangepicker,
        DataTableModule,
        CustomColumnsModule,
        NzSelectModule,
        NzSpinModule,
        NzModalModule
    ],
    declarations: [AssetDeviceListComponent, AssetDeviceAddComponent, AssetDeviceEditComponent, ImportTypeComponent],
    providers: [ExportExcelService, AssetDeviceService]
})
export class AssetDeviceModule { }
