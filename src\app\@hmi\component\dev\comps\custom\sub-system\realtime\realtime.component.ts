import { ShareMessageService } from '../share-message.service';
import { AlarmSeverityImgRenderComponent } from '@app/pages/monitor/components/alarm-severity-img-render/alarm-severity-img-render-componet';
import { BatteryCellOverviewService } from '@app/pages/monitor/battery/battery-cell-overview/battery-cell-overview.service';
import { Component, OnInit, ViewChild, OnDestroy, Input } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Ng2SmartTableComponent } from 'ng2-smart-table';
import { LocalDataSource } from 'ng2-smart-table';
import { Subscription } from 'rxjs';
import { AuthenticationService } from '@app/core/services/authentication.service';

@Component({
  selector: 'app-realtime',
  templateUrl: './realtime.component.html',
  styleUrls: ['./realtime.component.scss']
})
export class RealtimeComponent implements OnInit, OnDestroy {
  @Input()
  batteryStringId;
  private _perPage: number = 10;
  @Input()
  get perPage(): number {
    return this._perPage;
  }
  set perPage(value: number) {
    this.authService.getAuthenticationJson().subscribe(
      (config: any) => {
        if (config && config['bytedace.web.enable']) {
          this._perPage = value || this._perPage; // 如果传入的是 null/undefined/0，使用默认值this._perPage
          this.onPerPageChange(); // 每次变化时执行响应逻辑
        }
      }
    );
  }
  private onPerPageChange(): void {
    // 更新settings中的perPage值
    this.settings = {
      ...this.settings,
      pager: {
        display: true,
        perPage: this._perPage
      }
    };
    // 重新初始化表格以应用新的设置
    if (this.ng2SmartTable) {
      this.ng2SmartTable.source.setPaging(1, this._perPage, true);
    }
  }
  @ViewChild('table', { static: true })
  ng2SmartTable: Ng2SmartTableComponent;
  source: LocalDataSource = new LocalDataSource();
  lanKeys: string[];
  lanStrings: {};
  settings = {};
  subscription: Subscription;

  constructor(
    private activatedRoute: ActivatedRoute,
    private translate: TranslateService,
    private service: BatteryCellOverviewService,
    private authService: AuthenticationService, // 使用AuthenticationService获取配置
    private messageService: ShareMessageService
  ) {}

  ngOnInit() {
    this.getLanStrings();
    this.getQueryParams();
  }

  ngOnDestroy() {
    this.source.load([]);
    if (this.subscription) {
      this.subscription.unsubscribe();
    }
  }

  getLanStrings(): void {
    this.lanKeys = [
      'general.common.noData',
      'general.batteryCell.realTimeSignal.batteryCellId',
      'general.batteryCell.realTimeSignal.batteryCellName',
      'general.batteryCell.realTimeSignal.cellTemp',
      'general.batteryCell.realTimeSignal.cellTempStatus',
      'general.batteryCell.realTimeSignal.cellVoltage',
      'general.batteryCell.realTimeSignal.cellVoltageStatus',
      'general.batteryCell.realTimeSignal.cellIR',
      'general.batteryCell.realTimeSignal.cellIRStatus'
    ];
    this.translate.get(this.lanKeys).subscribe(values => {
      this.lanStrings = values;
      this.loadTableColumnSettings();
      this.ng2SmartTable.initGrid();
      this.getCacheData();
    });
  }

  getQueryParams() {
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['batteryStringId']) {
        this.batteryStringId = params['batteryStringId'];
      }
    });
  }

  getCacheData() {
    this.subscription = this.messageService.get().subscribe(data => {
      if (data && data['batteryCellRealTimeSignalList']) {
        const batteryCellModel = this.service.formatBatteryCellLivePoints(
          data['batteryCellRealTimeSignalList']
        );
        this.source.load(batteryCellModel);
      }
      return;
    });
    const batteryCells = this.service.getBatteryCellModel();
    if (batteryCells && batteryCells['batteryCellRealTimeSignalList']) {
      const batteryCellModel = this.service.formatBatteryCellLivePoints(
        batteryCells['batteryCellRealTimeSignalList']
      );
      this.source.load(batteryCellModel);
    }
  }

  loadTableColumnSettings(): void {
    this.settings = {
      actions: false,
      hideSubHeader: true,
      pager: {
        display: true,
        perPage: this.perPage
      },
      noDataMessage: this.lanStrings['general.common.noData'],
      columns: {
        cellId: {
          title: this.lanStrings[
            'general.batteryCell.realTimeSignal.batteryCellId'
          ],
          type: 'string'
        },
        cellName: {
          title: this.lanStrings[
            'general.batteryCell.realTimeSignal.batteryCellName'
          ],
          type: 'string'
        },
        cellVoltageCurrentValue: {
          title: this.lanStrings[
            'general.batteryCell.realTimeSignal.cellVoltage'
          ],
          type: 'string'
        },
        cellVoltageEventLevel: {
          title: this.lanStrings[
            'general.batteryCell.realTimeSignal.cellVoltageStatus'
          ],
          type: 'custom',
          renderComponent: AlarmSeverityImgRenderComponent,
          valuePrepareFunction: (cell) => { return cell; }
        },
        cellTemperatureCurrentValue: {
          title: this.lanStrings['general.batteryCell.realTimeSignal.cellTemp'],
          type: 'string'
        },
        cellTemperatureEventLevel: {
          title: this.lanStrings[
            'general.batteryCell.realTimeSignal.cellTempStatus'
          ],
          type: 'custom',
          renderComponent: AlarmSeverityImgRenderComponent as Component,
          valuePrepareFunction: (cell) => { return cell; }
        },
        cellIRCurrentValue: {
          title: this.lanStrings['general.batteryCell.realTimeSignal.cellIR'],
          type: 'string'
        },
        cellIREventLevel: {
          title: this.lanStrings[
            'general.batteryCell.realTimeSignal.cellIRStatus'
          ],
          type: 'custom',
          renderComponent: AlarmSeverityImgRenderComponent,
          valuePrepareFunction: (cell) => { return cell; }
          // valuePrepareFunction: (cell, row) => { 
          //   return Math.round(Math.random() * 5 ) - 1 ;
          // }
        }
      }
    };
  }
}
