<div class="filtration-event-query-container">
  <div class="event-query-content form-inline-header">
    <ng-container *ngTemplateOutlet="rangePicker"></ng-container>
    <button class="btn btn-primary ml-1 mr-1" (click)="getTableSource()">
      {{ 'general.common.find' | translate }}
    </button>
    <div class="search-container mr-2">
      <input type="text" class="form-control" name="searchText" placeholder="{{ 'admin.role.placeholder' | translate }}"
        [(ngModel)]="searchText" />
      <span class="input-group-addon"><i class="ion-ios-search-strong"></i></span>
    </div>
  </div>
  <div class="overflow-y-auto">
    <ngx-datatable #table [rows]="rows" [columns]="columns" [columnMode]="'force'" [headerHeight]="38"
      [footerHeight]="50" [rowHeight]="'auto'" [externalPaging]="true" [messages]="messages"
      [count]="page.totalElements" [offset]="page.pageNumber" [limit]="page.size" (page)="setPage($event)"
      (sort)="onSort($event)">
    </ngx-datatable>
  </div>
</div>

<ng-template #rangePicker>
  <div class="input-group bs-daterangepicker">
    <label class="control-label">{{'s2.shieldevent.starttime' | translate}}</label>
    <div class="input-group">
      <input
        class="form-control"
        name="startTime"
        type="text"
        readonly
        [(ngModel)]="startTime"
        placeholder="{{ 'general.mask.startTime' | translate }}"
        (applyDaterangepicker)="selectTime($event, 'startTime')"
        daterangepicker
        [options]="options"
        [disabled]="dateDisabled"
      />
    </div>
    <label class="control-label">{{'s2.shieldevent.endtime' | translate}}</label>
    <div class="input-group">
      <input
        class="form-control"
        name="endTime"
        type="text"
        readonly
        [(ngModel)]="endTime"
        placeholder="{{ 'general.mask.endTime' | translate }}"
        (applyDaterangepicker)="selectTime($event, 'endTime')"
        daterangepicker
        [options]="options"
        [disabled]="dateDisabled"
      />
    </div>
  </div>
</ng-template>