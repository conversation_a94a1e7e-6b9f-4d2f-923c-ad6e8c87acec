import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Injectable()
export class RegisterUserService {
  language: any;

  constructor(private http: HttpClient) {
 
  }

  registerUser( param: any){
      const header = new HttpHeaders().set('content-Type', 'application/json');
      return this.http.post('siteweb-flow/api/processInstance/create', param);
  }

  // 验证手机号接口（预留）
  validatePhone(phone: string) {
      const header = new HttpHeaders().set('content-Type', 'application/json');
      // TODO: 替换为实际的后端验证接口
      return this.http.post('api/validate-phone', { phone: phone }, { headers: header });
  }

  // 批量上传文件接口
  uploadFiles(files: File[]) {
      const formData = new FormData();

      // 将所有文件添加到FormData中
      files.forEach((file, index) => {
          console.log(`添加文件 ${index}:`, file.name, file.size, file.type);
          formData.append('files', file);
      });

      // 打印FormData内容（调试用）
      console.log('FormData entries:');
      try {
          for (let pair of (formData as any).entries()) {
              console.log(pair[0], pair[1]);
          }
      } catch (e) {
          console.log('无法遍历FormData entries');
      }

      console.log('发送上传请求到:', 'siteweb-flow/api/attachment/upload/batch');

      // 对于文件上传，不需要设置Content-Type，浏览器会自动设置为multipart/form-data
      // 并且会自动添加boundary参数
      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData);
  }

  // 备用上传方法 - 尝试不同的参数名
  uploadFilesAlternative(files: File[]) {
      const formData = new FormData();

      // 尝试使用单数形式的参数名
      files.forEach((file, index) => {
          console.log(`备用方法添加文件 ${index}:`, file.name);
          formData.append('file', file); // 使用 'file' 而不是 'files'
      });

      console.log('使用备用方法发送上传请求');
      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData);
  }

  // 测试单文件上传
  uploadSingleFile(file: File) {
      const formData = new FormData();
      formData.append('files', file);

      console.log('测试单文件上传:', file.name);
      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData);
  }
}
