<div class=" comp-item custom-comp-item" [ngClass]="{
  'xk': theme === 'default' && currentState !== 'dev',
  'jd': theme === 'classic'&& currentState !== 'dev',
  'sky': theme === 'sky'&& currentState !== 'dev',
  'jl_xk': settingCompTheme === 'xk' && currentState === 'dev',
  'jl_jd': settingCompTheme === 'jd' && currentState === 'dev',
  'jl_wg': settingCompTheme === 'wg' && currentState === 'dev',
  'jl_sky': settingCompTheme === 'sky' && currentState === 'dev',
 'comp-active': settingObj['active'] ,
 'multipAction': settingObj['multipAction']}" draggable="true" (dragstart)="compEvent($event)"
  (drag)="compEvent($event)" (dragend)="compEvent($event)" (click)="compEvent($event)" (mousedown)="compEvent($event)"
  [ngStyle]="{
  'position':style['position'],
  'top':expandUnit('top'),
  'left':expandUnit('left'),
  'width':((isBytedaceWebEnable && style['width']) ? expandUnit('width') : expandUnitPercent('widthPercentage')),
  'height': expandUnit('height'),
  'border-width':expandUnit('borderWidth'),
  'border-style':style['borderStyle'],
  'border-color':style['borderColor'],
  'z-index':style['zIndex'],
  'border-radius':style['borderRadius'],
  'opacity':style['opacity'],
  '-webkit-user-drag':style['userDrag'],
  'display': 'flex',
  'flex-direction': 'column',
  'overflow': 'hidden'
}">
  <div class="widget">
    <ba-card baCardClass="feed-comply-panel  todo-panel" class="battery-detail-general">
      <div class="form-inline">
        <div class="battery-name form-inline">
          <i class="iconfont icon-device-name"></i>
          <label class="battery-label">
            {{ batteryCellModel && batteryCellModel['batteryStringName'] ? batteryCellModel['batteryStringName'] :'' }}
          </label>
        </div>
        <div class="battery-path form-inline">
          <i class="iconfont icon-device-location"></i>
          <label class="battery-label">
            {{ batteryCellModel && batteryCellModel['equipmentPath'] ? batteryCellModel['equipmentPath'] :'--' }}
          </label>
        </div>
        <div class="battery-cellcount form-inline">
          <i class="iconfont icon-battery-cell-condition"></i>
          <label class="battery-label">{{ batteryCellModel && batteryCellModel['cellCount'] ? batteryCellModel['cellCount'] :'--' }}
            {{'general.batteryCell.batteryCellCount' | translate}}</label>
        </div>
        <div class="battery-status form-inline">
          <i *ngIf="status == '在线'" class="iconfont icon-rack-power-rate"></i>
          <i *ngIf="status == '离线'" class="iconfont icon-device-connectstatus"></i>
          <label class="battery-label">
            {{ status ? status :'--' }}
          </label>
        </div>
      </div>
      <div class="form-ul">
        <div class="warpper child-grid">
          <div class="childs">
            <ul class="checked-list">
              <li>
                <span class="w-40 first-col-title" title="{{'general.batteryCell.vendorModel' | translate}}">{{'general.batteryCell.vendorModel' | translate}}</span>
                <span class="w-10 first-col-value" title="{{ batteryCellModel && batteryCellModel['vendorModel'] ? batteryCellModel['vendorModel'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['vendorModel'] ? batteryCellModel['vendorModel'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.battery.ratedVoltage' | translate}}">{{'general.battery.ratedVoltage' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['ratedVoltage'] ? batteryCellModel['ratedVoltage'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['ratedVoltage'] ? batteryCellModel['ratedVoltage'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.battery.ratedCapacity' | translate}}">{{'general.battery.ratedCapacity' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['ratedCapacity'] ? batteryCellModel['ratedCapacity'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['ratedCapacity'] ? batteryCellModel['ratedCapacity'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.battery.initialIR' | translate}}">{{'general.battery.initialIR' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['initialIR'] ? batteryCellModel['initialIR'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['initialIR'] ? batteryCellModel['initialIR'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40 first-col-title" title="{{'general.batteryCell.totalVoltage' | translate}}">{{'general.batteryCell.totalVoltage' | translate}}</span>
                <span class="w-10 first-col-value" title="{{ batteryCellModel && batteryCellModel['totalVoltage'] ? batteryCellModel['totalVoltage'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['totalVoltage'] ? batteryCellModel['totalVoltage'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.batteryCell.averageVoltage' | translate}}">{{'general.batteryCell.averageVoltage' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['averageVoltage'] ? batteryCellModel['averageVoltage'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['averageVoltage'] ? batteryCellModel['averageVoltage'] :'--'
                  }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.batteryCell.averageIR' | translate}}">{{'general.batteryCell.averageIR' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['averageIR'] ? batteryCellModel['averageIR'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['averageIR'] ? batteryCellModel['averageIR'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.batteryCell.temperature' | translate}}">{{'general.batteryCell.temperature' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['temperature'] ? batteryCellModel['temperature'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['temperature'] ? batteryCellModel['temperature'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40 first-col-title" title="{{'general.batteryCell.totalCurrent' | translate}}">{{'general.batteryCell.totalCurrent' | translate}}</span>
                <span class="w-10 first-col-value" title="{{ batteryCellModel && batteryCellModel['totalCurrent'] ? batteryCellModel['totalCurrent'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['totalCurrent'] ? batteryCellModel['totalCurrent'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.batteryCell.maxMinVoltage' | translate}}">{{'general.batteryCell.maxMinVoltage' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['maxMinVoltage'] ? batteryCellModel['maxMinVoltage'] :'--' }}">
                  {{ batteryCellModel && batteryCellModel['maxMinVoltage'] ? batteryCellModel['maxMinVoltage'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.batteryCell.maxMinIR' | translate}}">{{'general.batteryCell.maxMinIR' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['maxMinIR'] ? batteryCellModel['maxMinIR'] :'--' }}"  >
                  {{ batteryCellModel && batteryCellModel['maxMinIR'] ? batteryCellModel['maxMinIR'] :'--' }}
                </span>
              </li>
              <li>
                <span class="w-40" title="{{'general.batteryCell.maxMinTemp' | translate}}">{{'general.batteryCell.maxMinTemp' | translate}}</span>
                <span class="w-10" title="{{ batteryCellModel && batteryCellModel['maxMinTemp'] ? batteryCellModel['maxMinTemp'] :'--' }}"  >
                  {{ batteryCellModel && batteryCellModel['maxMinTemp'] ? batteryCellModel['maxMinTemp'] :'--' }}
                </span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </ba-card>
  </div>
  <div class="export-button">
    <span class="fa fa-share-square-o" title="{{'general.common.export' | translate}}" (click)="exportData()"></span>
  </div>
  <div class="tabset">
    <ngb-tabset [activeId]="1">
      <ngb-tab>
        <ng-template ngbTabTitle>
          <div class="dashboard-title1" title="{{'general.batteryCell.realData' | translate}}">{{'general.batteryCell.realData' | translate}}</div>
        </ng-template>
        <ng-template ngbTabContent>
          <app-realtime [perPage]="perPage"></app-realtime>
        </ng-template>
      </ngb-tab>
      <ngb-tab>
        <ng-template ngbTabTitle>
          <div class="dashboard-title1" title="{{'general.batteryCell.performCompare' | translate}}">{{'general.batteryCell.performCompare' | translate}}</div>
        </ng-template>
        <ng-template ngbTabContent>
          <app-performance></app-performance>
        </ng-template>
      </ngb-tab>
      <ngb-tab>
        <ng-template ngbTabTitle>
          <div class="dashboard-title1" title="{{'general.batteryCell.realDischarge' | translate}}">{{'general.batteryCell.realDischarge' | translate}}</div>
        </ng-template>
        <ng-template ngbTabContent>
          <app-discharge-monitoring></app-discharge-monitoring>
        </ng-template>
      </ngb-tab>
      <ngb-tab>
        <ng-template ngbTabTitle>
          <div class="dashboard-title1" title="{{'general.batteryCell.hisdischarge' | translate}}">{{'general.batteryCell.hisdischarge' | translate}}</div>
        </ng-template>
        <ng-template ngbTabContent>
          <app-discharge-record [deviceId]='deviceId'></app-discharge-record>
        </ng-template>
      </ngb-tab>
      <ngb-tab>
        <ng-template ngbTabTitle>
          <div class="dashboard-title1" title="{{'general.batteryCell.alarms' | translate}}">{{'general.batteryCell.alarms' | translate}}</div>
        </ng-template>
        <ng-template ngbTabContent>
          <app-battery-alarm-list [deviceId]='deviceId'></app-battery-alarm-list>
        </ng-template>
      </ngb-tab>
      <ngb-tab *ngIf="equiementControlState === 'true'">
        <ng-template ngbTabTitle>
          <div class="dashboard-title1" title="{{'general.batteryCell.controls' | translate}}">{{'general.batteryCell.controls' | translate}}</div>
        </ng-template>
        <ng-template ngbTabContent>
          <app-equipment-asset-control [deviceId]="deviceId"></app-equipment-asset-control>
        </ng-template>
      </ngb-tab>

    </ngb-tabset>
  </div>
</div>