import { Component, Input } from '@angular/core';
import { ViewCell } from 'ng2-smart-table';

@Component({
  selector: 'app-reason-renderer',
  template: `
    <div>
      <span>{{ reasonName }}</span>
    </div>
  `,
  styles: []
})
export class ReasonRendererComponent implements ViewCell {
  @Input() value: any;  // 从 valuePrepareFunction 获取的告警分类名称
  @Input() rowData: any;
  @Input() rowIndex: number;
  
  reasonName: string;

  ngOnInit() {
    this.reasonName = this.value || '';  // 获取告警分类名称
  }
}
