
import { fork<PERSON><PERSON><PERSON> } from 'rxjs';
import { Component, OnInit, ViewChild, Input, OnChanges, SimpleChanges } from '@angular/core';
import { LocalDataSource } from 'ng2-smart-table';
import { Ng2SmartTableComponent } from 'ng2-smart-table';
import { TranslateService } from '@ngx-translate/core';
import { Router, ActivatedRoute } from '@angular/router';
import { AlertService } from '@app/communal/providers';
import { ToastService } from '@communal/providers';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as _ from 'lodash';
import { IOption } from 'ng-select';
import { Ng2TreeSettings, NodeEvent } from 'ng2-tree';
import { IndicatorModel } from '../../../hierarchy/components/indicator-edit/indicator.model';
import { CenterService } from '../../../hierarchy/services/center.service';
import { IndicatorSelectService } from '../../../hierarchy/components/indicator-edit/indicator-select-modal.service';
import { ExpressSelectorComponent } from '../../../hierarchy/components/express-selector/express-selector.component';
import { HttpClient } from '@angular/common/http';



@Component({
  selector: 'app-device-indicator-edit',
  templateUrl: './device-indicator-edit.component.html',
  styleUrls: ['./device-indicator-edit.component.scss']
})
export class DeviceIndicatorEditComponent implements OnInit, OnChanges {

  tree: any;
  treeSettings: Ng2TreeSettings = {};
  indexregexp = /(?<=ci\()\d+|(?<=LAST\()\d+/gi;
  // pointregexp = /(?<=CP\()\d+/gi;
  pointregexp = /(?<=CP\()(.+?)(?=\))/gi;
  @ViewChild('treeComponent') treeComponent;
  @ViewChild('table', { static: true }) ng2SmartTable: Ng2SmartTableComponent;
  indicatorData: IndicatorModel = new IndicatorModel();
  accuracyOptionList = [
    {
      value: '',
      label: '',
    },
    {
      value: '0',
      label: '0',
    },
    {
      value: '0.0',
      label: '0.0',
    },
    {
      value: '0.00',
      label: '0.00',
    },
    {
      value: '0.000',
      label: '0.000',
    },
    {
      value: '0.0000',
      label: '0.0000',
    }];
  iscalctypeList = [
    // {
    //   value: '',
    //   label: '',
    // },
    {
      value: '1',
      label: this.translate.instant('idcManage.common.isShow'),
    }
  ]
  expression: string;
  ids: Array<any> = [];
  selectIndexList: Array<any> = [];
  selectPointList: Array<any> = [];
  isIndex = false;
  isPoint = false;
  selectIndexIds: Array<string> = [];
  selectPointIds: Array<string> = [];
  allBusiness = [];
  complexIndexDefinitionsList = [];
  businessOptionList = [];
  indexDefinitionList = [];
  businessTypeName = null;
  indexObjectTemplates = [];
  langValues: any;
  defaultCron = { value: '0 0 0/12 * * ? *', label: '每12小时' };
  source: LocalDataSource = new LocalDataSource();
  columnHeaders: any;
  columnHeaderKeys: string[];
  settings: any;
  countPeriodRequired: boolean;
  saveCronRequired: boolean;
  submitted: boolean;
  ngFormValid = true;
  showTreeview = true;
  batchTypes = [
    { value: 0, name: 'idcManage.hierarchy.indicatorEdit.current', tooltip: 'idcManage.hierarchy.indicatorEdit.currentTip' },
    { value: 1, name: 'idcManage.hierarchy.indicatorEdit.sameLevel', tooltip: 'idcManage.hierarchy.indicatorEdit.sameLevelTip' },
    { value: 2, name: 'idcManage.hierarchy.indicatorEdit.childLevel', tooltip: 'idcManage.hierarchy.indicatorEdit.childLevelTip' }
  ];
  fields = [
    { value: 'checkExpression', name: 'idcManage.hierarchy.indicatorEdit.indicatorQuality', checked: true },
    { value: 'calcCron', name: 'idcManage.hierarchy.indicatorEdit.countPeriod', checked: true },
    { value: 'saveCron', name: 'idcManage.hierarchy.indicatorEdit.savePeriod', checked: true },
    { value: 'unit', name: 'idcManage.hierarchy.indicatorEdit.unit', checked: true },
    { value: 'accuracy', name: 'idcManage.hierarchy.indicatorEdit.precision', checked: true },
    { value: 'calcType', name: 'idcManage.hierarchy.indicatorEdit.iscalctype', checked: true }
  ];
  sceneId: any;
  structureTypeId: any;
  resourceStructureId: any;

  constructor(
    private translate: TranslateService,
    private centerService: CenterService,
    protected alertService: AlertService,
    protected toastService: ToastService,
    private http: HttpClient,
    private router: Router,
    private modal: NgbModal,
    private activatedRoute: ActivatedRoute,
    private indicatorService: IndicatorSelectService

  ) { }

  ngOnInit() {
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['deviceId']) {
        this.resourceStructureId = parseInt(params['deviceId']);
      }
    });
    this.http.get('/scenes/current').subscribe(res => {
      this.sceneId = res['sceneId'];
      this.structureTypeId = 7;
      this.setTableColumnSettings();
    })
    $(function () { $("[data-toggle='tooltip']").tooltip(); });
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['resourceStructureId'] && changes['resourceStructureId'].currentValue) {
      this.initData();
    }
  }

  setTableColumnSettings(): void {
    this.columnHeaderKeys = [
      'idcManage.indexAllocation.title',
      'idcManage.indexAllocation.businessType',
      'idcManage.indexAllocation.expression',
      'idcManage.indexAllocation.isExpression',
      'idcManage.indexAllocation.NotExpression',
      'idcManage.indexAllocation.operation',
      'general.common.noData',
      'idcManage.common.confirmDeleteIndicator'
    ];
    this.translate.get(this.columnHeaderKeys).subscribe((values) => {
      this.columnHeaders = values;
      this.loadTableColumnSettings();
      this.ng2SmartTable.initGrid();
      this.initData();
    });
  }


  initData() {
    this.expression = '';
    this.indicatorData.resourceStructureId = this.activatedRoute.snapshot.paramMap['params']['id'];
    if (this.resourceStructureId) {
      forkJoin([this.centerService.getAll(`complexindexs?objectId=${this.resourceStructureId}&objectTypeId=${this.structureTypeId}`)]).subscribe(res => {
        this.source = new LocalDataSource(res[0]);
      });
    }
    let url = `complexindexbusinesstype/buildtree?objectTypeId=${this.structureTypeId}`;
    if (this.sceneId != null) {
      url += `&sceneId=${this.sceneId}`;
    }
    this.centerService.getAll(url).subscribe(res => {
      if (res) {
        this.allBusiness = res;
        this.tree = null;
        this.tree = {
          value: '',
          children: res ? this.setChildren(res) : [],
        };
        this.tree.settings = {
          cssClasses: {
            expanded: "fa fa-caret-down",
            collapsed: "fa fa-caret-right",
            empty: "fa",
            leaf: "fa",
          },
        }
      }
    });
  }

  setChildren(data) {
    return data.map((item) => {
      return item = {
        id: item.businessTypeId,
        value: item.businessTypeName,
        complexIndexDefinitions: item.complexIndexDefinitions,
        children: item.children.length > 0 ? this.setChildren(item.children) : [],
      };
    });
  }

  // 指标定义转换成下拉框数据格式
  getDefinitionsNgselectOption(data) {
    const sources = Array<IOption>();
    data.map(element => {
      const sourceItem: IOption = {
        value: element.complexIndexDefinitionId.toString(),
        label: element.complexIndexDefinitionName
      };
      sources.push(sourceItem);
    });
    return sources;
  }

  // 业务分类下拉框失焦延迟关闭选项 不然会与选项树的点击事件冲突
  inputBlur() {
    setTimeout(() => {
      this.showTreeview = true;
    }, 200);
  }

  // 指标定义下拉框选项点击
  definitionSelected(event) {
    this.indicatorData.complexIndexName = event.label;
    this.complexIndexDefinitionsList.map(res => {
      if (res.complexIndexDefinitionName === this.indicatorData.complexIndexName) {
        this.indicatorData.calcCron = res.calcCron;
        this.indicatorData.checkExpression = res.checkExpression;
        this.indicatorData.saveCron = res.saveCron;
      }
    });
  }

  //计算精度下拉框点击
  accuracySelected(event) {
    this.indicatorData.accuracy = event.value;
  }

  //差值计算点击
  calcTypeSelected(event) {
    this.indicatorData.calcType = event.value;
  }

  // 指标列表行点击
  selectedRow(event) {
    this.selectIndexList = [];
    this.selectPointList = [];
    this.indexDefinitionList = [];
    this.indicatorData = new IndicatorModel();
    if (event.data) {
      this.indicatorData = event.data;
      this.businessTypeName = event.data.businessTypeName;
      this.expression = this.indicatorData.expression;
      this.ids = this.extractUniqueCIIds(this.expression);
      this.normalizeAfterCalc(); // 规范化 afterCalc 字段
      this.initIds();
      if (this.indicatorData.businessTypeId) {
        const treeController = this.treeComponent.getControllerByNodeId(this.indicatorData.businessTypeId);
        // 业务分类下拉框树型回显
        treeController.select();
        // 指标定义下拉框回显
        this.complexIndexDefinitionsList = treeController.tree.node.complexIndexDefinitions;
        this.indexDefinitionList = this.getDefinitionsNgselectOption(this.complexIndexDefinitionsList);
      }
      this.indicatorData.complexIndexDefinitionId = event.data.complexIndexDefinitionId ? event.data.complexIndexDefinitionId.toString() : null;
      this.indicatorData.calcType = event.data.calcType.toString();

      this.indicatorData.batchType = 0;
      this.countPeriodRequired = false;
      this.saveCronRequired = false;
    }
  }

  // 业务分类下拉框树型节点选择
  logEvent(e: NodeEvent) {
    const currentNode = e.node.node;
    this.indicatorData.businessTypeId = ~~e.node.node.id;
    this.businessTypeName = currentNode.value;
    this.complexIndexDefinitionsList = e.node.node.complexIndexDefinitions;
    this.indexDefinitionList = this.getDefinitionsNgselectOption(this.complexIndexDefinitionsList);
  }

  loadTableColumnSettings(): void {
    this.settings = {
      mode: 'external',
      actions: {
        columnTitle: this.columnHeaders['idcManage.indexAllocation.operation'],
        position: 'right',
        edit: false,
        delete: true,
        custom: [],
      },
      pager: {
        display: true,
        perPage: 15
      },
      noDataMessage: this.columnHeaders['general.common.noData'],
      columns: {
        complexIndexName: {
          title: this.columnHeaders['idcManage.indexAllocation.title'],
          type: 'string',
          filter: false
        },
        businessTypeName: {
          title: this.columnHeaders['idcManage.indexAllocation.businessType'],
          type: 'string',
          filter: false
        },
        expression: {
          title: this.columnHeaders['idcManage.indexAllocation.expression'],
          type: 'html',
          filter: false,
          valuePrepareFunction: (cell, row) => {
            return row.expression ? this.columnHeaders['idcManage.indexAllocation.isExpression'] : this.columnHeaders['idcManage.indexAllocation.NotExpression']
          }
        },
      },
      edit: { editButtonContent: '<i class="ion-edit"></i>' },
      delete: {
        deleteButtonContent: '<i class="ion-trash-a"></i>',
        confirmDelete: true
      }
    };
  }


  edit(event) {
    const id = event.data['complexIndexId'];
    this.router.navigate(['./indicatoredit', id], { relativeTo: this.activatedRoute });
  }

  delete(event) {
    const contentConfirm = this.columnHeaders['idcManage.common.confirmDeleteIndicator'] + event.data['complexIndexName'];
    this.alertService.deleteConfirm(contentConfirm).then(res => {
      if (res.value) {
        this.indicatorService.delete(event.data['complexIndexId']).subscribe(() => {
          this.initData();
          this.alertService.deletedSucceed();
        });
      }
    });
  }
  getCalcCron(cron) {
    this.indicatorData.calcCron = cron;
  }

  getIndicatorTemplate(indexId) {

    if (this.indexObjectTemplates) {

      const indexTemplate = this.indexObjectTemplates.find(
        x => x.complexIndexTemplateId === Number(indexId)
      );
      if (indexTemplate) {
        // this.indicatorData.accuracy=indexTemplate.accuracy;
        // this.indicatorData.afterCalc=indexTemplate.afterCalc;
        // this.indicatorData.businessId=indexTemplate.businessId;
        // this.indicatorData.calcCron=indexTemplate.calcCron;
        // this.indicatorData.calcType=indexTemplate.calcType;
        // this.indicatorData.category=indexTemplate.category;
        this.indicatorData = indexTemplate;
        this.indicatorData.complexIndexName = indexTemplate.complexIndexTemplateName;
      }
    }
  }

  initIds() {
    if (this.expression) {

      this.selectIndexIds = this.expression.match(this.indexregexp);

      if (this.selectIndexIds && this.selectIndexIds.length > 0) {
        this.isIndex = true;
        this.getIndexIdsData(this.selectIndexIds);
      }
      else {
        this.isIndex = false;
      }

      this.selectPointIds = this.expression.match(this.pointregexp);

      if (this.selectPointIds && this.selectPointIds.length > 0) {
        this.isPoint = true;
        this.getPointsIdsData(this.selectPointIds);

      }
      else {
        this.isPoint = false;
      }
    }
  }

  getIndexIdsData(complexIndeIds) {

    forkJoin([this.indicatorService.getCompplexIndex(complexIndeIds)]).subscribe(res => {
      if (res && res[0].length > 0) {
        this.selectIndexList = res[0];
      }
    })
  }

  getPointsIdsData(corePointIds) {
    forkJoin([this.indicatorService.getPoints(corePointIds)]).subscribe(res => {
      if (res && res[0].length > 0) {
        //将取出来的关联测点和表达式的顺序对应
        corePointIds.forEach((corePointId) => {
          const selectPointItem = res[0].filter(item => (item['equipmentId'] + '.' + item['signalId']) === corePointId);
          if (selectPointItem.length > 0) {
            this.selectPointList.push(selectPointItem[0]);
          }
        });
      }
    })
  }

  getSaveCron(cron) {
    this.indicatorData.saveCron = cron;
  }

  getUnit(unit) {
    this.indicatorData.unit = unit;
  }


  beforeSubmitAction() {
    this.indicatorData.title = this.indicatorData.complexIndexName;
    this.indicatorData.objectId = this.resourceStructureId;
    if (this.indicatorData.batchType === 1) {
      this.indicatorData.objectIds = this.activatedRoute.snapshot.queryParams['resourceStructureIds'];
    }
    this.indicatorData.objectTypeId = this.structureTypeId;
    if (!this.indicatorData.calcCron) {
      this.indicatorData.calcCron = null;
    }
    if (!this.indicatorData.saveCron) {
      this.indicatorData.saveCron = null;
    }
    if (!this.indicatorData.calcType) {
      this.indicatorData.calcType = 0;
    }
    return this.indicatorData;
  }

  openExpress(opentype) {
    const modal = this.modal.open(ExpressSelectorComponent, {
      backdrop: 'static',
      backdropClass: 'bounced-hierarchy',
      windowClass: 'window-hierarchy'
    });
    modal.componentInstance.opentype = opentype;
    modal.componentInstance.untimateFormula = '';
    if (this.expression) {
      modal.componentInstance.untimateFormula = this.expression;
    }
    modal.result.then((items) => {
      if (items) {
        this.indicatorData.expression = items.expression;
        this.expression = items.expression;
        this.ids = items.ids;
      }
      if (this.expression) {
        this.countPeriodRequired = this.indicatorData.calcCron ? false : true;
      }
    });
  }

  onCalcOrderChange(value: any) {
    const numValue = Number(value);
    if (isNaN(numValue) || numValue < 1) {
      this.indicatorData.afterCalc = '1';
    } else if (numValue > 9999) {
      this.indicatorData.afterCalc = '9999';
    } else {
      this.indicatorData.afterCalc = String(Math.floor(numValue));
    }
  }

  // 初始化时处理非数字的 afterCalc 值
  private normalizeAfterCalc() {
    // 只处理非数字字符串，null/undefined/空字符串保持原样
    if (this.indicatorData.afterCalc !== null && 
        this.indicatorData.afterCalc !== undefined && 
        this.indicatorData.afterCalc !== '') {
      const numValue = Number(this.indicatorData.afterCalc);
      if (isNaN(numValue)) {
        // 如果是非数字字符串，设置默认值为 1
        this.indicatorData.afterCalc = '1';
      } else {
        // 确保数值在有效范围内
        this.onCalcOrderChange(this.indicatorData.afterCalc);
      }
    }
    // null/undefined/空字符串保持原样，不做处理
  }

  extractUniqueCIIds(expression: string): string[] {
    const regex = /(CI|last)\((\d+)\)/g;
    const ids: string[] = [];
    let match: RegExpExecArray | null;

    while ((match = regex.exec(expression)) !== null) {
      ids.push(match[1]);
    }

    const uniqueIds: string[] = [];
    const seen = new Set<string>();

    for (const id of ids) {
      if (!seen.has(id)) {
        seen.add(id);
        uniqueIds.push(id);
      }
    }

    return uniqueIds;
  }

  submitClick(ngFormValid) {
    this.countPeriodRequired = this.indicatorData.calcCron ? false : true;
    this.saveCronRequired = this.indicatorData.saveCron ? false : true;
    if (!ngFormValid || this.countPeriodRequired || this.saveCronRequired) {
      this.ngFormValid = ngFormValid;
      return false;
    }
    if (this.indicatorData.batchType !== 0) {
      $('#myModal').modal('show');
    } else {
      this.submit();
    }
  }

  submit() {
    const param = this.beforeSubmitAction();
    const isCustomerSubmitRouter =
      _.isArray(param) && param.length === 2 && !!param[1]['submitUrl'];
    const params = isCustomerSubmitRouter ? param[0] : param;
    const submitedReturnUrl = isCustomerSubmitRouter
      ? param[1]['submitUrl']
      : false;
    this.indicatorService.update(params).subscribe(
      () => {
        this.toastService.showToast(
          'success',
          '保存成功',
          null
        );
        this.initData();
        if (submitedReturnUrl) {
          this.router.navigated = true;
          this.router.navigate([submitedReturnUrl]);
          setTimeout(() => {
            window.location.reload();
          }, 500);
        } else {
          this.cancel();
        }
      },
      () => {
        this.toastService.showToast(
          'error',
          '保存失败',
          null
        );
      }
    );
  }

  cancel(): void {
    if (this.indicatorData.businessTypeId) {
      const treeController = this.treeComponent.getControllerByNodeId(this.indicatorData.businessTypeId);
      treeController.unselect();
    }
    this.indicatorData = new IndicatorModel();
    this.indicatorData.objectId = this.resourceStructureId;
    this.indicatorData.objectTypeId = this.structureTypeId;
    this.selectIndexList = [];
    this.selectPointList = [];
    this.indexDefinitionList = [];
    this.businessTypeName = '';
    this.expression = '';
    this.ng2SmartTable.grid.dataSet.deselectAll();
  }

  modalSubmit() {
    this.indicatorData.fields = '';
    this.fields.map(item => {
      if (item.checked === true) {
        return this.indicatorData.fields += item.value + ' ';
      }
    });
    $('#myModal').modal('hide');
    this.submit();
  }
}
