
import { forkJoin, Subscribable, Observable } from 'rxjs';
import { Component, OnInit, Injector } from '@angular/core';
import { AssetDevice } from '../asset-device.model';
import { Router, ActivatedRoute } from '@angular/router';
import { AssetDeviceService } from '../asset-device.service';
import { IOption } from 'ng-select';
import { BaseAddComponents } from '@app/communal/components/base-add/baseAddComponents';
import * as _ from 'lodash';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TreeEquipmentSelectorComponent } from '@app/communal/components/tree-equipment-selector/tree-equipment-selector.component';

@Component({
  selector: 'app-asset-device-add',
  templateUrl: './asset-device-add.component.html',
  styleUrls: ['./asset-device-add.component.scss']
})
export class AssetDeviceAddComponent extends BaseAddComponents implements OnInit {
  assetDevice: AssetDevice = new AssetDevice();
  assetTypeList: any = [];
  assetCategory: string;
  assetExtend: any = [];
  assetDeviceData: any = [];
  duplicate = false;
  // 加载状态映射，通过扩展字段ID区分不同字段的加载状态
  isLoadingMap: { [extId: number]: boolean | string } = {};
  // 选项列表映射，通过扩展字段ID区分不同的选项列表
  optionListMap: { [extId: number]: any[] } = {};
  // 页码映射，通过扩展字段ID区分不同的页码
  pageNumberMap: { [extId: number]: number } = {};
  // 搜索值
  keyWord: string = '';
  dateOptions: any = {
    singleDatePicker: true,
    locale: {
      format: 'YYYY-MM-DD',
      cancelLabel: this.translateService.instant('s2.reportmanagement.alert.cancel'),
      applyLabel: this.translateService.instant('s2.reportmanagement.alert.confirm'),
      daysOfWeek: [
        this.translateService.instant('s2.calendar.Sunday'),
        this.translateService.instant('s2.calendar.Monday'),
        this.translateService.instant('s2.calendar.Tuesday'),
        this.translateService.instant('s2.calendar.Wednesday'),
        this.translateService.instant('s2.calendar.Thursday'),
        this.translateService.instant('s2.calendar.Friday'),
        this.translateService.instant('s2.calendar.Saturday'),
      ],
      monthNames: [
        this.translateService.instant('s2.calendar.January'),
        this.translateService.instant('s2.calendar.February'),
        this.translateService.instant('s2.calendar.March'),
        this.translateService.instant('s2.calendar.April'),
        this.translateService.instant('s2.calendar.May'),
        this.translateService.instant('s2.calendar.June'),
        this.translateService.instant('s2.calendar.July'),
        this.translateService.instant('s2.calendar.August'),
        this.translateService.instant('s2.calendar.September'),
        this.translateService.instant('s2.calendar.October'),
        this.translateService.instant('s2.calendar.November'),
        this.translateService.instant('s2.calendar.December'),
      ],
    },
  };


  constructor(
    injector: Injector,
    private assetService: AssetDeviceService,
    private modal: NgbModal,
  ) {
    super(injector, assetService, {
      baseUrl: 'idcassetdevices',
      translatePrefix: 'asset.assetDevice'
    });
    this.initTranslateMessage([]);
  }

  ngOnInit() {
    forkJoin([this.assetService.getAll('idcassetcategorys'), this.assetService.getAll('extfieldconfigurations?extTable=assetDevice'), this.assetService.getAll('idcassetdevices')]).subscribe(res => {
      this.assetTypeList = this.getAssetType(res[0]);
      this.assetExtend = res[1].filter(field => !field.extCode || !field.extCode.includes("_noshow"));
      this.assetDeviceData = res[2];

      this.assetExtend.forEach(item => {
        if (item.extDataType === 'table') {
          item['labelValue'] = item['extDataSource'] ? JSON.parse(item['extDataSource']) : {};
          this.pageNumberMap[item.extId] = 1;
          this.optionListMap[item.extId] = [];
        }
      })
    })
  }


  onValueChange(e, item) {
    item.extValue = DateUtil.shortDateToString(
      e.picker.startDate['_d']
    );
  }

  selectDevice(item) {
    let modal;
    modal = this.modal.open(TreeEquipmentSelectorComponent, { backdrop: 'static' });
    modal.componentInstance.multipleBool = false;
    modal.componentInstance.selectDeviceList = item.extValue ? JSON.parse(item.extValue) : [];
    modal.result.then((res) => {
      if (res) {
        const list = [];
        list.push({
          eqId: res[0].eqId,
          eqName: res[0].eqName,
          roomName: res[0].roomName,
          rId: res[0].rId,
          positionId: res[0].positionId,
        })
        this.assetDevice.settingPosition = res[0].fullPath;
        item.eqName = res[0].eqName;
        item.extValue = res.length > 0 ? JSON.stringify(list) : null;
      }
    });
  }

  checkDuplicate(event) {
    this.duplicate = false;
    if (this.assetDeviceData && this.assetDeviceData.length > 0) {
      _.map(this.assetDeviceData, item => {
        if (event === item.assetCode) {
          this.duplicate = true;
        }
      })
    }
  }

  beforeSubmitAction() {
    this.assetDevice.extValueConfigurationList = this.assetExtend;
    this.assetDevice.assetCategoryId = parseInt('' + this.assetCategory);
    return this.assetDevice;
  }

  getAssetType(assetTypeData) {
    if (!assetTypeData) {
      return null;
    }
    const sources = Array<IOption>();
    assetTypeData.forEach(element => {
      const sourceItem: IOption = {
        value: String(element.assetCategoryId),
        label: element.assetCategoryName
      };
      sources.push(sourceItem);
    });
    return sources;
  }

  // 搜索
  onSearch(event: string, extId: number): void {
    // 清理输入：去除首尾空格
    this.keyWord =  event ? event.trim() : '';
    // 解决输入第一个文字，还未输入完成时会触发搜索问题
    if (event !== ' ') {
      this.pageNumberMap[extId] = 1;
      this.optionListMap[extId] = [];
      this.loadMore(extId, true);
    }
  }

  // 加载更多
  loadMore(extId: number, search: boolean = false): void {
    // 如果已经加载完，并且不是搜索，则不加载
    if (this.isLoadingMap[extId] === 'noMore' && !search) {
      return;
    }
    this.isLoadingMap[extId] = true;
    this.assetService.loadExtFieldOptionData(extId, this.keyWord, this.pageNumberMap[extId], 10).subscribe({
      next: (data) => {
        this.isLoadingMap[extId] = false;
        this.pageNumberMap[extId]++;
        this.optionListMap[extId] = [...this.optionListMap[extId], ...data];
        if (data.length === 0) {
          this.isLoadingMap[extId] = 'noMore';
        }
      },
      error: () => {
        this.isLoadingMap[extId] = false;
      }
    });
  }

}
