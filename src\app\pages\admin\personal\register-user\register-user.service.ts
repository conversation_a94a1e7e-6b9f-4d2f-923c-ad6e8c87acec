import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Injectable()
export class RegisterUserService {
  language: any;

  constructor(private http: HttpClient) {
 
  }

  registerUser( param: any){
      const header = new HttpHeaders().set('content-Type', 'application/json');
      return this.http.post('siteweb-flow/api/processInstance/create', param);
  }



  // 批量上传文件接口
  uploadFiles(files: File[]) {
    
      const formData = new FormData();
        console.log(files)
      // 将所有文件添加到FormData中
      files.forEach((file, index) => {
        console.log(file)
          formData.append('files', file);
      });
     const header = new HttpHeaders().set('type', 'only_auth');
      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData,{
      headers: header,
      responseType: 'text'
    });
  }



  // 测试单文件上传
  uploadSingleFile(file: File) {
      const formData = new FormData();
      formData.append('files', file);

      console.log('测试单文件上传:', file.name);
      console.log('文件类型:', file.type);
      console.log('文件大小:', file.size);
       const header = new HttpHeaders().set('type', 'only_auth');


      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData,{
      headers: header,
      responseType: 'text'
        });
//  const uuid = this.folder || _.uniqueId();

    //      return this.http.post(`files/${uuid}`, file, {
    //   headers: header,
    //   responseType: 'text'
    // });
  }

}
