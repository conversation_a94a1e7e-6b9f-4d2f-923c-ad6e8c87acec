import { SessionService } from '@app/core/services/session.service';
import { BaseCRUDService } from '@communal/providers/baseCRUDService/baseCRUDService';
import { ShareMessageService } from '../share-message.service';
import { BatteryCellOverviewService } from '@app/pages/monitor/battery/battery-cell-overview/battery-cell-overview.service';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { BatteryCellModel } from '@app/pages/monitor/battery/models/battery-cell.model';
import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { CustomBasicComponent } from '../../../../../basic/custom-basic/custom-basic.component';
import { ActivatedRoute } from '@angular/router';
import { CompEmitService } from '@app/@hmi/providers/comp-emit.service';
import { SettingObjComponent } from '@app/@hmi/models/setting-object.component';
import * as XLSX from 'xlsx';
import * as _ from 'lodash';
import { DevicesService } from '../../devices/devices.service';
import { RealtimeColumns } from './realtime-excel.model';
import { BatteryAlarmColumns } from './battery-alarm-excel.model';
import { CssVariableService } from '@app/theme/theme.css-variable.service';
import { AuthenticationService } from '@app/core/services/authentication.service';

@Component({
  selector: 'app-ngb-tab-template',
  templateUrl: './ngb-tab-template.component.html',
  styleUrls: ['./ngb-tab-template.component.scss']
})
export class NgbTabTemplateComponent  extends CustomBasicComponent
  implements OnInit, OnDestroy, SettingObjComponent{
  batteryCellModel: BatteryCellModel = new BatteryCellModel();
  baseUrl  = 'batterystringbasicsignals';
  lanKeys: string[];
  lanStrings: {};
  deviceId: any;
  params: any;
  status = '';
  currentPageDataTimer:any;
  timeoutId: any;
  time = 6000;
  alive = true;
  equiementControlState = 'true';

  realtimeList: any;
  alarmList: any;
  fileName = '';

  signalColumns = [
    'general.alarm.deviceName',
    'general.batteryCell.realTimeSignal.batteryCellId',
    'general.batteryCell.realTimeSignal.batteryCellName',
    'general.batteryCell.realTimeSignal.cellVoltage',
    'general.batteryCell.realTimeSignal.cellVoltageStatus',
    'general.batteryCell.realTimeSignal.cellTemp',
    'general.batteryCell.realTimeSignal.cellTempStatus',
    'general.batteryCell.realTimeSignal.cellIR',
    'general.batteryCell.realTimeSignal.cellIRStatus'
  ]

  alarmColumns = [
    'general.alarm.deviceName',
    'general.alarm.severityName',
    'general.alarm.corePointName',
    'general.alarm.occurValue',
    'general.alarm.birthTime',
    'general.alarm.confirmTime',
    'general.alarm.clearTime'
  ];

  baseListTableOption1:any;
  baseListTableOption2:any;

  signalCols = [
    {wch: 10},
    {wch: 10},
    {wch: 10},
    {wch: 10},
    {wch: 10},
    {wch: 10},
    {wch: 10},
    {wch: 10},
    {wch: 10}
  ];

  alarmCols = [
    {wch: 10},
    {wch: 15},
    {wch: 10},
    {wch: 10},
    {wch: 25},
    {wch: 25},
    {wch: 25}
]

  perPageTemp: number = 10
  perPage: number = 3
  isBytedaceWebEnable: boolean = false;

  constructor(
    private activatedRoute: ActivatedRoute,
    private eventService: CompEmitService,
    private router: Router,
    private route: ActivatedRoute,
    private translate: TranslateService,
    private baseService: BaseCRUDService,
    private messageService: ShareMessageService,
    private service: BatteryCellOverviewService,
    private sessionService: SessionService,
    private deviceService: DevicesService,
    private authService: AuthenticationService,
    private cssVariableService: CssVariableService
  ) {
    super(activatedRoute, eventService);
  }

  ngOnInit() {
    this.theme = localStorage.getItem('theme');
    this.initBasicData();
    this.initData();

    this.getSignalData();
    this.getAlarmData();
    this.setColumns();
    this.getBytedaceWebEnable();
  }

  ngOnDestroy() {
    this.service.setBatteryCellModel([]);
    this.messageService.send([]);
    this.clearTimer();
    this.alive = false;
    super.ngOnDestroy();
  }

  initData() {
    this.service.setBatteryCellModel([]);
    this.messageService.send([]);
    this.getLanStrings();
    this.getPageData();
    this.getSysOfEquimentControl();
    this.sub = this.eventService.getEventSubject().subscribe(event => {
      if (event && event['type'] === 'selBg' && event['data']) {
        this.settingCompTheme = event['data']['selectBg'];
      }
    });
  }

  // 窗口大小变化后
  resizeHandler(): void {
    const sizeIsChange = this.updatePageSizeIfChanged();
    // 当列表在第一页并且表格行数发生变化，用户调整窗口高度时，始终保持表格满屏输出。可能比较吃性能
    if (!sizeIsChange) return
    this.perPage = this.perPageTemp
  }

  /**
   * 更新表格高度并检查每页显示行数是否有变化
   * @returns 如果每页显示行数有变化则返回true，否则返回false
   */
  updatePageSizeIfChanged(): boolean {
    const tableContainer = document.getElementById('app-realtime');
    // 每页显示行数是否有变化
    let sizeIsChange = false
    if (tableContainer) {
      // 获取容器高度
      // TODO，刷新浏览器时，containerHeight总会多出32px
      const containerHeight = tableContainer.offsetHeight;
      // 更新表格每页显示的行数
      const newSize = this.getTablePageSize(containerHeight);
      if (newSize !== this.perPageTemp) {
        this.perPageTemp = newSize
        sizeIsChange = true
      }
    }
    return sizeIsChange
  }

  /**
   * 视图初始化后的生命周期钩子
   */
  ngAfterViewInit(): void {
    // TODO，刷新浏览器时，containerHeight总会多出32px，延时300毫秒可解决
    // 在组件内容初始化后调用一次，确保表格大小正确
    this.updatePageSizeIfChanged();
    // 初始化后更新一页显示多少条
    this.perPage = this.perPageTemp;
    [300, 600, 900, 1200].forEach(interval => {
      setTimeout(() => {
        this.resizeHandler();
      }, interval);
    });
  }

  /**
   * 计算表格页面的行数 = （内容区 - 表格头高度 - 滚动条高度 - 分页）/ 表格行高度
   * @param containerHeight - 表格容器的高度
   * @returns 表格页面的行数
   */
  getTablePageSize(containerHeight: number): number {
    // 计算可用高度
    const availableHeight = containerHeight - this.cssVariableService.getCssVariableNumber('--portal-table-header-height') - this.cssVariableService.getCssVariableNumber('--portal-scrollbar-height') - this.cssVariableService.getCssVariableNumber('--portal-pagination-height');
    // 计算并返回页面的行数：可用高度除以单元格高度，并向下取整
    return Math.floor(availableHeight / this.cssVariableService.getCssVariableNumber('--portal-table-cell-height'));
  }

  clearTimer() {
    if (this.timeoutId) {
      window.clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  getLanStrings(): void {
    this.lanKeys = [
      'general.common.status',
      'general.dashboard.onLine',
      'general.dashboard.offLine',
      'general.deviceSearch.devicePosition',
      'general.batteryCell.batteryCellCount'
    ];
    this.translate.get(this.lanKeys).subscribe(values => {
      this.lanStrings = values;
      this.getURLParams();
    });
  }

  getURLParams() {
    this.route.queryParams.subscribe(params => {
      if (params['objId'] || params['deviceId']) {
        this.deviceId = parseInt(params['objId']) || parseInt(params['deviceId']);
      }
    });
  }

  getPageData() {
    if(!this.alive || !this.deviceId){
      return;
    }
    const deviceUrl = '?equipmentId=' + this.deviceId;
    this.baseService.getAll(this.baseUrl + deviceUrl)
    .subscribe(res => {
        if (res) {
          this.batteryCellModel = res;
          this.batteryCellModel.totalVoltage = parseFloat(this.batteryCellModel.totalVoltage).toFixed(1);
          this.updateData();
        }
        if (this.timeoutId) {
          window.clearTimeout(this.timeoutId);
        }
        this.timeoutId = setTimeout(() => this.getPageData(), this.time);
    });
  }

  updateData() {
    if (this.batteryCellModel) {
      if (this.batteryCellModel.status === 1 && this.lanStrings) {
        this.status = this.lanStrings['general.dashboard.onLine'];
      } else if (this.lanStrings) {
        this.status = this.lanStrings['general.dashboard.offLine'];
      }
      this.service.setBatteryCellModel(this.batteryCellModel);
      this.messageService.send(this.batteryCellModel);
    }
  }


  //获取信号列表
  getSignalData(){
    this.messageService.get().subscribe(data => {
      if (data && data['batteryCellRealTimeSignalList']) {
        this.realtimeList = this.service.formatBatteryCellLivePoints(
          data['batteryCellRealTimeSignalList']
        );
      }
      return;
    });
  }

  //获取告警列表
  getAlarmData(){
    this.deviceService
        .getDeviceAlarmList(this.deviceId)
        .subscribe((res) => {
            if (res) {
                res.sort((x, y) => {
                    if (x.eventLevel !== y.eventLevel) {
                        return x.eventLevel - y.eventLevel;
                    } else {
                        return (
                            new Date(y.startTime).getTime() -
                            new Date(x.startTime).getTime()
                        );
                    }
                });
                this.alarmList = res.filter((item) => !item.masking);
            }
        });
  }

    /**
   * 获取系统设置，是否显示设备控制列表
   */
  getSysOfEquimentControl() {
    const equimentControlShow = this.sessionService.getSysconfigByKey('equimentControlShow');
    if (equimentControlShow) {
        this.equiementControlState = equimentControlShow['systemConfigValue'];
    }
  }

  //设置excel表头
  setColumns(){
    this.translate.get(this.signalColumns).subscribe((values) => {
        this.baseListTableOption1 = values;			
    });
    this.translate.get(this.alarmColumns).subscribe((values) => {
        this.baseListTableOption2 = values;			
    });
}

  /**
  * 导出数据
   */
  exportData(){
    this.fileName = this.batteryCellModel['batteryStringName'] + this.translate.instant('general.devicedetail.exportName') + ".xlsx";
    //导出实时数据
    const xlsTemplateJson1 = [];
    xlsTemplateJson1.push(_.map(this.baseListTableOption1));
    if(this.realtimeList){
      let count1 = 0;
      this.realtimeList.forEach((element) => {
          const rc = new RealtimeColumns();
          if(count1 === 0){
              rc.deviceName = this.batteryCellModel['batteryStringName'];
              count1 = 1;
          }else{
            rc.deviceName = '';
          }

          rc.batteryCellId = element.cellId;
          rc.batteryCellName = element.cellName;
          rc.cellVoltage = element.cellVoltageCurrentValue;
          rc.cellVoltageStatus = element.cellVoltageEventSeverity;
          rc.cellTemp = element.cellTemperatureCurrentValue;
          rc.cellTempStatus = element.cellTemperatureEventSeverity;
          rc.cellIR = element.cellIRCurrentValue;
          rc.cellIRStatus = element.cellIREventSeverity;

          xlsTemplateJson1.push(_.values(rc));
      });
    }
    const xlsTemplateJson2 = [];
    xlsTemplateJson2.push(_.map(this.baseListTableOption2));
    if(this.alarmList){
      let count2 = 0;
      this.realtimeList.forEach((element) => {
          const bc = new BatteryAlarmColumns();
          if(count2 === 0){
            bc.deviceName = this.batteryCellModel['batteryStringName'];
              count2 = 1;
          }else{
            bc.deviceName = '';
          }

          bc.severityName = element.severityName;
          bc.corePointName = element.corePointName;
          bc.occurValue = element.occurValue;
          bc.birthTime = element.birthTime;
          bc.confirmTime = element.confirmTime;
          bc.clearTime = element.clearTime;
          xlsTemplateJson2.push(_.values(bc));
      });
    }

    /* generate worksheet */
    const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson1);
    const ws2: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson2);
        
    ws['!cols'] = this.signalCols;
    ws2['!cols'] = this.alarmCols;
    /* generate workbook */
    const wb: XLSX.WorkBook = XLSX.utils.book_new();
    /* add the worksheet */
    XLSX.utils.book_append_sheet(wb, ws, this.translate.instant('common.signal'));
    XLSX.utils.book_append_sheet(wb, ws2, this.translate.instant('common.alarm'));
    /* save to file */
    if(this.realtimeList || this.alarmList){
      XLSX.writeFile(wb, this.fileName);
    }
  }

  getBytedaceWebEnable(): void {
    this.authService.getAuthenticationJson().subscribe(
      (config: any) => {
        if (config && config['bytedace.web.enable']) {
          this.isBytedaceWebEnable = true;
        }
      }
    );
  }

  // 拖拽辅助线边框位置 -1px，因为辅助线宽度1px
  expandUnit(param, other?: string) {
    if (!this.isBytedaceWebEnable && param === 'height') {
      return '';
    }
    let paramVal =
        (!other
            ? this.style[param]
            : this.style[param] + this.style[other] - 0) || 0; //无边框计算方式
    paramVal = this.hasBorderWidth(paramVal, param, other);
    return paramVal + 'px';
  }
}
