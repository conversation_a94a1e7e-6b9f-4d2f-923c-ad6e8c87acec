import { Routes, RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/core';
import { PermissionGuard } from '@communal/guard/permission.guard';
import { EntryComponent } from './entry.component';
import { AlarmOverviewComponent } from '@app/pages/monitor/alarm-overview/alarm-overview.component';
import { TopographyComponent } from '@app/communal/components/topography/topography.component';
import { CameraComponent } from '@app/communal/components/camera/camera.component';
import { RealCameraComponent } from '@app/pages/video-management/real-camera/real-camera.component';
import { AuthGuard } from '@communal/guard/auth.guard';
import { UpdatePasswordComponent } from '@app/pages/admin/personal/update-password/update-password.component';
import { ForgetPasswordComponent } from '@app/pages/admin/personal/forget-password/forget-password.component';
import { LicenseGuard } from '@app/communal/guard/license.guard';
import { EmbedIframeComponent } from '@app/core/components/embed-iframe/embed-iframe.component';
import { RegisterUserComponent } from '@app/pages/admin/personal/register-user/register-user.component';

export const routes: Routes = [
  {
    path: 'signin',
    loadChildren: () => import('@app/pages/signin/signin.module').then((m) => m.SigninModule),
  },
  {
    path: 'page-not-found',
    loadChildren: () => import('app/pages/page-not-found/page-not-found.module').then((m) => m.PageNotFoundModule),
  },
  {
    path: 'updatePassword',
    component: UpdatePasswordComponent,
    data: {
      title: 'general.menu.updatePassword'
    }
  },
  {
    path: 'forgetpassword',
    component: ForgetPasswordComponent,
    data: {
      title: 'general.menu.forgetPassword'
    }
  },
  {
    path: 'registeruser',
    component: RegisterUserComponent,
    data: {
      title: 'general.menu.register'
    }
  },
  {
    path: 'entry',
    canActivateChild: [PermissionGuard, AuthGuard, LicenseGuard],
    component: EntryComponent,
    children: [
      {
        path: 'usermanagement',
        loadChildren: () =>
          import('@admin/user-management/user-management.module').then((m) => m.UserManagementModule),
      },
      {
        path: 'usermanagement',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@admin/user-management/user-management.module').then((m) => m.UserManagementModule),
      },
      {
        path: 'usermanagement',
        outlet: 'topRight',
        loadChildren: () =>
          import('@admin/user-management/user-management.module').then((m) => m.UserManagementModule),
      },
      {
        path: 'usermanagement',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@admin/user-management/user-management.module').then((m) => m.UserManagementModule),
      },
      {
        path: 'usermanagement',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@admin/user-management/user-management.module').then((m) => m.UserManagementModule),
      },
      {
        path: 'alarmoverview',
        data: {
          title: 'general.menu.alarmOverview',
        },
        component: AlarmOverviewComponent,
      },
      {
        path: 'alarmoverview',
        outlet: 'topLeft',
        data: {
          title: 'general.menu.alarmOverview',
        },
        component: AlarmOverviewComponent,
      },
      {
        path: 'alarmoverview',
        outlet: 'topRight',
        data: {
          title: 'general.menu.alarmOverview',
        },
        component: AlarmOverviewComponent,
      },
      {
        path: 'alarmoverview',
        outlet: 'bottomLeft',
        data: {
          title: 'general.menu.alarmOverview',
        },
        component: AlarmOverviewComponent,
      },
      {
        path: 'alarmoverview',
        outlet: 'bottomRight',
        data: {
          title: 'general.menu.alarmOverview',
        },
        component: AlarmOverviewComponent,
      },
      {
        path: 'alarms',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/monitor/alarm-list/alarm-list.module').then((m) => m.AlarmListModule),
      },
      {
        path: 'alarms',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/monitor/alarm-list/alarm-list.module').then((m) => m.AlarmListModule),
      },
      {
        path: 'alarms',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/monitor/alarm-list/alarm-list.module').then((m) => m.AlarmListModule),
      },
      {
        path: 'alarms',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/monitor/alarm-list/alarm-list.module').then((m) => m.AlarmListModule),
      },
      {
        path: 'alarms',
        loadChildren: () => import('app/pages/monitor/alarm-list/alarm-list.module').then((m) => m.AlarmListModule),
      },
      {
        path: 'organizations/permissions',
        loadChildren: () => import('app/pages/admin/team/team.module').then((m) => m.TeamModule),
      },
      {
        path: 'organizations/permissions',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/admin/team/team.module').then((m) => m.TeamModule),
      },
      {
        path: 'organizations/permissions',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/admin/team/team.module').then((m) => m.TeamModule),
      },
      {
        path: 'organizations/permissions',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/admin/team/team.module').then((m) => m.TeamModule),
      },
      {
        path: 'organizations/permissions',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/admin/team/team.module').then((m) => m.TeamModule),
      },
      {
        path: 'selfprocotocol',
        loadChildren: () => import('app/pages/backstage/self-procotocol-config/self-procotocol-config.module').then((m) => m.SelfProcotocolConfigModule),
      },
      {
        path: 'selfprocotocol',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/backstage/self-procotocol-config/self-procotocol-config.module').then((m) => m.SelfProcotocolConfigModule),
      },
      {
        path: 'selfprocotocol',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/backstage/self-procotocol-config/self-procotocol-config.module').then((m) => m.SelfProcotocolConfigModule),
      },
      {
        path: 'selfprocotocol',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/backstage/self-procotocol-config/self-procotocol-config.module').then((m) => m.SelfProcotocolConfigModule),
      },
      {
        path: 'selfprocotocol',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/backstage/self-procotocol-config/self-procotocol-config.module').then((m) => m.SelfProcotocolConfigModule),
      },
      {
        path: 'ubitmanagement',
        loadChildren: () => import('app/pages/ubit-management/ubit-management.module').then((m) => m.UbitManagementModule),
      },
      {
        path: 'ubitmanagement',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/ubit-management/ubit-management.module').then((m) => m.UbitManagementModule),
      },
      {
        path: 'ubitmanagement',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/ubit-management/ubit-management.module').then((m) => m.UbitManagementModule),
      },
      {
        path: 'ubitmanagement',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/ubit-management/ubit-management.module').then((m) => m.UbitManagementModule),
      },
      {
        path: 'ubitmanagement',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/ubit-management/ubit-management.module').then((m) => m.UbitManagementModule),
      },
      {
        path: 'organizations/persons',
        loadChildren: () => import('app/pages/admin/human/human.module').then((m) => m.HumanModule),
      },
      {
        path: 'organizations/persons',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/admin/human/human.module').then((m) => m.HumanModule),
      },
      {
        path: 'organizations/persons',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/admin/human/human.module').then((m) => m.HumanModule),
      },
      {
        path: 'organizations/persons',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/admin/human/human.module').then((m) => m.HumanModule),
      },
      {
        path: 'organizations/persons',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/admin/human/human.module').then((m) => m.HumanModule),
      },
      {
        path: 'organizations/groups',
        loadChildren: () => import('app/pages/admin/person-group/person-group.module').then((m) => m.PersonGroupModule),
      },
      {
        path: 'organizations/groups',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/admin/person-group/person-group.module').then((m) => m.PersonGroupModule),
      },
      {
        path: 'organizations/groups',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/admin/person-group/person-group.module').then((m) => m.PersonGroupModule),
      },
      {
        path: 'organizations/groups',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/admin/person-group/person-group.module').then((m) => m.PersonGroupModule),
      },
      {
        path: 'organizations/groups',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/admin/person-group/person-group.module').then((m) => m.PersonGroupModule),
      },
      {
        path: 'organizations/customer',
        loadChildren: () => import('app/pages/admin/customer-manage/customer-manage.module').then((m) => m.CustomerManageModule),
      },
      {
        path: 'organizations/customer',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/admin/customer-manage/customer-manage.module').then((m) => m.CustomerManageModule),
      },
      {
        path: 'organizations/customer',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/admin/customer-manage/customer-manage.module').then((m) => m.CustomerManageModule),
      },
      {
        path: 'organizations/customer',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/admin/customer-manage/customer-manage.module').then((m) => m.CustomerManageModule),
      },
      {
        path: 'organizations/customer',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/admin/customer-manage/customer-manage.module').then((m) => m.CustomerManageModule),
      },
      {
        path: 'airconditioninggroupcontrol/overview',
        loadChildren: () => import('app/pages/monitor/air-conditioner/air-conditioner-group-overview/air-conditioner-group-overview.module').then((m) => m.AirConditionerGroupOverviewModule),
      },
      {
        path: 'airconditioninggroupcontrol/model',
        loadChildren: () => import('app/pages/monitor/air-conditioner/air-conditioner-shadow/air-conditioner-shadow.module').then((m) => m.AirConditionerShadowModule),
      },
      {
        path: 'airconditioninggroupcontrol/setting',
        loadChildren: () => import('app/pages/monitor/air-conditioner/air-conditioner-group/air-conditioner-group.module').then((m) => m.AirConditionerGroupModule),
      },
      {
        path: 'battery/batterystringoverview',
        data: {
          title: '',
        },
        loadChildren: () => import('app/pages/monitor/battery/battery-string-overview/battery-string-overview.module').then((m) => m.BatteryStringOverviewModule),
      },
      {
        path: 'battery/batterycelloverview',
        data: {
          title: '',
        },
        loadChildren: () => import('app/pages/monitor/battery/battery-cell-overview/battery-cell-overview.module').then((m) => m.BatteryCellOverviewModule),
      },
      {
        path: 'battery/batteryModel',
        loadChildren: () => import('app/pages/monitor/battery/battery-model/battery-model.module').then((m) => m.BatteryModelModule),
      },
      {
        path: 'battery/batteryString',
        loadChildren: () => import('app/pages/monitor/battery/battery-string/battery-string.module').then((m) => m.BatteryStringModule),
      },
      {
        path: 'featuremanage/battery/batteryModel',
        loadChildren: () => import('app/pages/monitor/battery/battery-model/battery-model.module').then((m) => m.BatteryModelModule),
      },
      {
        path: 'featuremanage/battery/batteryString',
        loadChildren: () => import('app/pages/monitor/battery/battery-string/battery-string.module').then((m) => m.BatteryStringModule),
      },
      {
        path: 'monitor',
        loadChildren: () => import('app/pages/monitor/monitor.module').then((m) => m.MonitorModule),
      },
      {
        path: 'monitor',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/monitor/monitor.module').then((m) => m.MonitorModule),
      },
      {
        path: 'monitor',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/monitor/monitor.module').then((m) => m.MonitorModule),
      },
      {
        path: 'monitor',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/monitor/monitor.module').then((m) => m.MonitorModule),
      },
      {
        path: 'monitor',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/monitor/monitor.module').then((m) => m.MonitorModule),
      },
      {
        path: 'idcmanage',
        loadChildren: () => import('app/pages/idc-manage/idc-manage.module').then((m) => m.IdcManageModule),
      },
      {
        path: 'idcmanage',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/idc-manage/idc-manage.module').then((m) => m.IdcManageModule),
      },
      {
        path: 'idcmanage',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/idc-manage/idc-manage.module').then((m) => m.IdcManageModule),
      },
      {
        path: 'idcmanage',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/idc-manage/idc-manage.module').then((m) => m.IdcManageModule),
      },
      {
        path: 'idcmanage',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/idc-manage/idc-manage.module').then((m) => m.IdcManageModule),
      },
      {
        path: 'systemmanage/systeminternalconfig',
        loadChildren: () => import('app/pages/backstage/extends/extends.module').then((m) => m.ExtendsModule),
      },
      {
        path: 'systemmanage/standardspecification',
        loadChildren: () => import('app/pages/backstage/misc/basePointDictionary/basePointDictionary.module').then((m) => m.BasePointDictionaryModule),
      },
      {
        path: 'featuremanage/monitor/alarmconfig',
        loadChildren: () => import('app/pages/backstage/featuremanage/alarmconfig/alarmconfig.module').then((m) => m.AlarmconfigModule),
      },
      {
        path: 'featuremanage/monitor/device-signal-setting',
        loadChildren: () => import('app/pages/monitor/device-singal-setting/device-singal-setting.module').then((m) => m.DeviceSingalSettingModule),
      },
      {
        path: 'featuremanage/rackindicatorconfig',
        loadChildren: () => import('app/pages/backstage/featuremanage/rack-indicator-config/rack-indicator-config.module').then((m) => m.RackIndicatorConfigModule),
      },
      {
        path: 'featuremanage/indicatortemplate',
        loadChildren: () => import('app/pages/backstage/featuremanage/indicator-template/indicator-template.module').then((m) => m.IndicatorTemplateModule),
      },
      {
        path: 'systemmanage/systemconfig',
        loadChildren: () => import('app/pages/backstage/misc/misc.module').then((m) => m.MiscModule),
      },
      {
        path: 'systemmanage/themelogo',
        loadChildren: () => import('app/pages/backstage/misc/logo/logo.module').then((m) => m.LogoModule),
      },
      {
        path: 'systemmanage/scene',
        loadChildren: () => import('app/pages/backstage/misc/scene/scene.module').then((m) => m.SceneModule),
      },
      {
        path: 'systemmanage/versioninfo',
        loadChildren: () => import('app/pages/backstage/misc/version-info/version-info.module').then((m) => m.VersionInfoModule),
      },
      {
        path: 'systemmanage/pageConfig',
        loadChildren: () =>
          import('app/pages/backstage/misc/page-config/page-config.module').then((m) => m.PageConfigModule),
      },
      {
        path: 'systemmanage/mainpageconfig',
        loadChildren: () =>
          import('@app/pages/backstage/misc/main-page-config/main-page-config.module').then((m) => m.MainPageConfigModule),
      },
      {
        path: 'time',
        loadChildren: () => import('app/pages/backstage/time/time.module').then((m) => m.TimeModule),
      },
      {
        path: 'time',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/backstage/time/time.module').then((m) => m.TimeModule),
      },
      {
        path: 'time',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/backstage/time/time.module').then((m) => m.TimeModule),
      },
      {
        path: 'time',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/backstage/time/time.module').then((m) => m.TimeModule),
      },
      {
        path: 'time',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/backstage/time/time.module').then((m) => m.TimeModule),
      },
      {
        path: 'misc',
        loadChildren: () => import('app/pages/backstage/misc/misc.module').then((m) => m.MiscModule),
      },
      {
        path: 'safe',
        loadChildren: () => import('app/pages/backstage/safe/safe.module').then((m) => m.SafeModule),
      },
      {
        path: 'safe',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/backstage/safe/safe.module').then((m) => m.SafeModule),
      },
      {
        path: 'safe',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/backstage/safe/safe.module').then((m) => m.SafeModule),
      },
      {
        path: 'safe',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/backstage/safe/safe.module').then((m) => m.SafeModule),
      },
      {
        path: 'safe',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/backstage/safe/safe.module').then((m) => m.SafeModule),
      },
      {
        path: 'extends',
        loadChildren: () => import('app/pages/backstage/extends/extends.module').then((m) => m.ExtendsModule),
      },
      {
        path: 'extends',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/backstage/extends/extends.module').then((m) => m.ExtendsModule),
      },
      {
        path: 'extends',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/backstage/extends/extends.module').then((m) => m.ExtendsModule),
      },
      {
        path: 'extends',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/backstage/extends/extends.module').then((m) => m.ExtendsModule),
      },
      {
        path: 'extends',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/backstage/extends/extends.module').then((m) => m.ExtendsModule),
      },
      {
        path: 'assetc',
        loadChildren: () => import('app/pages/backstage/assetc/assetc.module').then((m) => m.AssetcModule),
      },
      {
        path: 'systemmanage/custommenu',
        loadChildren: () => import('app/pages/backstage/menu/menu.module').then((m) => m.MenuDefineModule),
      },
      {
        path: 'unit',
        loadChildren: () => import('app/pages/backstage/unit/unit.module').then((m) => m.UnitModule),
      },
      {
        path: 'cron',
        loadChildren: () => import('app/pages/backstage/cron/cron.module').then((m) => m.CronModule),
      },
      {
        path: 'basicinfo',
        loadChildren: () => import('@app/pages/admin/personal/base-information.module').then((m) => m.BaseInformationModule),
      },
      {
        path: 'onlineUser',
        loadChildren: () => import('@app/pages/admin/personal/online-user/online-user.module').then((m) => m.OnlineUserModule),
      },
      {
        path: 'usersetting',
        loadChildren: () => import('@app/pages/admin/personal/user-setting/user-setting.module').then((m) => m.UserSettingModule),
      },
      {
        path: 'rackEquipment',
        loadChildren: () => import('@app/pages/idc-manage/components/rackEquipment/rack-equipment.module').then((m) => m.RackEquipmentModule),
      },
      {
        path: 'rackmanage',
        loadChildren: () => import('@app/pages/idc-manage/components/rack-manage-hmi/rack-manage-hmi.module').then((m) => m.RackManageHmiModule),
      },
      {
        path: 'rackcustomermanage',
        loadChildren: () => import('@app/pages/idc-manage/components/rack-chart/rack-chart.module').then((m) => m.RackChartModule),
      },
      {
        path: 'hmiedit',
        loadChildren: () => import('@app/@hmi/hmi.module').then((m) => m.HmiModule),
      },
      {
        path: 'hmi',
        loadChildren: () => import('@app/pages/hmi/diagrampage.module').then((m) => m.DiagrampageModule),
      },
      {
        path: 'hmi',
        outlet: 'bottomLeft',
        loadChildren: () => import('@app/pages/hmi/diagrampage.module').then((m) => m.DiagrampageModule),
      },
      {
        path: 'hmi',
        outlet: 'bottomRight',
        loadChildren: () => import('@app/pages/hmi/diagrampage.module').then((m) => m.DiagrampageModule),
      },
      {
        path: 'hmi',
        outlet: 'topLeft',
        loadChildren: () => import('@app/pages/hmi/diagrampage.module').then((m) => m.DiagrampageModule),
      },
      {
        path: 'hmi',
        outlet: 'topRight',
        loadChildren: () => import('@app/pages/hmi/diagrampage.module').then((m) => m.DiagrampageModule),
      },
      {
        path: 'report',
        loadChildren: () => import('@app/pages/report/report.module').then((m) => m.ReportModule),
      },
      {
        path: 'report',
        outlet: 'topLeft',
        loadChildren: () => import('@app/pages/report/report.module').then((m) => m.ReportModule),
      },
      {
        path: 'report',
        outlet: 'topRight',
        loadChildren: () => import('@app/pages/report/report.module').then((m) => m.ReportModule),
      },
      {
        path: 'report',
        outlet: 'bottomLeft',
        loadChildren: () => import('@app/pages/report/report.module').then((m) => m.ReportModule),
      },
      {
        path: 'report',
        outlet: 'bottomRight',
        loadChildren: () => import('@app/pages/report/report.module').then((m) => m.ReportModule),
      },
      {
        path: 'reportmanagement',
        loadChildren: () =>
          import('@app/pages/report/report-category/report-management.module').then(
            (m) => m.ReportManagementModule
          ),
      },
      {
        path: 'reporttree',
        loadChildren: () => import('@app/pages/report-tree/report-tree.module').then((m) => m.ReportTreeModule),
      },
      {
        path: 'topologysystem',
        loadChildren: () =>
          import('@app/pages/topology-system/topology-system.module').then((m) => m.TopologySystemV2Module),
      },
      {
        path: 'topologysystem',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/topology-system/topology-system.module').then((m) => m.TopologySystemV2Module),
      },
      {
        path: 'topologysystem',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/topology-system/topology-system.module').then((m) => m.TopologySystemV2Module),
      },
      {
        path: 'topologysystem',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/topology-system/topology-system.module').then((m) => m.TopologySystemV2Module),
      },
      {
        path: 'topologysystem',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/topology-system/topology-system.module').then((m) => m.TopologySystemV2Module),
      },
      {
        path: 'topography',
        component: TopographyComponent,
      },
      {
        path: 'topography',
        outlet: 'topLeft',
        component: TopographyComponent,
      },
      {
        path: 'topography',
        outlet: 'topRight',
        component: TopographyComponent,
      },
      {
        path: 'topography',
        outlet: 'bottomLeft',
        component: TopographyComponent,
      },
      {
        path: 'topography',
        outlet: 'bottomRight',
        component: TopographyComponent,
      },
      {
        path: 'embediframe',
        component: EmbedIframeComponent,
      },
      {
        path: 'embediframe',
        outlet: 'topLeft',
        component: EmbedIframeComponent,
      },
      {
        path: 'embediframe',
        outlet: 'topRight',
        component: EmbedIframeComponent,
      },
      {
        path: 'embediframe',
        outlet: 'bottomLeft',
        component: EmbedIframeComponent,
      },
      {
        path: 'embediframe',
        outlet: 'bottomRight',
        component: EmbedIframeComponent,
      },
      {
        path: 'assetdevice',
        loadChildren: () => import('app/pages/asset/asset-device/asset-device.module').then((m) => m.AssetDeviceModule),
      },
      {
        path: 'assetdevice',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/asset/asset-device/asset-device.module').then((m) => m.AssetDeviceModule),
      },
      {
        path: 'assetdevice',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/asset/asset-device/asset-device.module').then((m) => m.AssetDeviceModule),
      },
      {
        path: 'assetdevice',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/asset/asset-device/asset-device.module').then((m) => m.AssetDeviceModule),
      },
      {
        path: 'assetdevice',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/asset/asset-device/asset-device.module').then((m) => m.AssetDeviceModule),
      },
      // {
      //   path: 'featuremanage/assetdevice',
      //   loadChildren: () => import('app/pages/asset/asset-device/asset-device.module').then((m) => m.AssetDeviceModule),
      // },
      {
        path: 'featuremanage/distributiontopology/relationship',
        loadChildren: () => import('app/pages/alarm-fitting/distribution-relationship/distribution-relationship.module').then((m) => m.DistributionRelationshipModule),
      },
      {
        path: 'assetsoverview',
        loadChildren: () => import('app/pages/asset/asset-device-overview/asset-device-overview.module').then((m) => m.AssetDeviceOverviewModule),
      },
      // {
      //   path: 'featuremanage/assetcategory',
      //   loadChildren: () => import('app/pages/asset/asset-category/asset-category.module').then((m) => m.AssetCategoryModule),
      // },
      // {
      //   path: 'featuremanage/assetextend',
      //   loadChildren: () => import('app/pages/asset/asset-extend/asset-extend.module').then((m) => m.AssetExtendModule),
      // },    
      {
        path: 'ai',
        loadChildren: () =>
          import(
            '@app/pages/monitor/air-conditioner/air-conditioner-corepoint/air-conditioner-corepoint.module'
          ).then((m) => m.AirConditionerCorepointModule),
      },
      {
        path: 'device',
        loadChildren: () => import('app/pages/devices/devices.module').then((m) => m.DevicesModule),
      },
      {
        path: 'device',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/devices/devices.module').then((m) => m.DevicesModule),
      },
      {
        path: 'device',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/devices/devices.module').then((m) => m.DevicesModule),
      },
      {
        path: 'device',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/devices/devices.module').then((m) => m.DevicesModule),
      },
      {
        path: 'device',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/devices/devices.module').then((m) => m.DevicesModule),
      },
      {
        path: 'forewarmingmanagement',
        loadChildren: () => import('app/pages/forewarning-management/forewarming-management.module').then((m) => m.ForewarmingManagementModule),
      },
      {
        path: 'forewarmingmanagement',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/forewarning-management/forewarming-management.module').then((m) => m.ForewarmingManagementModule),
      },
      {
        path: 'forewarmingmanagement',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/forewarning-management/forewarming-management.module').then((m) => m.ForewarmingManagementModule),
      },
      {
        path: 'forewarmingmanagement',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/forewarning-management/forewarming-management.module').then((m) => m.ForewarmingManagementModule),
      },
      {
        path: 'forewarmingmanagement',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/forewarning-management/forewarming-management.module').then((m) => m.ForewarmingManagementModule),
      },
      {
        path: 'featuremanage/forewarmingmanagement',
        loadChildren: () => import('app/pages/forewarning-management/forewarming-management.module').then((m) => m.ForewarmingManagementModule),
      },
      {
        path: 'mask',
        loadChildren: () => import('app/pages/mask/mask.module').then((m) => m.MaskModule),
      },
      {
        path: 'mask',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/mask/mask.module').then((m) => m.MaskModule),
      },
      {
        path: 'mask',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/mask/mask.module').then((m) => m.MaskModule),
      },
      {
        path: 'mask',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/mask/mask.module').then((m) => m.MaskModule),
      },
      {
        path: 'mask',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/mask/mask.module').then((m) => m.MaskModule),
      },
      {
        path: 'alarmnotification',
        loadChildren: () => import('app/pages/alarm-notification/alarm-notification.module').then((m) => m.AlarmNotificationModule),
      },
      {
        path: 'alarmnotification',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/alarm-notification/alarm-notification.module').then((m) => m.AlarmNotificationModule),
      },
      {
        path: 'alarmnotification',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/alarm-notification/alarm-notification.module').then((m) => m.AlarmNotificationModule),
      },
      {
        path: 'alarmnotification',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/alarm-notification/alarm-notification.module').then((m) => m.AlarmNotificationModule),
      },
      {
        path: 'alarmnotification',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/alarm-notification/alarm-notification.module').then((m) => m.AlarmNotificationModule),
      },
      {
        path: 'tts',
        loadChildren: () => import('app/pages/tts/tts.module').then((m) => m.TtsModule),
      },
      {
        path: 'tts',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/tts/tts.module').then((m) => m.TtsModule),
      },
      {
        path: 'tts',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/tts/tts.module').then((m) => m.TtsModule),
      },
      {
        path: 'tts',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/tts/tts.module').then((m) => m.TtsModule),
      },
      {
        path: 'tts',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/tts/tts.module').then((m) => m.TtsModule),
      },
      {
        path: 'alarmvideolink',
        loadChildren: () => import('app/pages/alarm-video-link/alarm-video-link.module').then((m) => m.AlarmVideoLinkModule),
      },
      {
        path: 'alarmvideolink',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/alarm-video-link/alarm-video-link.module').then((m) => m.AlarmVideoLinkModule),
      },
      {
        path: 'alarmvideolink',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/alarm-video-link/alarm-video-link.module').then((m) => m.AlarmVideoLinkModule),
      },
      {
        path: 'alarmvideolink',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/alarm-video-link/alarm-video-link.module').then((m) => m.AlarmVideoLinkModule),
      },
      {
        path: 'alarmvideolink',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/alarm-video-link/alarm-video-link.module').then((m) => m.AlarmVideoLinkModule),
      },
      {
        path: 'chartdataseparateconfig',
        loadChildren: () => import('app/pages/chart-data-separate-config/chart-data-separate-config.module').then((m) => m.ChartDataSeparateConfigModule),
      },
      {
        path: 'shiftmanagement',
        loadChildren: () => import('app/pages/shift-management/shift-management.module').then((m) => m.ShiftManagementModule),
      },
      {
        path: 'robotmaintain/robotvisitormanagement',
        loadChildren: () => import('app/pages/robot-visitor-management/robot-visitor-management.module').then(m => m.RobotVisitorManagementModule),
      },
      {
        path: 'robottask',
        loadChildren: () =>
          import('app/pages/robot-task/robot-task.module').then(
            (m) => m.RobotTaskModule
          ),
      },
      {
        path: 'robotalarm',
        loadChildren: () =>
          import('@app/pages/robot-alarm/robot-alarm.module').then(
            (m) => m.RobotAlarmModule
          ),
      },
      {
        path: 'robotchart',
        loadChildren: () =>
          import('@app/pages/robot-chart/robot-chart.module').then(
            (m) => m.RobotChartModule
          ),
      },
      {
        path: 'linkagestrategy',
        loadChildren: () => import('app/pages/linkage-strategy/linkage-strategy.module').then((m) => m.LinkageStrategyModule),
      },
      {
        path: 'linkagestrategy',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/linkage-strategy/linkage-strategy.module').then((m) => m.LinkageStrategyModule),
      },
      {
        path: 'linkagestrategy',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/linkage-strategy/linkage-strategy.module').then((m) => m.LinkageStrategyModule),
      },
      {
        path: 'linkagestrategy',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/linkage-strategy/linkage-strategy.module').then((m) => m.LinkageStrategyModule),
      },
      {
        path: 'linkagestrategy',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/linkage-strategy/linkage-strategy.module').then((m) => m.LinkageStrategyModule),
      },
      {
        path: 's2',
        loadChildren: () =>
          import('app/pages/site/sitewebtwo.module').then(
            (m) => m.SitewebTwoModule
          ),
      },
      {
        path: 's2',
        outlet: 'topLeft',
        loadChildren: () =>
          import('app/pages/site/sitewebtwo.module').then(
            (m) => m.SitewebTwoModule
          ),
      },
      {
        path: 's2',
        outlet: 'topRight',
        loadChildren: () =>
          import('app/pages/site/sitewebtwo.module').then(
            (m) => m.SitewebTwoModule
          ),
      },
      {
        path: 's2',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('app/pages/site/sitewebtwo.module').then(
            (m) => m.SitewebTwoModule
          ),
      },
      {
        path: 's2',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('app/pages/site/sitewebtwo.module').then(
            (m) => m.SitewebTwoModule
          ),
      },
      {
        path: 'idcautopatrol',
        loadChildren: () =>
          import('app/pages/site/idc-auto-patrol/idc-auto-patrol.module').then(
            (m) => m.IdcAutoPatrolModule
          ),
      },
      {
        path: 'video',
        loadChildren: () =>
          import('app/pages/video/video.module').then(
            (m) => m.VideoModule
          ),
      },
      {
        path: 'video',
        outlet: 'topLeft',
        loadChildren: () =>
          import('app/pages/video/video.module').then(
            (m) => m.VideoModule
          ),
      },
      {
        path: 'video',
        outlet: 'topRight',
        loadChildren: () =>
          import('app/pages/video/video.module').then(
            (m) => m.VideoModule
          ),
      },
      {
        path: 'video',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('app/pages/video/video.module').then(
            (m) => m.VideoModule
          ),
      },
      {
        path: 'video',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('app/pages/video/video.module').then(
            (m) => m.VideoModule
          ),
      },
      {
        path: 'capacitysystem',
        loadChildren: () => import('app/pages/capacity-management/capacity-system.module').then((m) => m.CapacitySystemModule),
      },
      {
        path: 'capacitysystem',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/capacity-management/capacity-system.module').then((m) => m.CapacitySystemModule),
      },
      {
        path: 'capacitysystem',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/capacity-management/capacity-system.module').then((m) => m.CapacitySystemModule),
      },
      {
        path: 'capacitysystem',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/capacity-management/capacity-system.module').then((m) => m.CapacitySystemModule),
      },
      {
        path: 'capacitysystem',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/capacity-management/capacity-system.module').then((m) => m.CapacitySystemModule),
      },
      {
        path: 'prealarmmanage',
        loadChildren: () => import('app/pages/pre-alarm/pre-alarm.module').then((m) => m.PreAlarmModule),
      },
      {
        path: 'camera',
        component: CameraComponent,
      },
      {
        path: 'camera',
        outlet: 'topLeft',
        component: CameraComponent,
      },
      {
        path: 'camera',
        outlet: 'topRight',
        component: CameraComponent,
      },
      {
        path: 'camera',
        outlet: 'bottomLeft',
        component: CameraComponent,
      },
      {
        path: 'camera',
        outlet: 'bottomRight',
        component: CameraComponent,
      },
      {
        path: 'realcamera',
        loadChildren: () =>
          import('@app/pages/video-management/real-camera/real-camera.module').then(
            (m) => m.RealCameraModule
          ),
      },
      {
        path: 'realcamera',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/video-management/real-camera/real-camera.module').then(
            (m) => m.RealCameraModule
          ),
      },
      {
        path: 'realcamera',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/video-management/real-camera/real-camera.module').then(
            (m) => m.RealCameraModule
          ),
      },
      {
        path: 'realcamera',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/video-management/real-camera/real-camera.module').then(
            (m) => m.RealCameraModule
          ),
      },
      {
        path: 'realcamera',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/video-management/real-camera/real-camera.module').then(
            (m) => m.RealCameraModule
          ),
      },
      {
        path: 'dynamicconfig',
        loadChildren: () => import('app/pages/monitor/dynamic-config/dynamic-config.module').then((m) => m.DynamicConfigModule),
      },
      {
        path: 'alarmnotification/notificationpolicy/flowtool',
        loadChildren: () => import('@app/pages/alarm-notification/flow-component/flow-component.module').then((m) => m.FlowComponentModule),
      },
      {
        path: 'linkagestrategy/lsflow/tool',
        loadChildren: () => import('app/pages/linkage-strategy/flow-linkage-strategy/ls-flow-component.module').then((m) => m.LSFlowComponentModule),
      },
      {
        path: 'accesscontrol',
        loadChildren: () =>
          import('@app/pages/access-control/access-control.module').then(
            (m) => m.TAccessControlModule
          ),
      },
      {
        path: 'accesscontrol',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/access-control/access-control.module').then(
            (m) => m.TAccessControlModule
          ),
      },
      {
        path: 'accesscontrol',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/access-control/access-control.module').then(
            (m) => m.TAccessControlModule
          ),
      },
      {
        path: 'accesscontrol',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/access-control/access-control.module').then(
            (m) => m.TAccessControlModule
          ),
      },
      {
        path: 'accesscontrol',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/access-control/access-control.module').then(
            (m) => m.TAccessControlModule
          ),
      },
      {
        path: 'accesscontrol-plus',
        loadChildren: () =>
          import('@app/pages/access-control-plus/access-control.module').then(
            (m) => m.TAccessPlusControlModule
          ),
      },
      {
        path: 'accesscontrol-plus',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/access-control-plus/access-control.module').then(
            (m) => m.TAccessPlusControlModule
          ),
      },
      {
        path: 'accesscontrol-plus',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/access-control-plus/access-control.module').then(
            (m) => m.TAccessPlusControlModule
          ),
      },
      {
        path: 'accesscontrol-plus',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/access-control-plus/access-control.module').then(
            (m) => m.TAccessPlusControlModule
          ),
      },
      {
        path: 'accesscontrol-plus',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/access-control-plus/access-control.module').then(
            (m) => m.TAccessPlusControlModule
          ),
      },
      {
        path: 'energypanorama',
        loadChildren: () =>
          import('@app/pages/energy-management/energy-panorama/energy-panorama.module').then(
            (m) => m.EnergyPanoramaModule
          ),
      },
      {
        path: 'energypanorama',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/energy-management/energy-panorama/energy-panorama.module').then(
            (m) => m.EnergyPanoramaModule
          ),
      },
      {
        path: 'energypanorama',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/energy-management/energy-panorama/energy-panorama.module').then(
            (m) => m.EnergyPanoramaModule
          ),
      },
      {
        path: 'energypanorama',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/energy-management/energy-panorama/energy-panorama.module').then(
            (m) => m.EnergyPanoramaModule
          ),
      },
      {
        path: 'energypanorama',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/energy-management/energy-panorama/energy-panorama.module').then(
            (m) => m.EnergyPanoramaModule
          ),
      },
      {
        path: 'energyoperate',
        loadChildren: () => import('app/pages/energy-management/operate-management/operate-management.module').then((m) => m.OperateManagementModule),
      },
      {
        path: 'saveenergy/energyinfo',
        loadChildren: () => import('app/pages/energy-management/saveenergy-management/energy-info/energy-info.module').then((m) => m.EnergyInfoModule),
      },
      {
        path: 'saveenergy/energyreport',
        loadChildren: () => import('app/pages/energy-management/saveenergy-management/energy-report/energy-report.module').then((m) => m.EnergyReportModule),
      },
      {
        path: 'energyconfig',
        loadChildren: () => import('app/pages/energy-management/energy-config/energy-config.module').then((m) => m.EnergyConfigModule),
      },
      {
        path: 'energyconfig',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/energy-management/energy-config/energy-config.module').then((m) => m.EnergyConfigModule),
      },
      {
        path: 'energyconfig',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/energy-management/energy-config/energy-config.module').then((m) => m.EnergyConfigModule),
      },
      {
        path: 'energyconfig',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/energy-management/energy-config/energy-config.module').then((m) => m.EnergyConfigModule),
      },
      {
        path: 'energyconfig',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/energy-management/energy-config/energy-config.module').then((m) => m.EnergyConfigModule),
      },
      {
        path: 'energyclassify',
        loadChildren: () => import('app/pages/energy-management/energy-classify/energy-classify.module').then(m => m.EnergyClassifyModule)
      },
      {
        path: 'energyclassify',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/energy-management/energy-classify/energy-classify.module').then(m => m.EnergyClassifyModule)
      },
      {
        path: 'energyclassify',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/energy-management/energy-classify/energy-classify.module').then(m => m.EnergyClassifyModule)
      },
      {
        path: 'energyclassify',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/energy-management/energy-classify/energy-classify.module').then(m => m.EnergyClassifyModule)
      },
      {
        path: 'energyclassify',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/energy-management/energy-classify/energy-classify.module').then(m => m.EnergyClassifyModule)
      },
      {
        path: 'energycontrol',
        loadChildren: () => import('app/pages/energy-management/energy-control/energy-control.module').then(m => m.EnergyControlModule)
      },
      {
        path: 'energycontrol',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/energy-management/energy-control/energy-control.module').then(m => m.EnergyControlModule)
      },
      {
        path: 'energycontrol',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/energy-management/energy-control/energy-control.module').then(m => m.EnergyControlModule)
      },
      {
        path: 'energycontrol',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/energy-management/energy-control/energy-control.module').then(m => m.EnergyControlModule)
      },
      {
        path: 'energycontrol',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/energy-management/energy-control/energy-control.module').then(m => m.EnergyControlModule)
      },
      {
        path: 'energyefficiency',
        loadChildren: () => import('app/pages/energy-management/energy-efficiency/energy-efficiency.module').then((m) => m.EnergyEfficiencyModule),
      },
      {
        path: 'alarmfitting',
        loadChildren: () =>
          import('@app/pages/alarm-fitting/alarm-fitting.module').then(
            (m) => m.AlarmFittingModule
          ),
      },
      {
        path: 'alarmfitting',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/alarm-fitting/alarm-fitting.module').then(
            (m) => m.AlarmFittingModule
          ),
      },
      {
        path: 'alarmfitting',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/alarm-fitting/alarm-fitting.module').then(
            (m) => m.AlarmFittingModule
          ),
      },
      {
        path: 'alarmfitting',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/alarm-fitting/alarm-fitting.module').then(
            (m) => m.AlarmFittingModule
          ),
      },
      {
        path: 'alarmfitting',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/alarm-fitting/alarm-fitting.module').then(
            (m) => m.AlarmFittingModule
          ),
      },
      {
        path: 'alarmsfocuspanel',
        loadChildren: () =>
          import('@app/pages/alarm-focus-panel/alarm-focus-panel.module').then(
            (m) => m.AlarmFocusPanelModule
          ),
      },
      {
        path: 'alarmsfocuspanel',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/alarm-focus-panel/alarm-focus-panel.module').then(
            (m) => m.AlarmFocusPanelModule
          ),
      },
      {
        path: 'alarmsfocuspanel',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/alarm-focus-panel/alarm-focus-panel.module').then(
            (m) => m.AlarmFocusPanelModule
          ),
      },
      {
        path: 'alarmsfocuspanel',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/alarm-focus-panel/alarm-focus-panel.module').then(
            (m) => m.AlarmFocusPanelModule
          ),
      },
      {
        path: 'alarmsfocuspanel',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/alarm-focus-panel/alarm-focus-panel.module').then(
            (m) => m.AlarmFocusPanelModule
          ),
      },
      {
        path: 'videoManagement',
        loadChildren: () =>
          import('@app/pages/video-management/video-management.module').then(
            (m) => m.VideoManagementModule
          ),
      },
      {
        path: 'videoManagement',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/video-management/video-management.module').then(
            (m) => m.VideoManagementModule
          ),
      },
      {
        path: 'videoManagement',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/video-management/video-management.module').then(
            (m) => m.VideoManagementModule
          ),
      },
      {
        path: 'videoManagement',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/video-management/video-management.module').then(
            (m) => m.VideoManagementModule
          ),
      },
      {
        path: 'videoManagement',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/video-management/video-management.module').then(
            (m) => m.VideoManagementModule
          ),
      },
      {
        path: 'notifyHost',
        loadChildren: () => import('app/pages/notify-management/notify-host/notify-host.module').then((m) => m.NotifyHostModule),
      },
      {
        path: 'notifyServer',
        loadChildren: () => import('app/pages/notify-management/notify-server/notify-server.module').then((m) => m.NotifyServerModule),
      },
      {
        path: 'multidimensionalConfig',
        loadChildren: () => import('app/pages/energy-management/multidimensional-config/multidimensional-config.module').then((m) => m.MultidimensionalConfigModule),
      },
      {
        path: 'systemmanage/about',
        loadChildren: () => import('app/pages/backstage/misc/about/about.module').then((m) => m.AboutModule),
      },
      {
        path: 'systemmanage/securityConfig',
        loadChildren: () => import('app/pages/backstage/misc/security-config/security-config.module').then((m) => m.SecurityConfigModule),
      },
      {
        path: 'totalanalysis',
        loadChildren: () => import('app/pages/energy-query/total-analysis/total-analysis.module').then(m => m.TotalAnalysisModule)
      },
      {
        path: 'subanalysis',
        loadChildren: () => import('app/pages/energy-query/sub-analysis/sub-analysis.module').then(m => m.SubAnalysisModule)
      },
      {
        path: 'energyrank',
        loadChildren: () => import('app/pages/energy-query/energy-rank/energy-rank.module').then(m => m.EnergyRankModule)
      },
      {
        path: 'energycontrast',
        loadChildren: () => import('app/pages/energy-query/energy-contrast/energy-contrast.module').then(m => m.EnergyContrastModule)
      },
      {
        path: 'totaltrendreport',
        loadChildren: () => import('app/pages/energy-query/total-trend-report/total-trend-report.module').then(m => m.TotalTrendReportModule),
        data: {
          definitionTypeId: 1
        }

      },
      {
        path: 'totalpuereport',
        loadChildren: () => import('app/pages/energy-query/total-pue-report/total-pue-report.module').then(m => m.TotalPueReportModule),
        data: {
          definitionTypeId: 2
        }
      },
      {
        path: 'totaloptionreport',
        loadChildren: () => import('app/pages/energy-query/total-option-report/total-option-report.module').then(m => m.TotalOptionReportModule),
        data: {
          definitionTypeId: 2
        }
      },
      {
        path: 'totalotherreport',
        loadChildren: () => import('app/pages/energy-query/total-other-report/total-other-report.module').then(m => m.TotalOtherReportModule),
        data: {
          definitionTypeId: 2
        }
      },
      {
        path: 'totalfeereport',
        loadChildren: () => import('app/pages/energy-query/total-fee-report/total-fee-report.module').then(m => m.TotalFeeReportModule),
        data: {
          definitionTypeId: 2
        }
      },
      {
        path: 'carbontrendreport',
        loadChildren: () => import('app/pages/energy-query/carbon-trend-report/carbon-trend-report.module').then(m => m.CarbonTrendReportModule)
      },
      {
        path: 'carbonoptionreport',
        loadChildren: () => import('app/pages/energy-query/carbon-option-report/carbon-option-report.module').then(m => m.CarbonOptionReportModule)
      },
      {
        path: 'saveanalysis',
        loadChildren: () => import('app/pages/energy-query/save-analysis/save-analysis.module').then(m => m.SaveAnalysisModule)
      },
      {
        path: 'requireanalysis',
        loadChildren: () => import('app/pages/energy-query/require-analysis/require-analysis.module').then(m => m.RequireAnalysisModule)
      },
      {
        path: 'airconditionControl',
        loadChildren: () => import('app/pages/aircondition-cotrol/aircondition-cotrol.module').then(m => m.AirconditionControlModule)
      },
      {
        path: 'pearanalysis',
        loadChildren: () => import('app/pages/energy-query/pear-analysis/pear-analysis.module').then(m => m.PearAnalysisModule)
      },
      {
        path: 'carbonquota',
        loadChildren: () => import('@app/pages/energy-management/quota-management/carbon-quota.module').then(m => m.CarbonQuotaModule)
      },
      {
        path: 'aienergysaving',
        loadChildren: () => import('app/pages/ai-energysaving/ai-energysaving.module').then(m => m.AiEnergysavingModule)
      },
      {
        path: 'assetmanager/assetcategory',
        loadChildren: () => import('app/pages/asset/asset-category/asset-category.module').then(m => m.AssetCategoryModule)
      },
      {
        path: 'assetmanager/assetextend',
        loadChildren: () => import('app/pages/asset/asset-extend/asset-extend.module').then(m => m.AssetExtendModule)
      },
      {
        path: 'tableglasspanorama',
        loadChildren: () =>
          import('app/pages/energy-management/custom-made/tableglass-panorama/tableglass-panorama.module').then(
            (m) => m.TableglassPanoramaModule
          ),
      },
      // {
      //   path: 'assetmanager/assetdevice',
      //   loadChildren: () => import('app/pages/asset/asset-device/asset-device.module').then(m => m.AssetDeviceModule)
      // },
      {
        path: 'systemmanage/businessconfig',
        loadChildren: () => import('app/pages/backstage/misc/business-config/business-config.module').then((m) => m.BusinessConfigModule),
      },
      {
        path: 'energyrecord',
        loadChildren: () =>
          import('@app/pages/energy-management/custom-made/energy-record/energy-record.module').then(
            (m) => m.EnergyRecordModule
          ),
      },
      {
        path: 'carbonemission',
        loadChildren: () => import('@app/pages/energy-management/emission-accounting/carbon-emission.module').then(m => m.CarbonEmissionModule)
      }, {
        path: 'helpdoc',
        loadChildren: () => import('app/pages/admin/help-doc/help-doc.module').then((m) => m.HelpDocModule),
      },
      {
        path: 's6/expertadvicecollect',
        loadChildren: () =>
          import('@app/pages/site/expert-advice-collect/expert-advice-collect.module').then((m) => m.ExpertAdviceCollectModule),
      },
      {
        path: 'oilengine',
        loadChildren: () => import('app/pages/oil-engine/oil-engine.module').then(m => m.OilEngineModule)
      },
      {
        path: 'fourclassifyreport',
        loadChildren: () => import('app/pages/energy-query/four-classify-report/four-classify-report.module').then(m => m.FourClassifyReportModule),
        data: {
          definitionTypeId: 2
        }
      },
      {
        path: 'fourclassconfig',
        loadChildren: () => import('app/pages/energy-management/fourclass-config/fourclass-config.module').then(m => m.FourclassConfigModule)
      },
      {
        path: 'fourclassconfig',
        outlet: 'topLeft',
        loadChildren: () => import('app/pages/energy-management/fourclass-config/fourclass-config.module').then(m => m.FourclassConfigModule)
      },
      {
        path: 'fourclassconfig',
        outlet: 'topRight',
        loadChildren: () => import('app/pages/energy-management/fourclass-config/fourclass-config.module').then(m => m.FourclassConfigModule)
      },
      {
        path: 'fourclassconfig',
        outlet: 'bottomLeft',
        loadChildren: () => import('app/pages/energy-management/fourclass-config/fourclass-config.module').then(m => m.FourclassConfigModule)
      },
      {
        path: 'fourclassconfig',
        outlet: 'bottomRight',
        loadChildren: () => import('app/pages/energy-management/fourclass-config/fourclass-config.module').then(m => m.FourclassConfigModule)
      },
      {
        path: 'energyaudit',
        loadChildren: () => import('app/pages/energy-management/energy-audit/energy-audit.module').then(m => m.EnergyAuditModule)
      },
      {
        path: 'energystatistics',
        loadChildren: () => import('app/pages/energy-management/custom-made/energy-statistics/energy-statistics.module').then(m => m.EnergyStatisticsModule)
      },
      {
        path: 'energyprediction',
        loadChildren: () => import('app/pages/energy-management/custom-made/energy-prediction/energy-prediction.module').then(m => m.EnergyPredictionModule)
      },
      {
        path: 'fawsubdivision',
        loadChildren: () => import('app/pages/energy-query/faw-subdivision/faw-subdivision.module').then(m => m.FawSubdivisionModule),
        data: {
          definitionTypeId: 2
        }
      },
      {
        path: 'lossanalysis',
        loadChildren: () => import('app/pages/energy-query/loss-analysis/loss-analysis.module').then(m => m.LossAnalysisModule),
        data: {
          definitionTypeId: 2
        }
      },
      {
        path: 'linkmanagement',
        loadChildren: () =>
          import('@app/pages/link-management/link-management.module').then(
            (m) => m.LinkManagementModule
          ),
      },
      {
        path: 'linkmanagement',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/link-management/link-management.module').then(
            (m) => m.LinkManagementModule
          ),
      },
      {
        path: 'linkmanagement',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/link-management/link-management.module').then(
            (m) => m.LinkManagementModule
          ),
      },
      {
        path: 'linkmanagement',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/link-management/link-management.module').then(
            (m) => m.LinkManagementModule
          ),
      },
      {
        path: 'linkmanagement',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/link-management/link-management.module').then(
            (m) => m.LinkManagementModule
          ),
      },
      {
        path: 'equipment-management',
        loadChildren: () =>
          import('@app/pages/asset/equipment-management/equipment-management.module').then(
            (m) => m.EquipmentManagementModule
          ),
      },
      {
        path: 'equipment-management',
        outlet: 'topLeft',
        loadChildren: () =>
          import('@app/pages/asset/equipment-management/equipment-management.module').then(
            (m) => m.EquipmentManagementModule
          ),
      },
      {
        path: 'equipment-management',
        outlet: 'topRight',
        loadChildren: () =>
          import('@app/pages/asset/equipment-management/equipment-management.module').then(
            (m) => m.EquipmentManagementModule
          ),
      },
      {
        path: 'equipment-management',
        outlet: 'bottomLeft',
        loadChildren: () =>
          import('@app/pages/asset/equipment-management/equipment-management.module').then(
            (m) => m.EquipmentManagementModule
          ),
      },
      {
        path: 'equipment-management',
        outlet: 'bottomRight',
        loadChildren: () =>
          import('@app/pages/asset/equipment-management/equipment-management.module').then(
            (m) => m.EquipmentManagementModule
          ),
      },
      {
        path: 'fault-recording',
        loadChildren: () => import('@app/pages/fault-recording/fault-recording.module').then((m) => m.FaultRecordingModule),
      },
    ],
  },
];
export const routing: ModuleWithProviders<RouterModule> = RouterModule.forChild(routes);
