import { Component, OnInit, ViewChild } from '@angular/core';
import { NzFormatEmitEvent, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { NzTreeComponent } from 'ng-zorro-antd/tree/tree.component';
import { GroupAddEditComponent } from './group-add-edit/group-add-edit.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { OperateMode } from '@app/communal/models/operateMode.model';
import { ReportTreeService } from './report-tree.service';
import nzTreeFilter from '../access-control/model/nzTreeFilter';
import { TranslateService } from '@ngx-translate/core';
import { AlertService, ToastService } from '@app/communal/providers';
import { ActivatedRoute, Router } from '@angular/router';
import { GlobalState } from '@app/global.state';
import { HttpClient } from '@angular/common/http';
import { HierarchyEditComponent } from './hierarchy-edit/hierarchy-edit.component';

@Component({
  selector: 'app-report-tree',
  templateUrl: './report-tree.component.html',
  styleUrls: ['./report-tree.component.scss']
})
export class ReportTreeComponent implements OnInit {
  @ViewChild('nzTreeReport') nzTreeReport: NzTreeComponent;
  isCollapsed = false;  // 添加折叠状态控制变量
  treeSource: NzTreeNodeOptions[] = [];//树源数据
  nodes: NzTreeNodeOptions[] = [];//渲染数据
  expandKeys: string[] = [];
  searchText = "";
  checkData: any;
  constructor(
    private modal: NgbModal,
    private http: HttpClient,
    private service: ReportTreeService,
    private translate: TranslateService,
    private alertService: AlertService,
    private toastService: ToastService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private globalState: GlobalState,
  ) {
    this.subscribeToGlobalEvents();
  }

  ngOnInit(): void {
    this.getReportTree();
  }

  private subscribeToGlobalEvents(): void {
    this.globalState.subscribe("reportAddSuccess", (value: any) => {
      setTimeout(() => this.addReport(value), 0);
    });
    this.globalState.subscribe("refreshReportTree", () => {
      setTimeout(() => {
        this.getReportTree();
        this.navigateToRoot();
      }, 0);
    });
  }

  addReport(rId) {
    this.service.reportfoldermap(rId, this.checkData['key']).subscribe(() => {
      this.showSuccessToast('ngForm.create.success');
      this.getReportTree();
      this.navigateToRoot();
    });
  }

  getReportTree() {
    this.service.getReportTree().subscribe(
      (res: any) => {
        if (res || res.code == 200) {
          this.nodes = res;
          this.treeSource = res;
          this.buildGroupTree(this.nodes);
          this.checkData = null;
        }
      },
      (error: any) => console.log(error)
    );
  }

  buildGroupTree(nodes: any) {
    nodes.forEach((node): void => {
      const { name, id, children } = node;
      node.title = name;
      node.key = id;
      node.children = children;
      children && children.length > 0 ? this.buildGroupTree(children) : node.isLeaf = true;
    });
  }

  /**
   * @description 搜索
   *  this.treeSource 树源数据
   *  this.treeNodes 渲染到组件树的数据
   *  this.searchText 搜索关键字
   *  this.expandKeys 展开的节点数组
   */
  search() {
    if (!this.searchText) {
      this.nodes = this.treeSource
      this.expandKeys = [];
      return
    }
    const { treeSource, treeNodes, expandKeys } = nzTreeFilter(this.treeSource, this.nodes, this.searchText, this.expandKeys)
    this.treeSource = treeSource
    this.nodes = treeNodes
    this.expandKeys = expandKeys
  }

  openGroup(type, data?) {
    if (this.checkData) data = this.checkData;
    const activeModal = this.modal.open(GroupAddEditComponent, {
      backdrop: "static",
    });
    activeModal.componentInstance.mode = (type === 'add') ? OperateMode.create : OperateMode.edit;
    activeModal.componentInstance.value = data ? data : undefined;
    activeModal.result.then((res) => {
      if (res) this.getReportTree();
    });
  }

  openTemplate(type, data?) {
    const id = type === 'add' ? this.checkData['key'] : (data['key'] || 0);
    const route = type === 'add' ? `./${id}/add` : `./edit/${id}`;
    this.router.navigate([route], { relativeTo: this.activatedRoute });
  }

  editGroup(type, event, data): void {
    event.stopPropagation();
    if (data.type === 'report') {
      this.openTemplate(type, data)
    } else if (data.type === 'folder') {
      this.checkData = null;
      this.openGroup(type, data)
    }
  }

  deleteGroup(event, data): void {
    event.stopPropagation();
    const content = this.translate.instant("general.common.confirmDelete") + data["name"];
    this.alertService.deleteConfirm(content).then((res) => {
      if (res.value) {
        this.performDelete(data);
      }
    });
  }

  private performDelete(data): void {
    const deleteAction = data.type === 'folder'
      ? () => this.service.delete(data.id)
      : () => this.http.delete(`reports/${data.id}`);

    deleteAction().subscribe(
      () => {
        this.showSuccessToast('general.common.deleteSuccess');
        this.getReportTree();
        this.navigateToRoot();
      },
      (err) => {
        if (err && err.error) {
          this.showErrorToast(JSON.parse(err.error).err_msg);
        }
        this.getReportTree();
      }
    );
  }

  onNodeSelect(e) {
    this.checkData = e.node.origin;
    this.checkData['parentId'] = e.node?.parentNode?.key || 0;
    if (this.checkData['type'] === 'report') {
      let id = this.checkData['key'] || 0;
      this.router.navigate([`./detail/${id}`], { relativeTo: this.activatedRoute });
    }
  }

  nzDrop(event: NzFormatEmitEvent): void {
    if (event && event.dragNode && event.dragNode.parentNode) {
      this.handleOnDrop(event.dragNode);
    }else{
      this.getReportTree();
    }
  }

  handleOnDrop(currentNode) {
    const parentNode = currentNode.parentNode;
    this.updateNodePosition(currentNode.origin, parentNode.origin.id);
  }

  openHierarchyEdit(node: any) {
    const modalRef = this.modal.open(HierarchyEditComponent, {
      backdrop: 'static'
    });
    modalRef.componentInstance.currentNode = node;
    modalRef.componentInstance.nodes = this.nodes;
    modalRef.result.then((result) => {
      if (result) {
        this.updateNodePosition(node, result);
      }
    });
  }

  private updateNodePosition(node: any, newParentId: string) {
    const updateMethod = node.type === 'report' ? 'reportfoldermap' : 'updateReportTree';

    this.service[updateMethod](node.id, newParentId).subscribe(
      () => {
        this.showSuccessToast('admin.accountManagement.success');
        this.getReportTree();
        this.router.navigate(['./'], { relativeTo: this.activatedRoute });
      },
      (err) => {
        if (err && err.error) {
          this.showErrorToast(JSON.parse(err.error).err_msg);
        }
      }
    );
  }


  private showSuccessToast(key: string): void {
    this.toastService.showToast('success', this.translate.instant(key), null);
  }

  private showErrorToast(message: string): void {
    this.toastService.showToast('error', message, null);
  }

  private navigateToRoot(): void {
    this.router.navigate(['./'], { relativeTo: this.activatedRoute });
  }
}