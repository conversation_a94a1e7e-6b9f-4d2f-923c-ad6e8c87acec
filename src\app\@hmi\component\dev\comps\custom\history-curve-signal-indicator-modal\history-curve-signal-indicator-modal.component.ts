import { Component, OnInit, Input } from '@angular/core';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import { Base64ImageData } from '@app/communal/models/excel/base64ImageData.model';
import { ExcelService } from '@app/communal/providers/excel.service';
import { ToastService } from '@app/communal/providers';
import { SessionService } from '@app/core/services/session.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { CurveDataExportService } from './curve-data-export.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: 'app-history-curve-signal-indicator-modal',
    templateUrl: './history-curve-signal-indicator-modal.component.html',
    styleUrls: ['./history-curve-signal-indicator-modal.component.scss'],
})
export class HistoryCurveSignalIndicatorModalComponent implements OnInit {
    @Input() type = 'signal';
    @Input() equipmentId: number;
    @Input() name = '';
    @Input() signalId: number;
    @Input() isInner: boolean = false;
    @Input() baseTypeId: number;
    complexIndexId: number;
    @Input() title = '';
    chartInstance: any;
    options: any;
    reportChartType: string;
    dataList: any[] = [];
    startDate = { value: '' };
    endDate = { value: '' };
    timeSpan = '1';
    startDateOptions: any;
    endDateOptions: any;
    startPicker: any;
    endPicker: any;
    baseTypeName = '';
    @Input() unit = '';
    maxVisible = false;
    minVisible = false;
    avgVisible = true;
    scatterBool = false;

    constructor(
        private baseService: BaseCRUDService,
        private excelService: ExcelService,
        private toastService: ToastService,
        private service: CurveDataExportService,
        private activeModal: NgbActiveModal,
        private sessionService: SessionService,
        private translate: TranslateService
    ) { }

    ngOnInit() {
        this.initDateOptions();
        this.getHistoryDataList();
        this.initOptions();
    }

    onChartInit(e) {
        this.chartInstance = e;
    }

    initOptions() {
        const options = {
            color: [
                '#2693FF',
                '#32c5e9',
                '#8579ea',
                '#9fe6b8',
                '#ffda5b',
                '#ff9f80',
                '#fb7395',
                '#e062ae',
                '#9d96f5',
                '#00d9a3',
            ],
            grid: {
                bottom: 70,
            },
            title: {
                text: this.title + '(' + this.name + ')',
                textStyle: {
                    color: '#72b8ff',
                },
                left: 'center',
            },
            tooltip: {
                trigger: 'axis',
                formatter: (params) => {
                    params = params[0];
                    return (
                        this.baseTypeName +
                        ' ' +
                        params.data[0] +
                        '<br>' +
                        params.data[1] +
                        this.unit
                    );
                },
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false,
                },
                axisLine: {
                    lineStyle: {
                        color: 'rgba(189, 189, 189, 0.4)',
                    },
                },
                axisLabel: {
                    color: '#b1b1b1',
                },
            },
            yAxis: {
                type: 'value',
                max: function (value) {
                    return Math.ceil((value.max) / 2) * 2;
                },
                min: function (value) {
                    return Math.floor((value.min) / 2) * 2;
                } as any,
                axisLine: {
                    lineStyle: {
                        color: 'rgba(189, 189, 189, 0.4)',
                    },
                },
                axisLabel: {
                    color: '#b1b1b1',
                },
                splitLine: {
                    show: false,
                },
            },
            series: [
                {
                    type: this.scatterBool ? 'scatter' : 'line',
                    data: this.dataList,
                    markLine: {
                        data: this.avgVisible ? [{
                            type: 'average',
                            name: '平均值',
                            label: {
                                formatter: '平均值 ' + '{c}',
                            },
                        }] : [] as any,
                        label: {
                            color: '#BFDFFF',
                            fontWeight: 'normal',
                            textBorderWidth: 0
                        }
                    },
                    markPoint: {
                        symbolSize: 25,
                        label: {
                            show: true,
                            formatter: '{b}:{c}',
                            position: 'right',
                            color: '#BFDFFF',
                            fontWeight: 'normal'
                        },
                        data: []
                    }
                },
            ],
            dataZoom: [
                {
                    show: true,
                    realtime: true,
                    start: 0,
                    end: 100,
                    xAxis: [0, 1],
                    height: 20,
                },
            ],
        };
        if (this.maxVisible) options.series[0].markPoint.data = [{ type: 'max', name: 'max' }, { type: 'min', name: 'min' }];
        if (this.minVisible) {
            const valueList = this.dataList.map(i => i[1]);
            if (valueList.length) {
                const reduce = Math.ceil((Math.max(...valueList) - Math.min(...valueList)) * 100) / 100;
                options.series[0].markLine.data.push([{
                    symbol: 'none',
                    xAxis: this.dataList[0][0],
                    yAxis: reduce
                },
                {
                    symbol: 'arrow',
                    xAxis: this.dataList[this.dataList.length - 1][0],
                    yAxis: reduce,
                    label: {
                        formatter: '差值 ' + reduce
                    }
                }])
                const num = Math.min(reduce, Math.min(...valueList));
                options.yAxis.min = Math.floor((num) / 2) * 2 - 10;
            }
        }

        //  修复数据动态更新导致dataZoom被重置的问题
        const oldDataZoom = this.chartInstance && this.chartInstance.getOption() && this.chartInstance.getOption().dataZoom;
        if (oldDataZoom && oldDataZoom.length > 0) {
            if (oldDataZoom[0]) {
                const startX = oldDataZoom[0].start;
                const endX = oldDataZoom[0].end;
                options.dataZoom[0].start = startX;
                options.dataZoom[0].end = endX;
            }
            if (oldDataZoom[1]) {
                const startY = oldDataZoom[1].start;
                const endY = oldDataZoom[1].end;

                options.dataZoom[1].start = startY;
                options.dataZoom[1].end = endY;
            }
        }
        this.options = options;
    }

    initDateOptions() {
        const dateOptions: any = {
            singleDatePicker: true,
            timePicker: true,
            timePicker24Hour: true,
            timePickerSeconds: true,
            autoUpdateInput: false,
            showDropdowns: true,
            locale: {
                format: 'YYYY-MM-DD HH:mm:ss',
                cancelLabel: this.translate.instant('s2.reportmanagement.alert.cancel'),
                applyLabel: this.translate.instant('s2.reportmanagement.alert.confirm'),
                daysOfWeek: [
                    this.translate.instant('s2.calendar.Sunday'),
                    this.translate.instant('s2.calendar.Monday'),
                    this.translate.instant('s2.calendar.Tuesday'),
                    this.translate.instant('s2.calendar.Wednesday'),
                    this.translate.instant('s2.calendar.Thursday'),
                    this.translate.instant('s2.calendar.Friday'),
                    this.translate.instant('s2.calendar.Saturday'),
                ],
                monthNames: [
                    this.translate.instant('s2.calendar.January'),
                    this.translate.instant('s2.calendar.February'),
                    this.translate.instant('s2.calendar.March'),
                    this.translate.instant('s2.calendar.April'),
                    this.translate.instant('s2.calendar.May'),
                    this.translate.instant('s2.calendar.June'),
                    this.translate.instant('s2.calendar.July'),
                    this.translate.instant('s2.calendar.August'),
                    this.translate.instant('s2.calendar.September'),
                    this.translate.instant('s2.calendar.October'),
                    this.translate.instant('s2.calendar.November'),
                    this.translate.instant('s2.calendar.December'),
                ],
            },
        };
        this.startDateOptions = dateOptions;
        this.endDateOptions = dateOptions;
        const date = DateUtil.latestSomeHours(24);
        this.startDate.value = date[0]['startTime'];
        this.endDate.value = date[1]['endTime'];
        this.startDateOptions.startDate = date[0]['startTime'];//时间弹窗时间
        this.endDateOptions.startDate = date[1]['endTime'];//时间弹窗时间
    }

    formatSeries(dataList: any[]) {
        this.dataList = [];
        if (this.type === 'signal') {
            dataList.forEach((item, index) => {
                if (index === 0) {
                    this.unit =
                        (item['pointValue'] &&
                            item['pointValue'][0] &&
                            item['pointValue'][0]['unit']) ||
                        '';
                    this.baseTypeName =
                        (item['pointValue'] &&
                            item['pointValue'][0] &&
                            item['pointValue'][0]['baseTypeName']) ||
                        '';
                }
                if (item['pointValue'] && item['pointValue'][0]) {
                    this.dataList.push([
                        item['sampleTime'],
                        Math.ceil(item['pointValue'] * 100) / 100,
                    ]);
                }
            });
        } else if (this.type === 'indicator') {
            dataList.forEach((item, index) => {
                if (index === 0) {
                    this.unit = item.unit;
                }
                if (item['indexValue']) {
                    this.dataList.push([
                        item['sampleTime'],
                        Math.ceil(item['indexValue'] * 100) / 100,
                    ]);
                }
            });
        } else if (this.type === 'baseTypeSignal') {
            dataList.forEach((item, index) => {
                if (index === 0) {
                    this.unit = item['unit'];
                    this.baseTypeName = item['baseTypeName'];
                }
                if (item['pointValue']) {
                    this.dataList.push([
                        item['time'],
                        Math.ceil(item['pointValue'] * 100) / 100,
                    ]);
                }
            });
        }
    }

    getHistoryDataList() {
        const param = this.initParams();
        this.baseService
            .get(param[1], param[0] as string)
            .subscribe((res) => {
                if (res) {
                    if ((this.type === 'signal' || this.type === 'baseTypeSignal') && res.length !== 0) {
                        this.formatSeries(res);
                    } else if (this.type === 'indicator' && res[this.complexIndexId] &&
                        res[this.complexIndexId].length !== 0) {
                        this.formatSeries(res[this.complexIndexId]);
                    } else {
                        this.dataList = [];
                    }
                    this.initOptions();
                }
            });
    }

    initParams() {
        const date = this.getDates();
        if (this.type === 'signal') {
            return ['historysignals', {
                equipmentId: this.equipmentId,
                signalId: this.signalId,
                ...date,
            }];
        } else if (this.type === 'indicator') {
            return ['historycomplexindexs', {
                complexIndexIds: this.complexIndexId,
                ...date,
            }];
        } else if (this.type === 'baseTypeSignal') {
            return ['historysignals', {
                equipmentId: this.equipmentId,
                baseTypeId: this.isInner ? this.baseTypeId : this.signalId,
                ...date,
            }];
        }
    }

    getDates() {
        if ((this.timeSpan == '0') || (this.timeSpan == '1')) {
            return {
                startTime: this.startDate.value,
                endTime: this.endDate.value,
            };
        }
        return {
            startTime: DateUtil.dateToDayLongString(this.startDate.value),
            endTime: DateUtil.endTimeFormat(this.endDate.value),
        };
    }
    getDateFromTimeSpan() {
        let date = [];
        switch (this.timeSpan) {
            case '0':
                date = DateUtil.latestSomeHours(6);
                break;
            case '1':
                date = DateUtil.latestSomeHours(24);
                break;
            case '2':
                date = DateUtil.latestSomeDays(7);
                break;
            case '3':
                date = DateUtil.latestSomeDays(30);
                break;
            default:
                date = DateUtil.latestSomeHours(6);
                break;
        }
        return date;
    }

    submit() {
        this.getHistoryDataList();
    }

    setTime(value, param) {
        param.value = DateUtil.dateToString(value.picker.startDate['_d']);
        this.timeSpan = null;
    }

    updateDateRangePicker() {
        const date = this.getDateFromTimeSpan();
        this.startDate.value = date[0]['startTime'];
        this.endDate.value = date[1]['endTime'];
        this.startDateOptions.startDate = date[0]['startTime'];//时间弹窗时间
        this.endDateOptions.startDate = date[1]['endTime'];//时间弹窗时间
    }

    clearTime() {
        this.startDate.value = '';
        this.endDate.value = '';
    }

    export() {
        this.getReportChartType();
        if (this.chartInstance) {
            const chartImage = this.initChartImage();
            const workbookData = this.service.exportExcel(
                chartImage,
                this.dataList,
                this.title,
                this.unit,
                this.name,
                this.reportChartType
            );
            const reportName = this.title + '(' + this.name + ')';
            this.excelService
                .exportExcelFile(workbookData, reportName)
                .subscribe(
                    (res) => {
                        this.toastService.showToast(
                            'success',
                            '导出成功',
                            null
                        );
                        return true;
                    },
                    (err) => {
                        this.toastService.showToast('error', '导出失败', null);
                        return false;
                    }
                );
        }
    }

    initChartImage() {
        const dataImg = this.chartInstance.getDataURL({
            pixelRatio: 2,
            backgroundColor: 'rgba(153, 204, 255, 0.1)',
        });
        const chartImages = Array<Base64ImageData>();
        const historyDataTrendImg = new Base64ImageData();
        historyDataTrendImg.imgName = 'historyDataTrend.png';
        historyDataTrendImg.imgBase64Info = dataImg;
        historyDataTrendImg.imageResizeWidthRatio = 1.02;
        historyDataTrendImg.imageResizeHightRatio = 0.9;
        historyDataTrendImg.imageDrawStartAnchorCol = 0;
        historyDataTrendImg.imageDrawStartAnchorRow = 0;
        historyDataTrendImg.imageDrawEndAnchorCol = 16;
        historyDataTrendImg.imageDrawEndAnchorRow = 20;
        chartImages.push(historyDataTrendImg);
        return chartImages;
    }

    getReportChartType() {
        const chartType = this.sessionService.getSysconfigByKey(
            'report.chart.type'
        );
        if (chartType) {
            this.reportChartType = chartType['systemConfigValue'];
        }
    }

    closeModal() {
        this.activeModal.close();
    }
}
