import { Component, OnInit, ViewChild } from '@angular/core';
import { AlertService, ToastService } from '@app/communal/providers';
import { CoreeventseverityService } from '@app/communal/providers/coreeventseverity.service';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { LocalDataSource, Ng2SmartTableComponent } from 'ng2-smart-table';
import { AlarmReasonConfigModalComponent } from './alarm-reason-modal/alarm-reason-modal.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
    selector: 'app-alarm-reason',
    templateUrl: './alarm-reason.component.html',
    styleUrls: ['./alarm-reason.component.scss']
})
export class AlarmReasonConfigComponent implements OnInit {

    @ViewChild('table') ng2SmartTableComponent: Ng2SmartTableComponent;
    source: LocalDataSource = new LocalDataSource();
    settings: any;

    constructor(
        private toast: ToastService,
        private alertService: AlertService,
        private ngbModal: NgbModal,
        private coreeventseverityService: CoreeventseverityService,
        private translate: TranslateService) { }

    ngOnInit() {
        this.loadTableColumnSettings();
        this.getTableData();
    }

    getTableData() {
        this.coreeventseverityService.get('', 'eventreasontypes').subscribe(result => {
            this.source.load(result);
        });
    }

    loadTableColumnSettings(): void {
        this.settings = {
            mode: 'external',
            actions: {
                add: false,
                edit: true,
                delete: true,
                custom: [],
                position: 'right',
                columnTitle: this.translate.instant('general.common.action'),
            },
            edit: {
                editButtonContent: '<i class="iconfont icon-edit1"></i>'
            },
            delete: {
                deleteButtonContent: '<i class="ion-trash-a"></i>',
                confirmDelete: true,

            },
            pager: {
                display: true,
                perPage: 15,
            },
            noDataMessage: this.translate.instant('general.common.noData'),
            columns: {
                id: {
                    title: 'ID',
                    type: 'string',
                },
                name: {
                    title: this.translate.instant('featuremanage.alarmconfig.name'),
                    type: 'string',
                },
                description: {
                    title: this.translate.instant('featuremanage.alarmconfig.description'),
                    type: 'string',
                },
            }
        }
    }

    addItem(item?: any) {
        const modal = this.ngbModal.open(AlarmReasonConfigModalComponent, {
            container: 'app-alarm-reason',
            size: 'md',
        });
        modal.componentInstance.data = item ? item : {
            name: '',
            description: '',
        };
        modal.result.then(res => {
            if (res) {
                if(item) {
                    this.coreeventseverityService.editAlarmReason(res).subscribe(
                        res => {
                            this.toast.showToast('success', this.translate.instant('ngForm.update.success'), null);
                            this.getTableData();

                        },
                        error => {
                            this.toast.showToast('error', this.translate.instant('ngForm.update.error'), null);
                            this.getTableData();
                        }
                    )
                } else {
                    this.coreeventseverityService.addAlarmReason(res).subscribe(
                        res => {
                            this.toast.showToast('success', this.translate.instant('ngForm.update.success'), null);
                            this.getTableData();
                        },
                        error => {
                            this.toast.showToast('error', this.translate.instant('ngForm.update.error'), null);
                            this.getTableData();
                        }
                    )
                }
            }
        });
    }

    openeditModel(event) {
        this.addItem(event.data);
    }

    delete(event) {
        this.alertService.deleteConfirm(this.translate.instant('general.gatewayList.confirmDelete'), '').then((res) => {
            if (res.value){
                this.coreeventseverityService.deleteAlarmReason(event.data.id).subscribe(
                    res => {
                        this.toast.showToast('success', this.translate.instant('ngForm.update.success'), null);
                        this.getTableData();

                    },
                    error => {
                        this.toast.showToast('error', this.translate.instant('ngForm.update.error'), null);
                        this.getTableData();
                    }
                )
            }
        });
    }

}
