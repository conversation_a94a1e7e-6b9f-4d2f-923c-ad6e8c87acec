  :host ::ng-deep {
    .ng2-smart-action.ng2-smart-action-custom-custom:not(:first-child) {
      margin-left: 0.5rem;
    }
    .halfpannel{
      flex: 1;
      overflow: hidden;
      background-color: var(--portal-cbg-color);
    }
    tr.selected .operation-icon:hover {
      color: var(--text-activated);
    }
    .header-part{
      padding-top: 10px;
      padding-left: 15px;
    }
    .ellipsis-cell {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;   /* 允许根据单元格自适应缩放 */
      vertical-align: middle;
    }
    .modal-title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .badge-info {
        font-size: 14px;
        background-color: #17a2b8;
        padding: 5px 10px;
        color: white;
      }
      .d-flex {
        display: flex;
        justify-content: space-between; 
        align-items: center; 
        width: 100%; 
      }
      
      .search-container {
        position: absolute;
        display: inline-block;
        width: 100%;
    }
    .input-container {
      margin-left: 64px;
    }
    .d-flex.align-items-center.justify-content-end {
      flex-grow: 1;
    }
    .set-custom-columns {
      display: flex;
      align-items: center;
      margin-right: 16px;
    }
    .alarm-container {
      display: flex;
      align-items: center;
      margin-right: 64px; /* 与搜索框的间距 */
    }
    .alarm-item {
      display: flex;
      align-items: center;
      margin-right: 10px; /* 多个 em 之间的间距 */
    }
    .search-container {
      position: relative;
      width: 250px;
      margin-right: 10px;
    }
    .search-container i {
      position: absolute;
      left: 8px;
      top: 50%;
      transform: translateY(-50%);
    }
    .search-container input {
      width: 100%;
      padding-left: 23px;
      height: 32px;
      box-sizing: border-box;
    }
    .tableFrameFocus {
      width: 100%;
      /*需要修改*/
      overflow-x: auto;
      overflow-y: hidden;
      height: calc(100% - 80px);

      d-data-table {
        overflow: auto;
      }

      padding: 0;
      margin: 0 auto;
    }
    .input-group-addon {
        position: absolute;
        top: 0;
        color: var(--icon);
        background: 0 0;
        border: 0;
        border-radius: 0;
        z-index: 1000;
        cursor: pointer;
    }
    app-custom-smart-table thead th {
        position: sticky;
        top: 0;
      }

    .viewport100 {
      height: 100%;
      .card-body {
        overflow: hidden;
      }
    }

    // 新增：d-data-table样式
    /* 放在全局样式或 ::ng-deep 中，确保能穿透组件封装 */
    .devui-table thead th {
      position: sticky;
      top: 0;
      background: white; /* 防止遮挡内容 */
      z-index: 10;       /* 保证在上层 */
    }


    tr.selected {
      td {
        color: var(--portal-table-cell-text-active-color) !important;

        i {
          color: var(--portal-table-cell-text-active-color) !important;
        }

        background-color: var(--portal-table-cell-active-bg-color) !important;
      }

      // height: 25px;
      background-color: rgba(var(--primary), 1) !important;
      // border-bottom: none;
      padding: 0px 20px 0px!important;

    }
    tr td{
      line-height: 33px !important;
    }

    tr th{
      line-height: 33px !important;
    }
    tr.confirmed-row 
    {
      td {
        color: rgba(25, 208, 163, 0.8) !important;
        padding: 0px 20px 0px!important;
      }
    }
    tr.unconfirmed-row 
    {
      td {
        padding: 0px 20px 0px!important;
      }
    }
    ng-select {
      position: relative;
      z-index: 9999;
    }
    
  }
  