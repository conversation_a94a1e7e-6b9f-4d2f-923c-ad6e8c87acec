
import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';

import { SessionService } from '@core/services/session.service';
import { SmartTableServerDataSource } from '@app/communal/smart-table-data-source/smart-table-server.data-source';

@Injectable()
export class AlarmListService {
	public selectedItems: any[] = [];
	public serverDataSource: SmartTableServerDataSource;
	public lastPageStatus: any;

	constructor(private http: HttpClient,
		private sessionService: SessionService) {
	}

	getAFilter(url: string): Observable<any> {
		return this.http.get(url);
	}

	getData(): Observable<any> {
		return this.http.get('activeeventdtos');
	}

	public getFilterStaticsEvent(param: string): Observable<any> {
		return this.http.get('activeeventdtos/groupby?' + param);
	}

	public getEventByTemplate(condition: any) {
		let url = `activeeventdtos?`;
		if (condition) {
			let orderName = '';
			Object.keys(condition).forEach(key => {
				if (key == 'sort') {
					orderName = condition[key][0];
				}
				url += '&' + key + '=' + condition[key].join(',');
			});
			if (orderName.length > 0) {
				orderName = orderName.split(',')[0];
				if (orderName == 'eventLevel') {
					url += '&sort=startTime,desc';
				} else if (orderName == 'startTime') {
					url += '&sort=eventLevel,asc';
				} else {
					url += '&sort=startTime,desc';
				}
			}
		}
		return this.http.get(url);
	}
	
	// 历史告警统计
	public getHistoryAlarmStatics(condition: any) {
		let url = `historyevents/groupby?`;
		if (condition) {
			let orderName = '';
			Object.keys(condition).forEach(key => {
				if (key == 'sort') {
					orderName = condition[key][0];
				}
				url += '&' + key + '=' + condition[key].join(',');
			});
			if (orderName.length > 0) {
				orderName = orderName.split(',')[0];
				if (orderName == 'eventLevel') {
					url += '&sort=startTime,desc';
				} else if (orderName == 'startTime') {
					url += '&sort=eventLevel,asc';
				} else {
					url += '&sort=startTime,desc';
				}
			}
		}
		return this.http.get(url);
	}
	
	// 历史告警列表
	public getHistoryEventByTemplate(condition: any) {
		let url = `historyevents/page?`;
		if (condition) {
			let orderName = '';
			Object.keys(condition).forEach(key => {
				if (key == 'sort') {
					orderName = condition[key][0];
				}
				url += '&' + key + '=' + condition[key].join(',');
			});
			if (orderName.length > 0) {
				orderName = orderName.split(',')[0];
				if (orderName == 'eventLevel') {
					url += '&sort=startTime,desc';
				} else if (orderName == 'startTime') {
					url += '&sort=eventLevel,asc';
				} else {
					url += '&sort=startTime,desc';
				}
			}
		}
		return this.http.get(url);
	}

	public saveTemplate(param: any) {
		return this.http.post('activeeventfiltertemplates', param);
	}
	public getTemplate(url) {
		return this.http.get(url)
	}
	public updateTemplate(param: any) {
		return this.http.put('activeeventfiltertemplates', param);
	}

	public delTemplate(id): Observable<any> {
		return this.http.delete('activeeventfiltertemplates/' + id);
	}

	public confirmCov(obj): Observable<any> {
		return this.http.post('activeevents/confirm', obj);
	}

	public confirmCovTimeOut(obj, timeout): Observable<any> {
		return this.http.post('activeevents/confirm', obj, { headers: new HttpHeaders({ 'Timeout': timeout.toString() }) });
	}

	public commentCov(obj): Observable<any> {
		return this.http.post('activeevents/note', obj);
	}

	public endCov(obj): Observable<any> {
		return this.http.post('activeevents/cancel', obj);
	}

	public opeventLog(obj): Observable<any> {
		return this.http.post('batchactiveeventoperationlogs', obj);
	}

	public opeventLogTimeOut(obj, timeout): Observable<any> {
		return this.http.post('batchactiveeventoperationlogs', obj, { headers: new HttpHeaders({ 'Timeout': timeout.toString() }) });
	}

	// 用设备ID 获取 3D场景信息
	public getSceneId(id: number) {
		return this.http.get(`api/devicerefdimensions?equipmentId=${id}`);
	}

	public getSnapshotIp() {
		return this.http.get('v1/vpform/EqumentManage/getMinioConfig');

	}

	public getSnapshotUrl(id) {
		return this.http.get(`api/alarmchangesnapshot/${id}`);
	}

	public getSnapshotList(page, size, field, order, keyword): Observable<any> {
		return this.http.get(`alleventbysnapshot?&page=${page}&size=${size}&field=${field}&order=${order}&keywords=${keyword}`);
	}

	// 创建告警工单
	public createAlarmWorkOrder(id) {
		return this.http.post(`api/createalarmworkorder`, id);
	}

	public getAlarmReasonList() {
		return this.http.get(`api/eventreasontypes`);
	}
}
