import { Component, ViewChild, Injector, OnInit, Input } from '@angular/core';
import { Ng2SmartTableComponent } from 'ng2-smart-table';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import * as _ from 'lodash';
import { ReportModel } from '../../model/report.model';
import { LocalDataSource } from 'ng2-smart-table';
import { TranslateService } from '@ngx-translate/core';
import { TableBaseCellComponent } from '@app/communal/components/base-list-view/table-base-cell/table-base-cell.component';
import * as XLSX from 'xlsx';
import { AuthenticationService } from '@app/core/services/authentication.service';

@Component({
    selector: 'server-source-report',
    templateUrl: 'server-source-report.component.html',
    styleUrls: ['server-source-report.component.scss']
})

export class ServerSourceReportComponent implements OnInit {
    @ViewChild('table') ng2SmartTable: Ng2SmartTableComponent;
    @Input() reportQueryParam: ReportModel;
    @Input() reportName: string;
    @Input() exportPermission: boolean;

    cloneQueryParam: ReportModel;
    datas: unknown;
    settings: unknown;
    source: LocalDataSource = new LocalDataSource();
    langKeys = [
        'general.common.noData'
    ];
    langValues: Record<string, string>;
    exportBtnShow = false;
    page: number;
    total: number;
    URL = 'reportdatas';
    isLoading = false;
    type: number;
    //导出按钮限制
    isExporting = false;
    exportTimer: any;
    isByteDance: boolean = false;

    constructor(
        private service: BaseCRUDService,
        private translate: TranslateService,
        private auth: AuthenticationService) { }

    ngOnInit() {
        if (this.reportQueryParam) {
            this.cloneQueryParam = _.cloneDeep(this.reportQueryParam);
            this.cloneQueryParam.size = 18;
            this.type = this.reportQueryParam.type;
        }

        this.auth.getAuthenticationJson().subscribe(resJson => {
            this.isByteDance = resJson['bytedace.web.enable'];
        })


        this.isLoading = true;
        this.initTranslate();
        const firsrPage = 1;
        this.getPage(firsrPage);
    }

    initTranslate() {
        this.translate.get(this.langKeys).subscribe(value => {
            this.langValues = value;
        })
    }

    parseReportData(data) {
        if (data) {
            const header: unknown = data.title,
                body = data.result;
            const columns = {};
            _.forIn(header, (value, key) => {
                columns[key] = {
                    title: value,
                    type: 'custom',
                    renderComponent: TableBaseCellComponent as Component
                }
            })
            this.loadTableColumnSettings(columns, body);
        }
    }

    loadTableColumnSettings(columns, data) {
        this.settings = {
            mode: 'external',
            actions: {
                add: false,
                edit: false,
                delete: false,
                position: 'right' // left|right
            },
            pager: {
                display: true,
                perPage: 18
            },
            noDataMessage: this.langValues['general.common.noData'],
            columns: columns,
            delete: { deleteButtonContent: '<i class="ion-trash-a"></i>' },
            edit: { editButtonContent: '<i class="ion-edit"></i>' }
        };
        this.ng2SmartTable.initGrid();
        this.source = new LocalDataSource(data);
    }

    getPage(page: number) {
        this.cloneQueryParam.page = page - 1;
        this.getSource(page);
    }

    getSource(page: number) {
        this.service.create(this.cloneQueryParam, this.URL).subscribe(res => {
            if (res && res.result) {
                this.page = page;
                this.total = res['totalElements'];
                this.datas = res['result'];
                this.source.load(res.result);
                this.parseReportData(res);
                if (page === 1) {
                    this.exportBtnShow = res.result.length > 0;
                }
                this.isLoading = false;
            }
        })
    }

    export(): void {
        if (!this.exportBtnShow) return;
        if(this.isExporting) return;
        this.isExporting = true;
        if(this.exportTimer) {
            clearTimeout(this.exportTimer);
        }
        this.isLoading = true;
        this.exportTimer = setTimeout(() => {
            this.service.create(this.reportQueryParam, this.URL).subscribe(
                res => {
                    if (res) {
                        const fileName = this.reportName + '.xlsx',
                        header = res['title'],
                        data = res['result'];
                        
                        if (this.reportQueryParam.reportSchemaId === 37 && res['charts'] && this.isByteDance){
                            const byteCharacters = atob(res['charts']); // Base64 解码
                            const byteNumbers = new Array(byteCharacters.length);
                            for (let i = 0; i < byteCharacters.length; i++) {
                                byteNumbers[i] = byteCharacters.charCodeAt(i);
                            }
                            const byteArray = new Uint8Array(byteNumbers);
                            const blob = new Blob([byteArray], { type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" });

                            const link = document.createElement("a");
                            link.href = URL.createObjectURL(blob);
                            link.download = fileName;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(link.href);
                        }
                        else{
                            const xlsTemplateJson = [];
                            xlsTemplateJson.push(_.values(header));
                            data.forEach(item => {
                                const obj = {};
                                _.forIn(header, (value, key) => {
                                    obj[key] = item[key] || (item[key] === 0 ? item[key] : '');
                                })
                                xlsTemplateJson.push(_.values(obj));
                            })
                            const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson);
                            const cols = [];
                            for (const i in header) {
                                cols.push({ wch: 20 });
                            }
                            ws["!cols"] = cols;
                            const wb: XLSX.WorkBook = XLSX.utils.book_new();
                            XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
                            XLSX.writeFile(wb, fileName);
                            this.isExporting = false;
                        }
                        this.isLoading = false;
                    }
                },
                error => {
                    this.isExporting = false;
                }
            )
        }, 2000);

    }

}