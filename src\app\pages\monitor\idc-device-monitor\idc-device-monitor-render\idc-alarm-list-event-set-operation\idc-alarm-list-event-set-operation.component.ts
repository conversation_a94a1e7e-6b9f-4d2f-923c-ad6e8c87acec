import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { SetMaskComponent } from '@app/communal/components/set-mask/set-mask.component';
import { AlertService, ToastService } from '@app/communal/providers';
import { SessionService } from '@app/core/services/session.service';
import { DeviceShieldConfigComponent } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.component';
import { DeviceShieldEventService } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
// import { AlarmListEventSetComponent } from './alarm-list-event-set.component';
// import { ShieldConfigComponent } from '../shield-config/shield-config.component';
import { TranslateService } from '@ngx-translate/core';
import { IdcAlarmListConditionComponent } from '../../idc-device-alarm-list/idc-alarm-list-condition/idc-alarm-list-condition.component';
import { IdcAlarmListEventSetComponent } from '../../idc-device-alarm-list/idc-alarm-list-event-set/idc-alarm-list-event-set.component';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-idc-alarm-list-event-set-operation',
  templateUrl: './idc-alarm-list-event-set-operation.component.html',
  styleUrls: ['./idc-alarm-list-event-set-operation.component.scss']
})
export class IdcAlarmListEventSetOperationComponent implements OnInit {

  public detailTip = this.translate.instant("s2.shieldevent.eventShield");
  public detailTip2 = this.translate.instant("s2.devicemonitor.alarmTest");
  public detailTip3 = this.translate.instant("s2.devicemonitor.alarmSet.eventConfig");
  public detailTip4 = this.translate.instant("general.mask.unblock");

  shieldDisplay = false;
  eventDisplay = false;
  _value: any;
  @Input('value')
  set value(value: any) {
    this._value = value;
    if (value.shieldDisplay)
      this.shieldDisplay = value.shieldDisplay;
    else
      this.shieldDisplay = false;
    if (value.eventDisplay)
      this.eventDisplay = value.eventDisplay;
    else
      this.eventDisplay = false;
  }
  get value() {
    return this._value;
  }

  @Input() deviceCategoryId;
  constructor(
    private translate: TranslateService,
    private router: Router,
    private ngbModal: NgbModal,
    private sessionService: SessionService,
    private toastService: ToastService,
    private alertService: AlertService,
    public http: HttpClient,
    private shieldService: DeviceShieldEventService,
  ) { }

  ngOnInit() {
    this.shieldDisplay = this.value.shieldDisplay;
    // this.eventDisplay=this.value.eventDisplay;
    // if(this.value.isBInterface == 0 && this.value.eventDisplay){
    if (this.value.eventDisplay) {
      this.eventDisplay = true
    }
  }

  checkIsValid() {
    if (!this.sessionService.hasPermission(15)) {
      this.toastService.showToast('error', this.translate.instant('general.idcDeviceMonitor.noDynamicConfigPermission'), '');
      return false;
    }

    return true;
  }


  ShowEventConfig() {
    if (!this.checkIsValid()) {
      return;
    }

    if (!this.value) {
      return;
    }
    const modal = this.ngbModal.open(IdcAlarmListEventSetComponent,
      { backdrop: 'static', size: 'lg', backdropClass: 'bounced-hierarchy', windowClass: 'my-batch-config-window' });
    modal.componentInstance.equipmentParam = this.value;
    modal.componentInstance.deviceCategoryId = this.deviceCategoryId;
    // console.log(this.value);

  }

  ShowAlarmTest() {
    if (!this.value) {
      return;
    }
    const modal = this.ngbModal.open(IdcAlarmListConditionComponent,
      { backdrop: 'static', size: 'lg', backdropClass: 'bounced-hierarchy', windowClass: 'my-batch-config-window' });
    modal.componentInstance.equipmentParam = this.value;
    modal.componentInstance.deviceCategoryId = this.deviceCategoryId;
    // console.log(this.value);

  }

  ShieldEventConfig() {
    if (!this.value) {
      return;
    }
    // console.log(this.value)
    // const modal = this.ngbModal.open(SetMaskComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });

    // modal.componentInstance.deviceId = this.value.equipmentId;
    // modal.componentInstance.corePointId = this.value.eventId;
    // modal.componentInstance.maskTarget = this.value.equipmentName;
    // modal.componentInstance.stationId = this.value.stationId;

    const eventmasksId = this.value.stationId + ',' + this.value.equipmentId + ',' + this.value.eventId;
    this.shieldService.getEventMask(eventmasksId).subscribe(res => {
      if (res) {
        const time = {
          timeGroupCategory: res['timeGroupCategory'],
          timeGroupSpans: res['timeGroupSpans'],
          InputstartTime: res['startTime'],
          InputendTime: res['endTime'],
          Inputreason: res['reason'],
        }
        this.handleOpenShield(time);
      } else {
        this.handleOpenShield();
      }
    }, err => {
      this.handleOpenShield();
    })

    // const modal = this.ngbModal.open(DeviceShieldConfigComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    // modal.componentInstance.maskTarget = this.value.eventName;
    // modal.componentInstance.equipmentId = this.value.equipmentId;
    // modal.componentInstance.stationId = this.value.stationId;
    // modal.componentInstance.eventId = this.value.eventId;

    // modal.result.then((res) => {
    //   if(res){
    //     let maskUrl = 'eventmasks';
    //     this.saveEventMask(res, maskUrl);
    //   }
    // });
  }

  handleOpenShield(timeRecover?) {
    const modal = this.ngbModal.open(DeviceShieldConfigComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.maskTarget = this.value.eventName;
    modal.componentInstance.equipmentId = this.value.equipmentId;
    modal.componentInstance.stationId = this.value.stationId;
    modal.componentInstance.eventId = this.value.eventId;

    if (timeRecover) {
      modal.componentInstance.timeGroupCategory = timeRecover.timeGroupCategory;
      modal.componentInstance.timeGroupSpans = timeRecover.timeGroupSpans;
      modal.componentInstance.InputstartTime = timeRecover.InputstartTime;
      modal.componentInstance.InputendTime = timeRecover.InputendTime;
      modal.componentInstance.Inputreason = timeRecover.Inputreason;
      modal.componentInstance.showDelete = true;
    }

    modal.result.then((res) => {
      if (res && res !== 'delete') {
        let maskUrl = 'eventmasks';
        this.shieldService.saveMask(res, maskUrl).subscribe(
          res => {
            this.toastService.showToast('success', this.translate.instant('s2.shieldevent.saveSuccess'), '');
          },
          error => {
            this.toastService.showToast('error', this.translate.instant('s2.shieldevent.saveError'), '');
          }
        )
      } else if (res === 'delete') {
        this.deleteMask();
      }
    });
  }

  saveEventMask(param, maskUrl) {
    this.shieldService.saveMask(param, maskUrl).subscribe(
      res => {
        this.toastService.showToast('success', this.translate.instant('s2.shieldevent.saveSuccess'), '');
      },
      error => {
        this.toastService.showToast('error', this.translate.instant('s2.shieldevent.saveError'), '');
      }
    )
  }

  deleteMask() {
    const content =
      this.translate.instant('general.mask.unblock') + this.value['equipmentName'];
    this.alertService.deleteConfirm(content).then(res => {
      if (res.value) {
        this.http.delete(`eventmasks/${this.value['stationId']},${this.value['equipmentId']},${this.value['eventId']}`).subscribe(
          res => {
            this.toastService.showToast('success', this.translate.instant('s2.shieldevent.saveSuccess'), '');
          },
          error => {
            this.toastService.showToast('error', this.translate.instant('s2.shieldevent.saveError'), '');
          });
      }
    });
  }
}
