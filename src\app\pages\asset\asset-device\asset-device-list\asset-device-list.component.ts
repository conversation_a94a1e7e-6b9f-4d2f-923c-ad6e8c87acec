import { fork<PERSON>oin } from 'rxjs';
import { Component, Injector, OnInit, ViewChild, ChangeDetectorRef, ElementRef, Renderer2} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ExportExcelService } from '../export-excel.service';
import { ExcelService } from '@communal//providers/excel.service';
import { TranslateService } from '@ngx-translate/core';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { AssetParkService } from '../../services/asset-park.service';
import * as _ from 'lodash';
import * as XLSX from 'xlsx';
import { SwalComponent } from '@sweetalert2/ngx-sweetalert2';
import { ToastService, AlertService } from '@communal/providers';
import { ExcelValidationService } from '../../excel-validation.service';
import { ValidateData } from '../../validateData.model';
type AOA = any[][];
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { OperateMode } from '@models/operateMode.model';
import { ImportTypeComponent } from '../import-type/import-type.component';
import { TableBaseCellComponent } from '@app/communal/components/base-list-view/table-base-cell/table-base-cell.component';
import { CustomColumnsComponent } from '@app/pages/site/components/custom-columns/custom-columns.component';
import { SessionService } from '@core/services/session.service';
import { AlarmListService } from '@app/pages/monitor/alarm-list/alarm-list.service';
import { SortEventArg } from 'ng-devui/data-table';
import { tableResizeFunc } from 'ng-devui';
import { AssetDeviceService } from '../asset-device.service';
import { AuthenticationService } from '@app/core/services/authentication.service';
import { NzModalService } from 'ng-zorro-antd/modal';
import { CssVariableService } from '@app/theme/theme.css-variable.service';
import { catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import * as FileSaver from 'file-saver';
import { map } from 'rxjs/operators';
@Component({
  selector: 'app-asset-device-list',
  templateUrl: './asset-device-list.component.html',
  styleUrls: ['./asset-device-list.component.scss']
})
export class AssetDeviceListComponent extends BaseCRUDService implements OnInit {
  // @ViewChild('table', { static: true }) ng2SmartTable: Ng2SmartTableComponent;
  DEVELOPMENT_MODEL = 'develop';
  source: any = []
  columnHeaders: Array<string> = [];
  searchText: string;
  searchNameList: Array<any>;
  columnHeaderKeys = [
  ];
  assetDeviceData: any = [];
  currentAssetDeviceData: any = [];
  importType: number;
  saveAssetCategory: boolean;
  data: AOA = [];
  fileName = '';
  validateResult: string[] = [];
  excelData: any;
  extendData: any = [];
  selectItem: any = [];
  unselectedItem: any = [];
  pageCounts = [20, 50, 100];
  serverPaging = {
    number: 0,
    size: 50,
    totalElements: 0
  };
  settings: any = {
    columns: []
  };
  defaultColumns = [] // 接口返回的所有表格字段列
  ColumnCustomType = 'AssetDeviceColumn'
  savedCustomColumnId:any
  multiSort: SortEventArg[] = [];
  minWidth = 100;
  requiredFields = []
  forbiddenField = []
  showImportFromEqu = false;
  tableheight = (window.innerHeight - 300)
  isLoading = false;
  @ViewChild('excelImportSwal') private excelImportSwal: SwalComponent;


  constructor(
    injector: Injector,
    private service: AssetParkService,
    private http: HttpClient,
    private toastService: ToastService,
    private exportExcelService: ExportExcelService,
    private excelService: ExcelService,
    private translate: TranslateService,
    private excelValidationService: ExcelValidationService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private modal: NgbModal,
    private alertService: AlertService,
    private cdr: ChangeDetectorRef,
    private ele: ElementRef,
    public sessionService: SessionService,
    private alarmService: AlarmListService,
    private assetService: AssetDeviceService,
    private authenticationService:AuthenticationService,
    private nzmodal: NzModalService,
    private cssVariableService:CssVariableService,
  ) {
    super(http, 'idcassetdevices/page')
  }

  ngOnInit() {
    this.setTableColumnSettings();
    this.authenticationService.getAuthenticationJson().subscribe(resJson => {
      this.showImportFromEqu = resJson['bytedace.web.enable'];
    });
  }
  onResize(e) {
    const currentColumn = e.currentColumn;
    if (currentColumn) {
      const field = currentColumn.field;
      const column = this.settings.columns.find(col => col.field === field);
      const beforeWidth =  column ? parseFloat(column.width) : this.minWidth
      const width = parseFloat(currentColumn.width);
      const func = tableResizeFunc(this.settings.columns, this.ele);
      const withObj = {beforeWidth,width}
      if (withObj.width < this.minWidth) {
			   withObj.width = this.minWidth;
		  }
		  func(withObj, field);
      this.saveCustomColumn();
    }
  }
  setTableColumnSettings(): void {
    this.columnHeaderKeys = [
      'asset.assetDevice.title',
      'asset.assetDevice.excelFileName',
      'asset.assetDevice.assetDeviceId',
      'asset.assetDevice.deviceCode',
      'asset.assetDevice.deviceName',
      'asset.assetDevice.assetCategoryId',
      'asset.assetDevice.deviceType',
      'asset.assetDevice.brand',
      'asset.assetDevice.model',
      'asset.assetDevice.capacityParameter',
      'asset.assetDevice.settingPosition',
      'asset.assetDevice.serialNumber',
      'asset.assetDevice.manufactor',
      'general.common.downloadSuccess',
      'general.common.action',
      'general.common.noData',
      'general.common.confirmDelete',
      'general.common.deleteSuccess',
      'general.common.confirmDelete2',
      'asset.common.templateError',
      'asset.common.excelImportSuccess',
      'asset.common.excelImportError',
      'report.batteryAlarmHistoryReport.downloadExcelFileFail'
    ];
    this.translate.get(this.columnHeaderKeys).subscribe((values) => {
      this.columnHeaders = values;
      this.loadTableColumnSettings();
      this.initData();
    });
  }

  initData() {
    const param = {};
    param['page'] = this.serverPaging.number + 1;
    param['size'] = this.serverPaging.size;
    if (this.searchText) param['keyword'] = this.searchText;
    this.requiredFields = ['编号','名称']
    this.forbiddenField = []
    forkJoin([this.get(param, 'idcassetdevices/page'), this.getAll('extfieldconfigurations?extTable=assetDevice')]).subscribe(res => {
      if (res[1] && res[1].length > 0) {
        const dynamicRequiredFields = res[1].filter(field => field.extNecessary && field.extDataType !== 'equipment').map(field => field.extName);
        this.requiredFields = Array.from(new Set([...this.requiredFields, ...dynamicRequiredFields]));
        this.forbiddenField = res[1]
        .filter(field => field.extDataType === 'equipment' || field.extDataType === 'table')
        .map(field => field.extName);
        this.extendData = [];
        _.map(res[1], (result, i) => {
          if (result && result.extCode && result.extCode.includes("_noshow")) {
            return; 
          }
          const extendObj = {
            title: result.extName,
            extId: result.extId,
            type: 'custom',
            filter: false,
            renderComponent: TableBaseCellComponent as Component
          }     
          this.extendData.push(extendObj);
          this.settings.columns.push({
            field: 'extId' + result.extId,
            title: extendObj.title,
            width: result.extDataType === 'date' ?'90px':'150px',
            headerText: extendObj.title,
            canSort: true,
            visible: true
          })
        })
       const operateColumn = this.settings.columns.find(col => col.field === 'operate'); // 确保操作列置于数组末
       if (operateColumn) {
        this.settings.columns = this.settings.columns.filter(col => col.field !== 'operate');
        this.settings.columns.push(operateColumn);
        }
        this.defaultColumns = []
        this.defaultColumns = JSON.parse(JSON.stringify(this.settings.columns))
        this.fetchCustomColumn()
      }
      _.map(res[0].records, item => {
        _.map(item['extValueConfigurationList'], (obj, j) => {
          if (obj['extValue'] && obj['extFieldConfiguration']['extDataType'] === 'equipment') {
            item['extId' + obj.extId] = JSON.parse(obj['extValue'])[0]['eqName'];
          } else {
            item['extId' + obj.extId] = obj.extValue;
          }
        })
      })
      const newPaging = {
        number: res[0].current - 1,
        size: res[0].size,
        totalElements: res[0].total
      };
      this.serverPaging = newPaging;
      this.source = res[0].records
      this.assetDeviceData = res[0].records;
      this.currentAssetDeviceData = res[0].records;
      this.selectItem = [];
    })
  }

  getData() {
    const param = {};
    param['page'] = this.serverPaging.number + 1;
    param['size'] = this.serverPaging.size;
    if (this.searchText) param['keyword'] = this.searchText;
    this.get(param, 'idcassetdevices/page').subscribe(res => {
      _.map(res.records, item => {
        _.map(item['extValueConfigurationList'], (obj, j) => {
          if (obj['extValue'] && obj['extFieldConfiguration']['extDataType'] === 'equipment') {
            item['extId' + obj.extId] = JSON.parse(obj['extValue'])[0]['eqName'];
          } else {
            item['extId' + obj.extId] = obj.extValue;
          }
        })
      })
      const newPaging = {
        number: res.current - 1,
        size: res.size,
        totalElements: res.total
      };
      this.serverPaging = newPaging;
      this.source = res.records
      this.assetDeviceData = res.records;
      this.currentAssetDeviceData = res.records;
      this.selectItem = [];
    })
  }

  onPageChange(num) {
    this.serverPaging.number = num;
    this.getData();
  }


  getSearchField(columns, query = '') {
    const searchList = [];
    if (columns) {
      for (const i of Object.keys(columns)) {
        const fieldObj = { field: i, search: query };
        searchList.push(fieldObj);
      }
    }
    return searchList;
  }
  checkChange(e) {
    const data = e.rowItem
    if(e.checked) {
      this.selectItem.push(data);
      data.$rowClass = 'isSelected'
    } else {
      this.selectItem = this.selectItem.filter(item => item !== data);
      data.$rowClass = ''
    }
  }
  allCheckChange(isAllChecked) {
    if(isAllChecked) {
      this.selectItem = this.source
    } else {
      this.selectItem = []
    }
    this.source.forEach(item => {
      item.$rowClass = isAllChecked ? 'isSelected' : ''
    })
  }
  onUserRowSelect(event) {

  }
  onSortChange(e) {
    if(e && e.length ===1) {
      const {field, direction} = e[0]
      const sortOrder = direction === 'DESC' ? -1 : 1;
      this.source = this.source.sort((a, b) => {
        const valueA = typeof a[field] === 'string' ? a[field].toLowerCase() : a[field];
        const valueB = typeof b[field] === 'string' ? b[field].toLowerCase() : b[field];
    
        if (typeof a[field] === 'string') {
          return valueA.localeCompare(valueB) * sortOrder;
        } else {
          return (valueA - valueB) * sortOrder;
        }
      });
    }
  }

  loadTableColumnSettings(): void {
    this.settings = {
      columns: [
        { field: 'assetCode', title: this.columnHeaders['asset.assetDevice.deviceCode'], width: '250px', headerText: this.columnHeaders['asset.assetDevice.deviceCode'], canSort: true, visible: true },
        { field: 'assetName', title: this.columnHeaders['asset.assetDevice.deviceName'], width: '250px', headerText: this.columnHeaders['asset.assetDevice.deviceName'], canSort: true, visible: true },
        { field: 'assetCategoryName', title: this.columnHeaders['asset.assetDevice.deviceType'], width: '100px', headerText: this.columnHeaders['asset.assetDevice.deviceType'], canSort: true, visible: true },
        { field: 'brand', title: this.columnHeaders['asset.assetDevice.brand'], width: '100px', headerText: this.columnHeaders['asset.assetDevice.brand'], canSort: true, visible: true },
        { field: 'model', title: this.columnHeaders['asset.assetDevice.model'], width: '100px', headerText: this.columnHeaders['asset.assetDevice.model'], canSort: true, visible: true },
        { field: 'capacityParameter', title: this.columnHeaders['asset.assetDevice.capacityParameter'], width: '100px', headerText: this.columnHeaders['asset.assetDevice.capacityParameter'], canSort: true, visible: true },
        { field: 'settingPosition', title: this.columnHeaders['asset.assetDevice.settingPosition'], width: '100px', headerText: this.columnHeaders['asset.assetDevice.settingPosition'], canSort: true, visible: true },
        { field: 'serialNumber', title: this.columnHeaders['asset.assetDevice.serialNumber'], width: '100px', headerText: this.columnHeaders['asset.assetDevice.serialNumber'], canSort: true, visible: true },
        { field: 'manufactor', title: this.columnHeaders['asset.assetDevice.manufactor'], width: '100px', headerText: this.columnHeaders['asset.assetDevice.manufactor'], canSort: true, visible: true },
        { field: 'operate',title: this.translate.instant('energyPanorama.saveEnergy.operation'),width: '100px',headerText: this.translate.instant('energyPanorama.saveEnergy.operation'),canSort: true, visible: true }
      ]
    }
    this.defaultColumns = JSON.parse(JSON.stringify(this.settings.columns))
  }

  selectImportType() {
    this.openImportType(OperateMode.add);
  }

  openImportType(mode: OperateMode) {
    const activeModal = this.modal.open(ImportTypeComponent);
    activeModal.result.then(res => {
      if (res) {
        this.importType = res['importType'];
        this.saveAssetCategory = res['saveAssetCategory'];
        const inputElement = document.getElementById('excelImport');
        inputElement.click();
      }
    })
    this.getData();
  }
  onFileChange(evt: any) {  //导入文件
    /* wire up file reader */
    // 确保文件选择成功
    if (!evt || !evt.target || !evt.target.files || evt.target.files.length === 0) {
      console.error('没有选择文件或文件选择失败');
      return;
    }
    
    // 正确设置SweetAlert按钮状态
    if (this.excelImportSwal) {
      this.excelImportSwal.showConfirmButton = true; // 确保确认按钮可见
    }
    
    const target: DataTransfer = evt.target as DataTransfer;
    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      /* read workbook */
      const bstr: string = e.target.result;
      // 修改XLSX读取选项，确保将所有单元格作为字符串处理
      const wb: XLSX.WorkBook = XLSX.read(bstr, { 
        type: 'binary',
        cellText: true,
        cellDates: true,
        raw: false
      });

      /* grab first sheet */
      const wsname: string = wb.SheetNames[0];
      const ws: XLSX.WorkSheet = wb.Sheets[wsname];

      /* save data */
      // 修改sheet_to_json选项，确保所有数值都作为字符串处理
      this.data = XLSX.utils.sheet_to_json(ws, { 
        header: 1,
        raw: false,
        defval: ''
      });
      
      /* validation */
      const rowOffset = 1;
      if (this.data.length <= rowOffset) {
        this.validateResult = [];
        this.validateResult.push(this.columnHeaders['asset.common.templateError']);
        this.excelImportSwal.fire().then(() => {
          evt.target.value = '';
        });
        return;
      }
      const columnsCount: number = this.data[rowOffset - 1].length;
      const excelData = this.data.slice(1);
      const assetCodeList: Array<any> = new Array<any>();
      excelData.forEach((item, index) => {
        if (item && item.length > 0) {
          assetCodeList.push(String(item[0]));
          for (let i = 0; i < columnsCount; i++) {
            if (!item[i]) {
              excelData[index].push('');
            } else {
              excelData[index][i] = String(excelData[index][i]);
            }
            // 清空 forbiddenField 列的值
            const columnName = this.data[rowOffset - 1][i];
            if (this.forbiddenField.includes(columnName) && item[i]) {
              item[i] = '';
              this.toastService.showToast('warning',
                `${columnName}会被清空，导入填写项无法回显,请在编辑中选择${columnName}`,
                null,
                {timeOut: 3000}
              );
            }
          }
        }
      });
        // 动态生成校验规则
        const validateRules = this.data[rowOffset - 1].map((column, index) => {
          const rules = [];
          if (this.requiredFields.includes(column)) {
              rules.push({ rule: 'required' });
          }
          if (column === '编号') {
            rules.push({ 'rule': 'uniqueField', 'params': assetCodeList });
          }
          rules.push({ rule: 'maxlength', params: 255 });
          return rules;
      });  
      this.validateResult = this.excelValidationService.dataValidate(excelData, validateRules, 1);
      if (this.validateResult.length !== 0) {// validation failed
        this.excelImportSwal.showConfirmButton = true;
        this.excelData = excelData;
        this.excelImportSwal.fire().then(() => {
          evt.target.value = '';
        });
        return;
      }
      evt.target.value = '';
      const batchList = this.dataToList(excelData);
      this.dataImport(batchList);
    };
    reader.readAsBinaryString(target.files[0]);
  }

  dataToList(data) {
    const assetDeviceList: Array<any> = new Array<any>();
    if (data && data.length > 0) {
      data.map((item) => {
        if (item && item.length > 0) {
          assetDeviceList.push(this.getTempModel(item));
        }
      });
    }
    return assetDeviceList;
  }

  dataImport(params) {
    const param = {
      importType: this.importType,
      saveAssetCategory: this.saveAssetCategory,
      assetDeviceDTOList: params
    }
    this.service.create(param, 'idcassetdevices/list').subscribe(res => {
      this.setTableColumnSettings();
      this.toastService.showToast(
        'success',
        this.columnHeaders['asset.common.excelImportSuccess'],
        null
      );
    },
      err => {
        this.toastService.showToast(
          'error',
          this.columnHeaders['asset.common.excelImportError'],
          null
        );
      });
  }

  getTempModel(item) {
    const tempModel: { [key: string]: any } = {}
    let i = 0;
    const importColumns = this.defaultColumns.filter(i => i.field !== 'operate')
    for (const column of importColumns) {
      const field = column.field;
      tempModel[field] = item[i++];
    }
    tempModel['extValueConfigurationList'] = Object.keys(tempModel)
      .filter(key => key.startsWith('extId'))
      .map(key => ({
        extId: key.replace('extId', ''), 
        extValue: tempModel[key]
      }));
    return tempModel;
  }



  exportErrorExce1() {
    this.fileName = this.columnHeaders['asset.assetDevice.title'] + '.xlsx';
    const xlsTemplateJson = [
      // ['', '', '', '', '', '', this.columnHeaders['asset.assetDevice.title']],
      // []
    ];
    const columnTitles = this.defaultColumns.filter(i => i.field !== 'operate').map((col) => col.title || '');
    xlsTemplateJson.push(columnTitles);

    for (let i = 0; i < this.excelData.length; i++) {
      for (let j = 0; j < this.excelData[i].length; j++) {
        if (!this.excelData[i][j]) {
          this.excelData[i][j] = '';
        }
      }
    }
    const validateDataList = new Array<ValidateData>();
    for (let i = 0; i < this.validateResult.length; i++) {
      const tempValidate = new ValidateData();
      const validArray = this.validateResult[i].split(',');
      const tempRow = validArray[0].split(':')[1];  // 获取行号；
      const tempColumn = validArray[1].split(':')[1];  // 获取列字母；
      const columnNum = this.excelValidationService.lettersToNumber(tempColumn); // 获取列索引
      const validateText = validArray[2];  // 获取列字母
      tempValidate.validateColumn = columnNum.toString();
      tempValidate.validateRow = tempRow;
      tempValidate.validateText = validateText;
      validateDataList.push(tempValidate);
    }
    this.excelValidationService.downloadExcelFile(xlsTemplateJson, this.excelData, validateDataList, this.fileName, 'Sheet1');

  }


  export() {
    if (this.selectItem && this.selectItem.length > 0) {
      this.assetDeviceData = _.orderBy(this.selectItem, ['assetDeviceId'], ['asc']);
    } else {
      this.assetDeviceData = this.currentAssetDeviceData;
    }
    if (!this.assetDeviceData || this.assetDeviceData.length <= 0) {
      return;
    }
    const workbookData = this.exportExcelService.exportExcel(this.assetDeviceData, this.extendData);
    const fileName = this.columnHeaders['asset.assetDevice.excelFileName'];
    this.excelService.exportExcelFile(workbookData, fileName).subscribe(
      res => {
        this.toastService.showToast('success', this.columnHeaders['general.common.downloadSuccess'], null);
        return true;
      },
      err => {
        this.toastService.showToast(
          'error',
          this.columnHeaders['report.batteryAlarmHistoryReport.downloadExcelFileFail'],
          null
        );
        return false;
      }
    );
  }

  /**
   * 导出全部
   */
  exportAll(): void {
    const header = new HttpHeaders().set('content-Type', 'application/json');
    this.http.post('idcassetdevices/export', {}, { headers: header, responseType: 'blob' }).pipe(
      map(_res => {
        if (_res) {
          const data = new Blob([_res], { type: 'application/vnd.ms-excel,charset=utf-8' });
          FileSaver.saveAs(data, this.columnHeaders['asset.assetDevice.excelFileName']);
          this.toastService.showToast('success', this.columnHeaders['general.common.downloadSuccess'], null);
        }
      }),
      catchError(err => {
        this.toastService.showToast(
          'error',
          this.columnHeaders['report.batteryAlarmHistoryReport.downloadExcelFileFail'],
          null
        );
        return of(null);
      })
    ).subscribe();
  }


  add(): void {
    this.router.navigate(['./add'], { relativeTo: this.activatedRoute });
  }

  edit(data) {
    this.router.navigate(['./edit', data.assetDeviceId], { relativeTo: this.activatedRoute });
  }

  deleteAsset(data) {
    const content =
      this.columnHeaders['general.common.confirmDelete'] + data.assetName;
    this.alertService.deleteConfirm(content).then(res => {
      if (res.value) {
        this.service.delete(data.assetDeviceId, 'idcassetdevices').subscribe(() => {
          for (let i = 0; i < this.currentAssetDeviceData.length; i++) {
            if (data.assetDeviceId === this.currentAssetDeviceData[i]['assetDeviceId']) {
              this.currentAssetDeviceData.splice(i, 1);
            }
          }
          this.selectItem = _.filter(this.selectItem, every => {
            return every.assetDeviceId !== data.assetDeviceId;
          });
          this.source = this.currentAssetDeviceData
          this.alertService.deletedSucceed();
        });
      }
    });
    this.getData();
  }
  customColumns() {
    const modal = this.modal.open(CustomColumnsComponent, {
			container: 'app-asset-device-list',
			backdrop: 'static',
		});
    modal.componentInstance.columns = this.settings.columns
		modal.result.then((config) => {
			if (config) {
        this.settings.columns = config
				this.saveCustomColumn();
			}
		});
  }
  fetchCustomColumn() {
		this.alarmService
			.getAFilter('activeeventfiltertemplates?filterType=' + this.ColumnCustomType)
			.subscribe((res: any) => {
				if (res && res.length > 0) {
          const findData = res.find(item => item.activeEventFilterTemplateId === this.savedCustomColumnId)
          const data = findData ? findData : res[0]
          let configColumns = JSON.parse(data.content)
          configColumns = configColumns.filter(i => 
            this.defaultColumns.some(j => j.field === i.field)
          );
          const missColumns = []
          this.defaultColumns.forEach(i => {
            const fieldExists = configColumns.some(j => j.field === i.field);
            if (!fieldExists) {
              missColumns.push(i);
            }
          });
         this.settings.columns = [...configColumns,...missColumns]
				 this.savedCustomColumnId = data.activeEventFilterTemplateId;
				} else {
          this.settings.columns = this.defaultColumns
				}
			}, error => {
			});
	}
  saveCustomColumn() {
		const para = {
			personId: parseInt(this.sessionService.getPersonId()),
			templateName: this.ColumnCustomType,
			filterType: this.ColumnCustomType,
      content: JSON.stringify(this.settings.columns),
			description: ' Asset Device List Event Page Column Custom',
		};
		if (this.savedCustomColumnId) {
			para['activeEventFilterTemplateId'] = this.savedCustomColumnId;
			this.alarmService.updateTemplate(para).subscribe(ret => {
				this.fetchCustomColumn();
			});
		} else {
			this.alarmService.saveTemplate(para).subscribe((ret:any) => {
        this.savedCustomColumnId = ret && ret.activeEventFilterTemplateId
				this.fetchCustomColumn();
			});
		}
	}
  
  importFromEqu(btn :HTMLButtonElement) {
    btn.blur();
    this.alertService.confirm({
      title: this.translate.instant("general.common.tip"),
      html: this.translate.instant("equipmentManagement.equipmentLedger.syncLongTimeWarning"),
      confirmButtonText: this.translate.instant("general.common.ok"),
      showCancelButton: false
    }).then(({ value: confirmed }) => {
      if (!confirmed) return;
  
      this.isLoading = true;
  
      this.assetService.importFromEqu().pipe(
        catchError((err) => {
          this.toastService.showToast("warn",this.translate.instant("equipmentManagement.equipmentLedger.syncDataLarge"),null);
          this.isLoading = false;
          return of(null);
        })
      ).subscribe((res: any) => {
        this.isLoading = false;
  
        if (typeof res === 'string') {
          this.toastService.showToast('success',this.translate.instant("equipmentManagement.equipmentLedger.syncSuccess"),null);
          this.getData();
        } else {
          this.toastService.showToast('error',this.translate.instant("equipmentManagement.equipmentLedger.syncError"),null);
        }
      });
    });
  }
  

  batchDelete(){
    const content = this.columnHeaders['general.common.confirmDelete2'] ;
    this.alertService.deleteConfirm(content).then(res => {
      if (res.value) {
        const ids = this.selectItem.map(item => item.assetDeviceId);
        this.assetService.batchDelete(ids).subscribe(() => {
          this.toastService.showToast('success', this.columnHeaders['general.common.deleteSuccess'], null);
          this.getData();
        });
      }
    });
  }
  
}
