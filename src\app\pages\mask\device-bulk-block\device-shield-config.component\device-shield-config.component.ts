import { Component, OnInit, AfterViewInit, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastService } from '@communal/providers';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import { DeviceShieldEventService } from './device-shield-config.service'
import { DateUtil } from '@app/communal/utils/DateUtil';
import { Masktime } from './model/week.model';
import { Timespan } from './model/timespan.model';
import { AlertService } from '@communal/providers/alert.service';

@Component({
    selector: 'app-device-shield-config',
    templateUrl: './device-shield-config.component.html',
    styleUrls: ['./device-shield-config.component.scss']
})

export class DeviceShieldConfigComponent implements OnInit {
    @Input() equipmentId: string | number;
    @Input() stationId: string | number;
    @Input() eventId: string | number;
    @Input() timeGroupCategory: number;
    @Input() timeGroupSpans;
    @Input() InputstartTime: string;
    @Input() InputendTime: string;
    @Input() Inputreason: string;
    @Input() showDelete: boolean = false;


    startTime: any;
    endTime: any;
    selectedItemList: any[];
    activeid: number;
    configObjectIds: string[] = [];
    maskTimeGroup: Masktime = new Masktime();
    reason: string;
    saveAsTemplate = 'false';
    public confirmDelete = this.translate.instant("s2.reportmanagement.report_task.confirmdelete");
    dateDisabled = false;
    deleteDisabled = true;

    optionsStartTime: any = {
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        timePickerSeconds: true,
        autoUpdateInput: false,
        showDropdowns: true,
        locale: {
            format: 'YYYY-MM-DD',
        },
        minDate: new Date(),
    };

    optionsEndTime: any = {
        singleDatePicker: true,
        timePicker: true,
        timePicker24Hour: true,
        timePickerSeconds: true,
        autoUpdateInput: false,
        showDropdowns: true,
        locale: {
            format: 'YYYY-MM-DD',
        },
        minDate: new Date(),
    };

    shieldtype = [{
        value: 1,
        name: 's2.shieldevent.allTime'
    }, {
        value: 2,
        name: 's2.shieldevent.periodTime'
    }];
    langKeys = [
        'general.common.noData',
        'general.common.action',
        'ngForm.create.success',
        'ngForm.create.error',
        'selectResource.submit',
        's2.shieldevent.allSave',
        's2.shieldevent.allDelete',
        's2.shieldevent.delete',
        'general.dateOptions.Mo',
        'general.dateOptions.Tu',
        'general.dateOptions.We',
        'general.dateOptions.Th',
        'general.dateOptions.Fr',
        'general.dateOptions.Sa',
        'general.dateOptions.Su',
        'general.dateOptions.January',
        'general.dateOptions.February',
        'general.dateOptions.March',
        'general.dateOptions.April',
        'general.dateOptions.May',
        'general.dateOptions.June',
        'general.dateOptions.July',
        'general.dateOptions.August',
        'general.dateOptions.September',
        'general.dateOptions.October',
        'general.dateOptions.November',
        'general.dateOptions.December',
        's2.devicemonitor.alarmColumns.meaning',

        "s2.reportmanagement.alert.confirm",
        "s2.reportmanagement.alert.cancel",

        "s2.calendar.January",
        "s2.calendar.February",
        "s2.calendar.March",
        "s2.calendar.April",
        "s2.calendar.May",
        "s2.calendar.June",
        "s2.calendar.July",
        "s2.calendar.August",
        "s2.calendar.September",
        "s2.calendar.October",
        "s2.calendar.November",
        "s2.calendar.December",

        "s2.calendar.Sunday",
        "s2.calendar.Monday",
        "s2.calendar.Tuesday",
        "s2.calendar.Wednesday",
        "s2.calendar.Thursday",
        "s2.calendar.Friday",
        "s2.calendar.Saturday",
    ];
    chooseable = false;
    langValues: any;
    shieldt: number;
    spantime1: any[] = [];
    spantime2: any[] = [];
    spantime3: any[] = [];
    spantime4: any[] = [];
    spantime5: any[] = [];
    spantime6: any[] = [];
    spantime7: any[] = [];
    title: string[] = ["00:00-00:30", "00:30-01:00", "01:00-01:30", "01:30-02:00", "02:00-02:30",
        "02:30-03:00", "03:00-03:30", "03:30-04:00", "04:00-04:30", "04:30-05:00", "05:00-05:30",
        "05:30-06:00", "06:00-06:30", "06:30-07:00", "07:00-07:30", "07:30-08:00", "08:00-08:30",
        "08:30-09:00", "09:00-09:30", "09:30-10:00", "10:00-10:30", "10:30-11:00", "11:00-11:30",
        "11:30-12:00", "12:00-12:30", "12:30-13:00", "13:00-13:30", "13:30-14:00", "14:00-14:30",
        "14:30-15:00", "15:00-15:30", "15:30-16:00", "16:00-16:30", "16:30-17:00", "17:00-17:30",
        "17:30-18:00", "18:00-18:30", "18:30-19:00", "19:00-19:30", "19:30-20:00", "20:00-20:30",
        "20:30-21:00", "21:00-21:30", "21:30-22:00", "22:00-22:30", "22:30-23:00",
        "23:00-23:30", "23:30-00:00",]
    weeks: any[] = [];
    constructor(
        private NgbModal: NgbActiveModal,
        private toastService: ToastService,
        private translate: TranslateService,
        private router: Router,
        private alertService: AlertService,
        private service: DeviceShieldEventService
    ) {
        for (var i = 0; i < 48; ++i) {
            this.spantime1[i] =
            {
                selected: false,
                index: i,
                title: this.title[i]
            };
        }
        for (var i = 0; i < 48; ++i) {
            this.spantime2[i] =
            {
                selected: false,
                index: i,
                title: this.title[i]
            };
        }
        for (var i = 0; i < 48; ++i) {
            this.spantime3[i] =
            {
                selected: false,
                index: i,
                title: this.title[i]
            };
        }
        for (var i = 0; i < 48; ++i) {
            this.spantime4[i] =
            {
                selected: false,
                index: i,
                title: this.title[i]
            };
        }
        for (var i = 0; i < 48; ++i) {
            this.spantime5[i] =
            {
                selected: false,
                index: i,
                title: this.title[i]
            };
        }
        for (var i = 0; i < 48; ++i) {
            this.spantime6[i] =
            {
                selected: false,
                index: i,
                title: this.title[i]
            };
        }
        for (var i = 0; i < 48; ++i) {
            this.spantime7[i] =
            {
                selected: false,
                index: i,
                title: this.title[i]
            };
        }
    }

    ngOnInit() {
        this.initTranslate();
        if (this.timeGroupSpans && this.timeGroupSpans.length > 0) {
            this.showTimeSpan();
        }
        if (this.timeGroupCategory) {
            this.shieldt = this.timeGroupCategory;
            // this.shieldt = 1;
        }
        if (this.InputstartTime) {
            this.startTime = this.InputstartTime;
        }
        if (this.InputendTime) {
            this.endTime = this.InputendTime;
        }
        if (this.Inputreason) {
            this.reason = this.Inputreason;
        }
    }

    initTranslate() {
        this.translate.get(this.langKeys).subscribe(res => {
            this.langValues = res;

            this.optionsStartTime.locale = {
                format: 'YYYY-MM-DD',
                cancelLabel: this.langValues['s2.reportmanagement.alert.cancel'],
                applyLabel: this.langValues['s2.reportmanagement.alert.confirm'],
                daysOfWeek: [
                    this.langValues['s2.calendar.Sunday'],
                    this.langValues['s2.calendar.Monday'],
                    this.langValues['s2.calendar.Tuesday'],
                    this.langValues['s2.calendar.Wednesday'],
                    this.langValues['s2.calendar.Thursday'],
                    this.langValues['s2.calendar.Friday'],
                    this.langValues['s2.calendar.Saturday']],
                monthNames: [
                    this.langValues['s2.calendar.January'],
                    this.langValues['s2.calendar.February'],
                    this.langValues['s2.calendar.March'],
                    this.langValues['s2.calendar.April'],
                    this.langValues['s2.calendar.May'],
                    this.langValues['s2.calendar.June'],
                    this.langValues['s2.calendar.July'],
                    this.langValues['s2.calendar.August'],
                    this.langValues['s2.calendar.September'],
                    this.langValues['s2.calendar.October'],
                    this.langValues['s2.calendar.November'],
                    this.langValues['s2.calendar.December'],
                ],
            },
                this.optionsEndTime.locale = this.optionsStartTime.locale;

            this.initdata();
            this.weeks =
                [
                    {
                        name: this.langValues['general.dateOptions.Mo'],
                        selected: false,
                        time: this.spantime1,
                        id: 2
                    },
                    {
                        name: this.langValues['general.dateOptions.Tu'],
                        selected: false,
                        time: this.spantime2,
                        id: 3
                    },
                    {
                        name: this.langValues['general.dateOptions.We'],
                        selected: false,
                        time: this.spantime3,
                        id: 4
                    },
                    {
                        name: this.langValues['general.dateOptions.Th'],
                        selected: false,
                        time: this.spantime4,
                        id: 5
                    },
                    {
                        name: this.langValues['general.dateOptions.Fr'],
                        selected: false,
                        time: this.spantime5,
                        id: 6
                    },
                    {
                        name: this.langValues['general.dateOptions.Sa'],
                        selected: false,
                        time: this.spantime6,
                        id: 7
                    },
                    {
                        name: this.langValues['general.dateOptions.Su'],
                        selected: false,
                        time: this.spantime7,
                        id: 1
                    },
                ];
        });
    }

    initdata() {
        this.shieldt = 1;
        this.startTime = DateUtil.dateFormat(new Date(), 'YYYY-MM-DD HH:mm:ss');
        this.endTime = DateUtil.dateFormat(new Date("2050-1-1"), 'YYYY-MM-DD HH:mm:ss');
    }

    selectTime(value: any, state: string) {
        const date = state === 'startTime' ? 'startDate' : 'endDate';
        const val = value.picker[date]['_d'];
        const time = DateUtil.dateToString(val);
        if (state === 'startTime') {
            this.startTime = time;
            this.optionsEndTime.minDate = this.startTime;
        } else {
            this.endTime = time;
        }
    }

    changeShieldtype(shieldt) {
        if (shieldt === 1) {
            this.dateDisabled = false;
        }
        else if (shieldt === 2) {
            this.dateDisabled = true;
        }
    }

    isCheck(name, index) {
        if (this.shieldt === 2) {
            this.weeks.forEach(item => {
                if (item['name'] === name) {
                    item['time'].forEach(time => {
                        if (time['index'] === index) {
                            time['selected'] = !time['selected'];
                        }
                    }
                    );
                }
            });
        }
    }

    isAllCheck(name) {
        if (this.shieldt === 2) {
            this.weeks.forEach(item => {
                if (item['name'] === name) {
                    item['time'].forEach(time => {
                        if (item['selected']) {
                            time['selected'] = false;
                        }
                        else {
                            time['selected'] = true;
                        }
                    }
                    );
                    if (item['selected']) {
                        item['selected'] = false;
                    }
                    else {
                        item['selected'] = true;
                    }
                }
            });
        }
    }

    showToast(type, msg) {
        this.toastService.showToast(type, this.translate.instant(msg), '');
    }

    submitMenu() {
        if (this.reason == "" || this.reason == null) {
            this.toastService.showToast('error', this.translate.instant('general.mask.commentNotEmpty'), null);
            return;
        }
        this.maskTimeGroup.timeSpanBool = [];
        this.maskTimeGroup.timeSpanBool = new Array<Timespan>();
        if (this.shieldt === 1) {
            this.maskTimeGroup.timeSpanBool = null;
            if (this.startTime >= this.endTime) {
                const timeJudge = this.translate.instant('general.mask.timeJudge');
                this.toastService.showToast('error', timeJudge, '');
                return;
            }
        }
        else {
            this.weeks.forEach(week => {
                const temp = new Timespan();
                temp.timeSpanBool = [];
                temp.week = week.id;
                week.time.forEach(span => {
                    temp.timeSpanBool[span.index] = span.selected;
                });
                this.maskTimeGroup.timeSpanBool.push(temp);
            });
        }
        const param =
        {
            startTime: this.startTime,
            endTime: this.endTime,
            stationId: this.stationId,
            equipmentId: this.equipmentId,
            reason: this.reason,
            timeGroupCategory: this.shieldt,
            timeGroupSpans: this.maskTimeGroup.timeSpanBool,
        };

        if (this.eventId) {
            param['eventId'] = this.eventId;
        }
        this.closeCreateMenuModal(param);
    }
    closeCreateMenuModal(param) {
        this.NgbModal.close(param);
    }

    showTimeSpan() {
        for (var i = 0; i < 7; ++i) {
            if (i != 6) {
                var temp = this.timeGroupSpans.filter(time => time.week === i + 2);
                for (var j = 0; j < 48; ++j) {
                    this.weeks[i].time[j].selected = temp[0].timeSpanBool[j];
                }
            }
            else {
                var temp = this.timeGroupSpans.filter(time => time.week === 1);
                for (var j = 0; j < 48; ++j) {
                    this.weeks[i].time[j].selected = temp[0].timeSpanBool[j];
                }
            }
        }
    }

    close() {
        this.NgbModal.close();
    }

    delete() {
        this.NgbModal.close('delete');
    }
}
