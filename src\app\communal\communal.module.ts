import { HtmlPipe } from './pipes/html.pipe';
import { TriggerConditionsComponent } from '@app/pages/monitor/battery/battery-cell-overview/trigger-conditions/trigger-conditions.component';
import { ExcelService } from './providers/excel.service';
import { ExportExcelFileService } from './providers/exportExcelFile.service';
import {
  CUSTOM_ELEMENTS_SCHEMA,
  NgModule,
  NO_ERRORS_SCHEMA,
} from '@angular/core';
import { Ng2SmartTableModule } from 'ng2-smart-table';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ModuleWithProviders } from '@angular/core';
import { AppTranslationModule } from '@app/app.translation.module';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FileUploadModule } from 'ng2-file-upload';
import { BaseListViewComponent } from './components/base-list-view/base-list-view.component';
import { TableBaseCellComponent } from './components/base-list-view/table-base-cell/table-base-cell.component';
import { CellDownloadViewComponent } from './components/cell-download-view/cell-download-view.component';
import { ImagePreviewComponent } from './components/cell-download-view/image-preview/image-preview.component';
import { FileUploadViewComponent } from './components/file-upload-view/file-upload-view.component';
import { ImageUploaderComponent } from '@components/image-uploader/image-uploader.component';
import { TagsComponent } from './components/tags/tags.component';
import { TagAddComponent } from './components/tags/components/tag-add/tag-add.component';
import { DefaultModal, DelModalComponent } from './components';
import {
  EmailService,
  ToastService,
  AlertService,
  FileService,
} from './providers';
import { TaskDeviceFormComponent } from '@components/task-device-form/task-device-form.component';
import { TaskDeviceFromService } from '@components/task-device-form/service/task-device-from.service';
import { PhotographModalComponent } from '@components/photograph-modal/photograph-modal.component';
import { SelectPoritComponent } from './components/select-porit/select-porit.component';
import { SelectCorePointComponent } from './components/select-core-point/select-core-point.component';
import { SelectIndicatorComponent } from './components/select-indicator/select-indicator.component';
import { SelectIndicatorEnergyComponent } from '@app/pages/energy-management/multidimensional-config/components/select-indicator/select-indicator.component';
import { TimerSettingComponent } from './components/timer-setting/timer-setting.component';
import { TimerSettingService } from '@components/timer-setting/timer-setting.service';
import { TimerSettingPipe } from '@components/timer-setting/timer-setting.pipe';
import { EnumToArrayPipe } from './pipes/enum-to-array.pipe';
import { EacherTempService } from '@communal/providers/eacherTempService/eacherTemp.service';
import { TagService } from '@app/communal/components/tags/service/tag.service';
import { CoreModule } from '@app/core/core.module';
import { TagColumnComponent } from './components/tags/components/tag-column/tag-column.component';
import { TagEditComponent } from './components/tags/components/tag-edit/tag-edit.component';
import { PersonComponent } from './components/person/person.component';
import { PersonsService } from '@app/pages/admin/human/persons/persons.service';
import { SelectModule } from 'ng-select';
import { SweetAlert2Module } from '@sweetalert2/ngx-sweetalert2';
import { BasicStatisticsComponent } from './components/basic-statistics/basic-statistics.component';
import { AlarmSeverityImgRenderComponent } from '@app/pages/monitor/components/alarm-severity-img-render/alarm-severity-img-render-componet';
import { PointStatusItemComponent } from '@app/pages/monitor/device-detail/live-point/custom-table-items/point-status-item-componet';
import { PointOperationItemComponent } from '@app/pages/monitor/device-detail/live-point/custom-table-items/point-operation-item-componet';
import { LivePointValueSetComponent } from '@app/pages/monitor/device-detail/live-point/live-point-value-set/live-point-value-set.component';
import { AlarmPostionStatComponent } from '@app/pages/monitor/components/alarm-postion-stat/alarm-postion-stat.component';
import { AlarmLevelStatComponent } from '@app/pages/monitor/components/alarm-level-stat/alarm-level-stat.component';
import { DeviceStatusStatComponent } from '@app/pages/monitor/components/device-status-stat/device-status-stat.component';
import { AlarmRealListComponent } from '@app/pages/monitor/components/alarm-real-list/alarm-real-list.component';
import { AlarmTrendChartComponent } from '@app/pages/monitor/components/alarm-trend-chart/alarm-trend-chart.component';
import { D3Service } from 'd3-ng2-service';
import { AlarmPostionStatService } from '@app/pages/monitor/components/alarm-postion-stat/alarm-postion-stat.service';
import { AlarmLevelStatService } from '@app/pages/monitor/components/alarm-level-stat/alarm-level-stat.service';
import { DeviceStatusStatService } from '@app/pages/monitor/components/device-status-stat/device-status-stat.service';
import { AlarmTrendChartService } from '@app/pages/monitor/components/alarm-trend-chart/alarm-trend-chart.service';
import { AlarmRealListService } from '@app/pages/monitor/components/alarm-real-list/alarm-real-list.service';
import { NgxEchartsModule } from 'ngx-echarts';
import { BasicWidgetComponent } from './components/basic-widget/basic-widget.component';
import { PieChartComponent } from './components/pie-chart/pie-chart.component';
import { DateComponent } from './components/daterange/date-component';
import { ArrayListPipe } from './pipes';
import { BooleanValueConvertPipe } from './pipes/boolean-value-convert.pipe';
import { EmptyValueConvertPipe } from './pipes/empty-value-convert.pipe';
import { SliceMixinPipe } from './pipes/slice-mixin.pipe';
import { StrInterceptPipe } from './pipes/strIntercept.pipe';
import { PieChartOptionsComponent } from './components/pie-chart/pie-chart-options/pie-chart-options.component';
import { LoadingComponent } from './components/loading/loading.component';
import { PieChartService } from './components/pie-chart/pie-chart.service';
import { WidgetPanelSettingComponent } from './components/basic-widget/widget-panel-setting/widget-panel-setting.component';
import { WidgetTemplateComponent } from './components/basic-widget/widget-template/widget-template.component';
import { DebounceDirective } from '@app/core/directives/debounceDirective/debounce-directive';
import { IconSelectorComponent } from './components/icon-selector/icon-selector.component';
import { CurveChartComponent } from './components/curve-chart/curve-chart.component';
import { TaskTrendService } from './components/curve-chart/task-trend.service';
import { CurveChartOptionsComponent } from './components/curve-chart/curve-chart-options/curve-chart-options.component';
import { RangeColorSelectorComponent } from './components/range-color-selector/range-color-selector.component';
import { AlarmOverviewComponent } from '@app/pages/monitor/alarm-overview/alarm-overview.component';
import { FormulaConfigComponent } from './components/formula-config/formula-config.component';
import { ThemeDashboardService } from '@app/theme/theme.dashboard.service';
import { CronSelectorComponent } from './components/cron-selector/cron-selector.component';
import { IndicatorUnitSelector } from './components/indicator-unit-selector/indicator-unit-selector.component';
import { ViewContainRefHostDirective } from '@app/@hmi/directive/view-contain-ref-host.directive';
import { ComplexIndexFunctionComponent } from './components/complex-index-function/complex-index-function';
import { ViewContainRefViewDirective } from '@app/@hmi/directive/view-contain-ref-view.directive';
import { SelectSignalComponent } from './components/select-signal/select-signal.component';
import { SignalEventExpressionComponent } from './components/signal-event-expression/signal-event-expression.component';
import { BaseCRUDService } from './providers/baseCRUDService/baseCRUDService';
import { TopographyComponent } from '@app/communal/components/topography/topography.component';
import { CompDevModule } from '@app/@hmi/component/dev/comp-dev.module';
import { CompListComponent } from '@app/@hmi/editor/comp-list/comp-list.component';
import { CompTemplateComponent } from '@app/@hmi/editor/comp-template/comp-template.component';
import { CompTemplateListComponent } from '@app/@hmi/editor/comp-template-list/comp-template-list.component';
import { DevelopmentPageComponent } from '@app/@hmi/editor/development';
import { CompSettingComponent } from '@app/@hmi/editor/comp-setting/comp-setting.component';
import { CompPageDetailComponent } from '@app/@hmi/editor/comp-page-detail/comp-page-detail.component';
import { CompManagementComponent } from '@app/@hmi/editor/comp-management/comp-management.component';
import { TreeModule as TM } from '@circlon/angular-tree-component';
import { TreeModule } from 'ng2-tree';
import { AlarmOperationItemComponent } from '@app/pages/monitor/alarm-list/alarm-operation-item-componet';
import { TreeSelectorComponent } from './components/tree-selector/tree-selector.component';
import { GraphicPageModalComponent } from './components/graphic-page-modal/graphic-page-modal.component';
import { TreeEventSelectorComponent } from './components/tree-event-selector/tree-event-selector.component';
import { SetMaskComponent } from './components/set-mask/set-mask.component';
import { IndexAllocationListComponent } from '@app/pages/idc-manage/components/hierarchy/components/index-allocation-list/index-allocation-list.component';
import { IndicatorEditComponent } from '@app/pages/idc-manage/components/hierarchy/components/indicator-edit/indicator-edit.component';
import { PageCompListComponent } from '@app/@hmi/editor/page-comp-list/page-comp-list.component';
import { CustomSmartTableComponent } from './components/custom-smart-table/custom-smart-table.component';
import { IndicatorSelectorComponent } from './components/indicator-selector/indicator-selector.component';
import { CapacityCellSelectorComponent } from './components/capacity-cell-selector/capacity-cell-selector.component';
import { BasicSignalSelectorComponent } from './components/basic-signal-selector/basic-signal-selector.component';
import { RackSelectorComponent } from './components/rack-selector/rack-selector.component';
import { ViewBasicComponent } from '@app/@hmi/viewer/view-basic/view-basic.component';
import { RackHistoryComponent } from './components/rack-history/rack-history.component';
import { RackSearchComponent } from './components/rack-search/rack-search.component';
import { RackModalComponent } from './components/rack-modal/rack-modal.component';
import { ForewarmingListPanelComponent } from './components/forewarming-list-panel/forewarming-list-panel.component';
import { SelectTheBuildingComponent } from './components/select-devices/select-building.component';
import { SelectTheFloorComponent } from './components/select-devices/select-floor.component';
import { SelectTheRoomComponent } from './components/select-devices/select-room.component';
import { SelectTheServerityComponent } from './components/select-devices/select-serverity.component';
import { IDCRackWeightComponent } from '@app/pages/idc-manage/components/rack-management/rack-edit/rack-weight/rack-weight.component';
import { BaseTypeSelectorComponent } from './components/base-type-selector/base-type-selector.component';
import { RackPointSelector } from './components/rack-point-selector/rack-point-selector.component';
import { MachineCalendarPopupComponent } from './components/machine-calendar-popup/machine-calendar-popup.component';
import { IndicatorTemplateSelectorComponent } from './components/indicatorTemplate-selector/indicatorTemplate-selector.component';
import { ExpressSelectorComponent } from '@app/pages/idc-manage/components/hierarchy/components/express-selector/express-selector.component';
import { ExpressSelectorComponentEnergy } from '@app/pages/energy-management/multidimensional-config/components/express-selector/express-selector.component';
import { DynamicCurveComponent } from './components/dynamic-curve/dynamic-curve.component';
import { DynamicCurveModalComponent } from './components/dynamic-curve/dynamic-curve-modal/dynamic-curve-modal.component';
import { SingleFileUploadComponent } from './components/single-file-upload/single-file-upload.component';
import { CollectCommunicationSelectorComponent } from './components/collect-communication-selector/collect-communication-selector.component';
import { HistoryCurveComponent } from './components/dynamic-curve/history-curve/history-curve.component';
import { Daterangepicker } from 'ng2-daterangepicker';
import { CurveDataExportService } from './components/dynamic-curve/history-curve/curve-data-export.service';
import { HierarchySelectorComponent } from './components/hierarchy-selector/hierarchy-selector.component';
import { ThreeDObjectSelectorComponent } from './components/three-d-object-selector/three-d-object-selector.component';
import { NewRackSelectorComponent } from './components/new-rack-selector/new-rack-selector.component';
import { DevicePointSelectorComponent } from './components/device-point-selector/device-point-selector.component';
import { SelectObjectsComponent } from './components/select-objects/select-objects.component';
import { ConfiguationBreadcrumbComponent } from './components/configuation-breadcrumb/configuation-breadcrumb.component';
import { DynamicComponentServiceService } from '@app/@hmi/providers/dynamic-component-service.service';
import { HcmService } from '@app/@hmi/hcm.service';
import { GraphicPageAddComponent } from '@app/pages/idc-manage/components/device-management/components/graphic-page-add/graphic-page-add.component';
import { DeviceListComponent } from '@app/pages/idc-manage/components/device-edit/components/device-list/device-list.component';
import { DeviceListDialogComponent } from '@app/pages/idc-manage/components/device-management/components/device-management-edit-dialog/device-list.component';
import { BaseSignalConfigComponent } from '@app/pages/site/auto-patrol/group-management/basesignal-config/basesignal-config.component';
import { PreviewFilterParamComponent } from '@app/pages/site/components/params-filter/preview-filter-param.component';
import { IndicatorHistoryChartSettingComponent } from './components/indicator-history-chart-setting/indicator-history-chart-setting.component';
import { IndicatorTableValColumnComponent } from './components/indicator-history-chart-setting//indicator-table-val-column/indicator-table-val-column.component';
import { IndicatorTableValRowComponent } from './components/indicator-history-chart-setting//indicator-table-val-row/indicator-table-val-row.component';
import { IndicatorProgressChartSettingComponent } from './components/indicator-progress-chart-setting/indicator-progress-chart-setting.component';
import { AnimationDisplaySettingComponent } from './components/animation-display-setting/animation-display-setting.component';
import { IndicatorDoughnutChartSettingComponent } from './components/indicator-doughnut-chart-setting/indicator-doughnut-chart-setting.component';
import { IndicatorDoubleProgressChartSettingComponent } from './components/indicator-doubleprogress-chart-setting/indicator-doubleprogress-chart-setting.component';
import { CapacityAttributesComponent } from '@app/pages/idc-manage/components/capacity-management/capacity-attributes/capacity-attributes.component';
import { TreeStructureSelectorComponent } from './components/tree-structure-selector/tree-structure-selector.component';
import { SetStructureTreeComponent } from './components/set-structure-tree/set-structure-tree.component';
import { GlobalResourceNodeSelectorComponent } from './components/globalResourceNode-selector/globalResourceNode-selector.component';
import { SceneStructureTreeModule } from '@app/pages/idc-manage/components/hierarchy/scene-structure-tree/scene-structure-tree.module';
import { SelectCapacityComponent } from './components/select-capacity/select-capacity.component';
import { PreAlarmAllocationlistComponent } from '@app/pages/idc-manage/components/prealarm-management/prealarm-allocation-list/prealarm-allocation-list.component';
import { PreAlarmExpressSelectorComponent } from '@app/pages/idc-manage/components/prealarm-management/prealarmexpress-selector/prealarmexpress-selector.component';
import { CapacitySelectorComponent } from './components/capacity-selector/capacity-selector.component';
import { ViewComponent } from '@app/@hmi/viewer/view/view.component';
import { UpdatePasswordComponent } from '@app/pages/admin/personal/update-password/update-password.component';
import { ForgetPasswordComponent } from '@app/pages/admin/personal/forget-password/forget-password.component';
import { UpdatePasswordService } from '@app/pages/admin/personal/update-password/update-password.service';
import { ForgetPasswordService } from '@app/pages/admin/personal/forget-password/forget-password.service';
import { CameraSelectorComponent } from './components/camera-selector/camera-selector.component';
import { CameraTreeModule } from '@app/pages/video-management/camera-tree/camera-tree.module';
import { ControlSelectorComponent } from './components/control-selector/control-selector.component';
import { ControlSetModalComponent } from './components/control-set-modal/control-set-modal.component';
import { DiagramSelectorComponent } from './components/diagram-selector/diagram-selector.component';
import { DeviceShieldConfigComponent } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.component';
import { DeviceShieldEventService } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.service';
import { TreeEquipmentSelectorComponent } from './components/tree-equipment-selector/tree-equipment-selector.component';
import { LocalEntryComponent } from '@app/pages/access-control/access-card-management/local-entry/local-entry.component';
import { LocalPhotographComponent } from '@app/pages/access-control/access-card-management/local-photograph/local-photograph.component';
import { AlarmAllTempalteModalComponent } from '@app/pages/monitor/alarm-list/alarm-all-tempalte-modal.component';
import { GlobalResourceService } from './providers/global-resource.service';
import { CronTabModule } from './components/cron-tab/cron-tab.module';
import { CodeEditorModalComponent } from '@app/pages/chart-data-separate-config/code-editor-modal/code-editor-modal.component';
import { ParamsFilterModule } from '@app/pages/energy-management/energy-panorama/params-filter/params-filter.module';
import { SaveFilterTemplateComponent } from '@app/pages/energy-management/energy-panorama/params-filter/save-filter-template.component';
import { EnergyAllTempalteModalComponent } from '@app/pages/energy-management/energy-panorama/params-filter/energy-all-tempalte-modal.component';
import { EnergyPanoramaService } from '@app/pages/energy-management/energy-panorama/energy-panorama.service';
import { TreeAirSelectorComponent } from '@app/communal/components/tree-aircondition-selector/tree-aircondition-selector.component'
import { AlarmLockSpanShowComponent } from './components/alarm-lockspan-show/alarm-lockspan-show.component';
import { MenuSelectorComponent } from './components/menu-selector/menu-selector.component';
import { PhoenixPagerComponent } from './components/pager/pager.component';
import { AiHierarchySelection } from '@app/pages/ai-energysaving/modals/hierarchy-selection/hierarchy-selection.component';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { PrintModalComponent } from './components/print-modal/print-modal.component';
import { NgxQRCodeModule } from '@techiediaries/ngx-qrcode';
import { ColorPickerModule } from 'ngx-color-picker';
import { VideoModalComponent } from './components/video-modal/video-modal.component';
// import { PhoenixPagerModule } from './components/pager/pager.module';
// import { SceneStructureTreeModule } from '@app/pages/idc-manage/components/hierarchy/scene-structure-tree/scene-structure-tree.module';
import { PageScalDirective } from '@app/pages/ai-energysaving/2dview/ai-view-scale.directive';
import { shieldConfigModelComponent } from '@app/pages/monitor/shield-event/shieldconfig-modal/shieldconfig-modal.component';
import { ShieldEventService } from '@app/pages/monitor/shield-event/shield-event.service';
import { EquipmentCategorySelectorComponent } from './components/equipment-category-selector/equipment-category-selector.component';
import { PortFilterComponent } from '@app/@hmi/component/dev/settings/data/collector-state-val/port-filter/port-filter.component';
import { DataTableModule } from 'ng-devui';
import { RegisterUserComponent } from '@app/pages/admin/personal/register-user/register-user.component';
import { RegisterUserService } from '@app/pages/admin/personal/register-user/register-user.service';
import { ReviewModelComponent } from '@app/pages/monitor/components/review-modal/review-modal.component';
import { ReviewModelService } from '@app/pages/monitor/components/review-modal/review-modal.service';
import { DragDropModule } from '@angular/cdk/drag-drop';

import { HistoryCurveSignalIndicatorModalComponent } from '@app/@hmi/component/dev/comps/custom/history-curve-signal-indicator-modal/history-curve-signal-indicator-modal.component';

const COMP_DEVELOP = [
  CompListComponent,
  CompTemplateComponent,
  CompTemplateListComponent,
  DevelopmentPageComponent,
  ViewComponent,
  CompSettingComponent,
  CompPageDetailComponent,
  CompManagementComponent,
  PageCompListComponent,
];
const LY_COMPONENTS = [

  DefaultModal,
  DelModalComponent,
  BaseListViewComponent,
  TableBaseCellComponent,
  ImagePreviewComponent,
  CellDownloadViewComponent,
  FileUploadViewComponent,
  ImageUploaderComponent,
  TaskDeviceFormComponent,
  PhotographModalComponent,
  SelectPoritComponent,
  SelectIndicatorEnergyComponent,
  SelectCorePointComponent,
  SelectIndicatorComponent,
  CapacityCellSelectorComponent,
  HistoryCurveSignalIndicatorModalComponent,
  SelectSignalComponent,
  TimerSettingComponent,
  TagsComponent,
  TagAddComponent,
  TagEditComponent,
  TagColumnComponent,
  PersonComponent,
  BasicStatisticsComponent,
  AlarmSeverityImgRenderComponent,
  AlarmOperationItemComponent,
  PointStatusItemComponent,
  PointOperationItemComponent,
  LivePointValueSetComponent,
  AlarmPostionStatComponent,
  AlarmLevelStatComponent,
  DeviceStatusStatComponent,
  ReviewModelComponent,
  AlarmTrendChartComponent,
  AlarmRealListComponent,
  DateComponent,
  BasicWidgetComponent,
  PieChartComponent,
  PieChartOptionsComponent,
  CurveChartComponent,
  CurveChartOptionsComponent,
  WidgetPanelSettingComponent,
  WidgetTemplateComponent,
  LoadingComponent,
  IconSelectorComponent,
  RangeColorSelectorComponent,
  AlarmOverviewComponent,
  FormulaConfigComponent,
  CronSelectorComponent,
  ComplexIndexFunctionComponent,
  IndicatorUnitSelector,
  ViewBasicComponent,
  SignalEventExpressionComponent,
  SetMaskComponent,
  DeviceShieldConfigComponent,
  AlarmAllTempalteModalComponent,
  TopographyComponent,
  TreeSelectorComponent,
  GraphicPageModalComponent,
  TreeEventSelectorComponent,
  TreeStructureSelectorComponent,
  TreeEquipmentSelectorComponent,
  TreeAirSelectorComponent,
  SetStructureTreeComponent,
  IndicatorSelectorComponent,
  IndicatorHistoryChartSettingComponent,
  IndicatorTableValColumnComponent,
  IndicatorTableValRowComponent,
  IndicatorProgressChartSettingComponent,
  AnimationDisplaySettingComponent,
  IndicatorDoughnutChartSettingComponent,
  IndicatorDoubleProgressChartSettingComponent,
  IndexAllocationListComponent,
  IndicatorEditComponent,
  CustomSmartTableComponent,
  BasicSignalSelectorComponent,
  RackSelectorComponent,
  RackHistoryComponent,
  RackSearchComponent,
  RackModalComponent,
  ForewarmingListPanelComponent,
  SelectTheBuildingComponent,
  SelectTheFloorComponent,
  SelectTheRoomComponent,
  SelectTheServerityComponent,
  TriggerConditionsComponent,
  IDCRackWeightComponent,
  BaseTypeSelectorComponent,
  RackPointSelector,
  MachineCalendarPopupComponent,
  IndicatorTemplateSelectorComponent,
  ExpressSelectorComponent,
  ExpressSelectorComponentEnergy,
  DynamicCurveComponent,
  DynamicCurveModalComponent,
  SingleFileUploadComponent,
  CollectCommunicationSelectorComponent,
  PortFilterComponent,
  HistoryCurveComponent,
  HierarchySelectorComponent,
  ThreeDObjectSelectorComponent,
  EquipmentCategorySelectorComponent,
  NewRackSelectorComponent,
  DevicePointSelectorComponent,
  SelectObjectsComponent,
  ConfiguationBreadcrumbComponent,
  GraphicPageAddComponent,
  DeviceListComponent,
  DeviceListDialogComponent,
  BaseSignalConfigComponent,
  PreviewFilterParamComponent,
  CapacityAttributesComponent,
  GlobalResourceNodeSelectorComponent,
  SelectCapacityComponent,
  PreAlarmAllocationlistComponent,
  PreAlarmExpressSelectorComponent,
  CapacitySelectorComponent,
  UpdatePasswordComponent,
  ForgetPasswordComponent,
  RegisterUserComponent,
  CameraSelectorComponent,
  ControlSelectorComponent,
  ControlSetModalComponent,
  DiagramSelectorComponent,
  LocalPhotographComponent,
  LocalEntryComponent,
  CodeEditorModalComponent,
  SaveFilterTemplateComponent,
  EnergyAllTempalteModalComponent,
  AlarmLockSpanShowComponent,
  MenuSelectorComponent,
  PrintModalComponent,
  AiHierarchySelection,
  PhoenixPagerComponent,
  shieldConfigModelComponent
];

const LY_SERVICES = [
  EmailService,
  EacherTempService,
  ToastService,
  AlertService,
  FileService,
  TaskDeviceFromService,
  TimerSettingService,
  TagService,
  PersonsService,
  AlarmPostionStatService,
  AlarmLevelStatService,
  DeviceStatusStatService,
  ReviewModelService,
  AlarmTrendChartService,
  AlarmRealListService,
  D3Service,
  ThemeDashboardService,
  BaseCRUDService,
  ExcelService,
  ExportExcelFileService,
  CurveDataExportService,
  DynamicComponentServiceService,
  HcmService,
  UpdatePasswordService,
  RegisterUserService,
  ForgetPasswordService,
  DeviceShieldEventService,
  GlobalResourceService,
  ShieldEventService,
  {
    provide: 'piechartservice',
    useClass: PieChartService,
  },
  {
    provide: 'tasktrendservice',
    useClass: TaskTrendService,
  },
  EnergyPanoramaService
];

const LY_PIPES = [
  TimerSettingPipe,
  EnumToArrayPipe,
  ArrayListPipe,
  BooleanValueConvertPipe,
  EmptyValueConvertPipe,
  SliceMixinPipe,
  StrInterceptPipe,
  HtmlPipe,
];

export function loadEcharts() {
  return import('echarts');
}

@NgModule({
  declarations: [
    ...COMP_DEVELOP,
    ...LY_COMPONENTS,
    ...LY_PIPES,
    DebounceDirective,
    ViewContainRefHostDirective,
    ViewContainRefViewDirective,
    VideoModalComponent,
    PageScalDirective
  ],
  imports: [
    TreeModule,
    TM,
    RouterModule,
    NgxEchartsModule.forRoot({
      echarts: loadEcharts,
    }),
    FormsModule,
    CommonModule,
    CoreModule.forRoot(),
    ReactiveFormsModule,
    AppTranslationModule,
    FileUploadModule,
    SelectModule,
    NgbModule,
    CompDevModule,
    SweetAlert2Module.forRoot(),
    Ng2SmartTableModule,
    Daterangepicker,
    SceneStructureTreeModule,
    CameraTreeModule,
    CronTabModule,
    ParamsFilterModule,
    DataTableModule,
    NzTreeModule,
    NzButtonModule,
    NzSelectModule,
    NzCheckboxModule,
    NgxQRCodeModule,    
    DragDropModule, 
    ColorPickerModule
  ],
  // entryComponents: [...LY_COMPONENTS],
  exports: [
    ...COMP_DEVELOP,
    ...LY_COMPONENTS,
    ArrayListPipe,
    DebounceDirective,
    ViewContainRefHostDirective,
    ViewContainRefViewDirective,
    PageScalDirective
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
})
export class CommunalModule {
  static forRoot(): ModuleWithProviders<CommunalModule> {
    return {
      ngModule: CommunalModule,
      providers: [...LY_SERVICES],
    };
  }
}
