import { Component, OnInit, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastService } from '@communal/providers/toast.service';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { GlobalState } from '@app/global.state';
import { AlarmListService } from '@app/pages/monitor/alarm-list/alarm-list.service';
import { SessionService } from '@app/core/services/session.service';

@Component({
  selector: 'app-alarm-commoncomment',
  templateUrl: './alarm-commoncomment.component.html',
  styleUrls: ['./alarm-commoncomment.component.scss']
})
export class AlarmCommonCommentComponent implements OnInit {
  @Input() theTitle = '';
  @Input() opType = '';
  @Input() reasonList = [];
  @Input() isBatch = false;
  reason: string;
  reasonType: any;
  extTitle: any;
  private lanKes = ['general.alarm.comment', 'general.common.succeed', 'general.common.failed', 'general.alarm.batch', 'general.alarm.reason', 'general.alarm.desc', 'general.alarm.alarmCount'];
  private lanValues;

  constructor(private ngModal: NgbActiveModal, private service: AlarmListService, private globalState: GlobalState, private sessionServer: SessionService,
    private translateService: TranslateService, private toastService: ToastService) {
  }

  ngOnInit() {
    this.translateService.get(this.lanKes).subscribe(
      (values) => {
        this.lanValues = values;
        if (this.service.selectedItems.length > 1) {
          this.extTitle = this.lanValues['general.alarm.batch'] + this.theTitle + this.service.selectedItems.length + this.lanValues['general.alarm.alarmCount'];
        } else {
          this.extTitle = this.theTitle;
        }
        if (this.opType === 'comment') {
          this.theTitle = this.theTitle + " " + this.lanValues['general.alarm.desc'];
        } else {
          this.theTitle = this.theTitle + " " + this.lanValues['general.alarm.reason'];
        }
      }
    );
  }

  submit(): void {
    if (this.opType === 'end') {
      this.endPointAlarm();
    } else if (this.opType === 'confirm') {
      this.confirmPoint();
    } else if (this.opType === 'comment') {
      this.commentPointAlarm();
    }
  }

  endPointAlarm(): void {
    if (this.service.selectedItems.length === 0 || !this.reason || this.reason.trim() === '') {
      return;
    }
    try {
      const sids = this.service.selectedItems.map(e => { return e.sequenceId; });
      this.service.endCov({ 'sequenceIds': sids, 'note': this.reason }).subscribe(
        res => {
          this.toastService.showToast('success',
            this.extTitle + this.lanValues['general.common.succeed'], '');
          this.ngModal.close({ ok: true });
          this.globalState.notifyDataChanged('refreshAlarmOpLog');
          this.opAlarmLog(this.service.selectedItems, this.translateService.instant('general.pointsList.opForceEndTip'));
        },
        err => {
          let msg = '';
          if (err.error) {
            msg = err.error.err_msg
          }
          this.toastService.showToast('error', msg, this.lanValues['general.common.failed']);
        }
      );
    }
    catch {
      this.toastService.showToast('error',
        this.theTitle + this.lanValues['general.common.failed'], '');
    }
  }

  confirmPoint(): void {
    if (this.service.selectedItems.length <= 0 || !this.reason || this.reason.trim() === '' || !this.reasonType) {
      return;
    }
    try {
      const sids = this.service.selectedItems.map(e => { return e.sequenceId; });
      let defaultTimeOut = 30000;
      if (this.service.selectedItems.length > 1000)
        defaultTimeOut = 60000;
      else if (this.service.selectedItems.length > 10000)
        defaultTimeOut = 120000;
      this.service.confirmCovTimeOut({ 'sequenceIds': sids, 'note': this.reason, 'eventReasonType': this.reasonType }, defaultTimeOut).subscribe(
        res => {
          this.opAlarmLog(this.service.selectedItems, this.translateService.instant('general.pointsList.opConfirmTip'));
          this.toastService.showToast('success',
            this.extTitle + this.lanValues['general.common.succeed'], '');
          this.ngModal.close({ ok: true });
          this.globalState.notifyDataChanged('refreshAlarmOpLog');
        },
        err => {
          let msg = '';
          if (err.error) {
            msg = err.error.err_msg
          }
          this.toastService.showToast('error', msg, this.lanValues['general.common.failed']);
        }
      );
    }
    catch {
      this.toastService.showToast('error',
        this.theTitle + this.lanValues['general.common.failed'], '');
    }
  }

  opAlarmLog(items: any, opDesc: string): void {
    try {
      const obj = {
        'description': this.reason,
        'equipmentIds': items.map(e => { return e.equipmentId; }),
        'eventConditionIds': items.map(e => { return e.eventConditionId; }),
        'eventIds': items.map(e => { return e.eventId; }),
        'operation': opDesc,
        'sequenceIds': items.map(e => { return e.sequenceId; }),
        'startTimes': items.map(e => { return e.startTime; }),
        'stationIds': items.map(e => { return e.stationId; }),
      }
      let defaultTimeOut = 30000;
      if (this.service.selectedItems.length > 1000)
        defaultTimeOut = 60000;
      else if (this.service.selectedItems.length > 10000)
        defaultTimeOut = 120000;
      this.service.opeventLogTimeOut(obj, defaultTimeOut).subscribe(
        res => {
          //
        },
        err => {
          let msg = '';
          if (err.error) {
            msg = err.error.err_msg
          }
          this.toastService.showToast('error', msg, this.lanValues['general.common.failed']);
        }
      );
    }
    catch {
      this.toastService.showToast('error',
        this.theTitle + this.lanValues['general.common.failed'], '');
    }
  }

  commentPointAlarm(): void {
    if (this.service.selectedItems.length <= 0 || !this.reason || this.reason.trim() === '') {
      return;
    }
    try {
      const sids = this.service.selectedItems.map(e => { return e.sequenceId; });
      this.service.commentCov({ 'sequenceIds': sids, 'note': this.reason }).subscribe(
        res => {
          this.toastService.showToast('success',
            this.extTitle + this.lanValues['general.common.succeed'], '');
          this.opAlarmLog(this.service.selectedItems, this.translateService.instant('general.alarm.occureRemark'));
          this.ngModal.close({ ok: true });
          this.globalState.notifyDataChanged('refreshAlarmOpLog');
        },
        err => {
          let msg = '';
          if (err.error) {
            msg = err.error.err_msg
          }
          this.toastService.showToast('error', msg, this.lanValues['general.common.failed']);
        }
      );
    }
    catch {
      this.toastService.showToast('error',
        this.theTitle + this.lanValues['general.common.failed'], '');
    }
  }

  closeOrg() {
    this.ngModal.close();
  }
}
