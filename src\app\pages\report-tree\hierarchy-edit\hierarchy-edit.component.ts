import { Component, OnInit, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ReportTreeService } from '../report-tree.service';
import { ToastService } from '@app/communal/providers';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-hierarchy-edit',
  templateUrl: './hierarchy-edit.component.html',
  styleUrls: ['./hierarchy-edit.component.scss']
})
export class HierarchyEditComponent implements OnInit {
  @Input() currentNode: any;
  @Input() nodes: any[];

  parentGroups: any[];
  selectedParentId: string;
  allNodeKeys: string[] = [];

  constructor(
    private activeModal: NgbActiveModal,
  ) { }

  ngOnInit() {
    this.selectedParentId = this.currentNode?.parentId || 0;
    this.parentGroups = this.filterGroups(this.nodes, this.currentNode.id);
    this.allNodeKeys = this.getAllKeys(this.parentGroups);
    console.log(this.parentGroups);
  }

  filterGroups(nodes: any[], excludeId: string): any[] {
    return nodes
      .filter((node) => node.type === 'folder' && node.id !== excludeId)
      .map((node) => ({
        title: node.name,
        key: node.id,
        children: node.children ? this.filterGroups(node.children, excludeId) : [],
      }));
  }

  getAllKeys(nodes: any[]): string[] {
    let keys: string[] = [];
    nodes.forEach(node => {
      keys.push(node.key);
      if (node.children && node.children.length) {
        keys = keys.concat(this.getAllKeys(node.children));
      }
    });
    return keys;
  }

  onParentChange(value: string) {
    this.selectedParentId = value;
  }

  onSubmit() {
    this.activeModal.close(this.selectedParentId);
  }

  close() {
    this.activeModal.close();
  }
}