
import { fork<PERSON>oin } from 'rxjs';
import { Compo<PERSON>, On<PERSON><PERSON>roy, OnInit, ViewChild, ElementRef, Input, ChangeDetectorRef } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AlarmListService } from '@app/pages/monitor/alarm-list/alarm-list.service';
import * as _ from 'lodash';
import { ToastService } from '@communal/providers/toast.service';
import { DeviceEvent } from '../../models/DeviceEvent.model';
import { SessionService } from '@core/services/session.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import * as XLSX from 'xlsx';
import { SmartTableServerDataSource } from '@communal/smart-table-data-source/smart-table-server.data-source';
import { ParamsFilterComponent } from '@app/pages/site/components/params-filter/params-filter.component';
import { cloneDeep, mergeWith } from 'lodash';
import { SaveFilterTemplateComponent } from '@app/pages/site/components/params-filter/save-filter-template.component';
import { AlarmAllTempalteModalComponent } from '../alarm-all-tempalte-modal.component';
import { AlertService } from '@app/communal/providers';
import { AlarmCommonCommentComponent } from '@app/core/components/alarm-commoncomment';
import { AuthenticationService } from '@app/core/services/authentication.service';
import { AlarmListFilterComponent } from '../alarm-list-filter.component';
import { CustomColumnsComponent } from '@app/pages/site/components/custom-columns/custom-columns.component';
import { ThreedModalComponent } from '../threed-modal/threed-modal.component';
import { DeviceShieldConfigComponent } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.component';
import { AlarmLockSpanShowComponent } from '@app/communal/components/alarm-lockspan-show/alarm-lockspan-show.component';
import { RealtimeAlarmExpertAdviceModalComponent } from '../realtime-alarm-expert-advice-modal.component';
import { tableResizeFunc } from 'ng-devui';
import { SnapshotModalComponent } from '../snapshot-modal/snapshot-modal.component';
import { S } from '@angular/cdk/keycodes';

@Component({
	selector: 'app-alarm-list-devui',
	templateUrl: './alarm-list-devui.component.html',
	styleUrls: ['./alarm-list-devui.component.scss']
})
export class AlarmListDevUIComponent implements OnInit, OnDestroy {
	@Input() levelId: string;
	@ViewChild('lockAlarm') lockAlarm: ElementRef;
	freezedColumns = [{ width: '60px', lkey: 's2.devicemonitor.signalColumns.checkBox', canSort: false, field: 'checkBox', visible: true },
	]
	currentColumns = [];
	configbleColumns = [
		{ width: '125px', lkey: 's2.devicemonitor.signalColumns.operation', canSort: false, field: 'operation', visible: true },
		{ width: '125px', lkey: 'general.alarm.severityName', canSort: true, field: 'eventLevel', visible: true },
		{ width: '228px', lkey: 'general.alarm.deviceName', canSort: true, field: 'equipmentName', visible: true },
		{ width: '280px', lkey: 'general.alarm.devicePosition', canSort: false, field: 'equipmentPosition', visible: true },
		{ width: '138px', lkey: 's2.alarm.equipmentCategory', canSort: true, field: 'baseEquipmentName', visible: true },
		{ width: '186px', lkey: 'general.alarm.corePointName', canSort: true, field: 'eventName', visible: true },
		{ width: '106px', lkey: 'notifyManagement.notifyServer.meaning', canSort: true, field: 'meanings', visible: true },
		{ width: '111px', lkey: 'general.alarm.occurValue', canSort: true, field: 'eventValue', visible: true },
		{ width: '111px', lkey: 'general.alarm.endValue', canSort: false, field: 'endValue', visible: true },
		{ width: '111px', lkey: 'general.alarm.eventReasonTypeName', canSort: false, field: 'eventReasonTypeName', visible: true },
		{ width: '111px', lkey: 's2.alarm.overturn', canSort: true, field: 'reversalNum', visible: true },
		{ width: '178px', lkey: 'general.alarm.birthTime', canSort: true, field: 'startTime', visible: true },
		{ width: '100px', lkey: 'general.alarm.confirmTime', canSort: true, field: 'confirmTime', visible: true },
		{ width: '178px', lkey: 'general.alarm.confirmerName', canSort: true, field: 'confirmerName', visible: true },
		{ width: '178px', lkey: 'general.alarm.clearTime', canSort: true, field: 'endTime', visible: true },
		{ width: '111px', lkey: 'general.idcDeviceMonitor.threshold', canSort: false, field: 'startCompareValue', visible: true },
		{ width: '106px', lkey: 's2.stationState.projectState', canSort: true, field: 'maintainState', visible: true },
		{ width: '106px', lkey: 's2.button.remark', canSort: true, field: 'description', visible: false },
		{ width: '106px', lkey: 's2.alarm.standardTypeName', canSort: false, field: 'standardTypeName', visible: false },
		{ width: '106px', lkey: 's2.alarm.stdSignalDescription', canSort: false, field: 'stdSignalDescription', visible: false },
		{ width: '106px', lkey: 's2.alarm.stdSignalMeanings', canSort: false, field: 'stdSignalMeanings', visible: false },
		{ width: '106px', lkey: 's2.alarm.stdNote', canSort: false, field: 'stdNote', visible: false },
		{ width: '106px', lkey: 's2.alarm.equipmentLogicCategory', canSort: false, field: 'equipmentCategoryName', visible: false },
		{ width: '138px', lkey: 's2.alarm.alarmLogicCategory', canSort: false, field: 'alarmLogicCategory', visible: false }
	];
	theme: string;
	isBackColorAlarm = false;
	showDeviceCatagory = true;
	showlevels: string;
	lockName = this.translate.instant('general.alarm.lock');
	lockTimer: any;
	lockSpan = 30000;
	isPageLocked = false;
	adjStartTime: any;
	refreshSpan = 2000;
	refreshStandardSpan = 2000;
	allSelected = false;
	allSelectedItems = [];
	items: any = [];
	currentItems: any = [];
	serverPaging = {
		number: 0,
		size: 25,
		totalElements: 0
	};
	sortColumn = {
		name: 'startTime',
		direction: 'desc'
	};

	pageCounts = [25, 50, 100, 200];
	alive = true;
	columnHeaders: any;
	baseListTableOption1: any;
	langKeys = [];
	query = '';
	isFirst = true;
	subscribeOfAlarm: any;
	routeSub: any;
	settings: any = {
		mode: 'external',
		actions: false,
		selectMode: 'multi',
		pager: {
			display: true,
			perPage: 500,
		}
	};
	isReturn = false;
	showEndReason = false;
	showCommitReason = false;
	showConfirmReason = false;
	showBatchProcessing = false;
	NoEndCount = 0;
	NoConfirmCount = 0;
	canMaskCount = 0;
	fileName = '';
	intervalId: any;
	alarmSeverities;
	columnHeaderKeys: string[];
	source: SmartTableServerDataSource;
	searchText = '';
	currentFilterParms: any;
	FilterType = 'EventListFilter';
	ColumnCustomType = 'EventColumnCustomNew';
	activeTemplate: any;
	templateList = [];
	savedCustomColumnId: any;
	filterStat: any;
	showAlarmSeverities = [];
	has3DLicense = false;
	minWidth = 40;
	checkedAlarmItems = [];
	tableHeight = '640px';
	resizeObserver: ResizeObserver;
	showName = false;
	runAtUICore = false;
	enableAdvice = false;
	enableAlarmSnapshot = false;
	enableAlarmWorkOrder = false;
	enableAlarm3D = false;
	isNeedHistoryAlarm = false;
	isByteDance = false;
	alarmReason: any = [];

	constructor(
		protected service: AlarmListService,
		private alertService: AlertService,
		private authenticationService: AuthenticationService,
		private router: Router,
		private route: ActivatedRoute,
		private ngbModal: NgbModal,
		private ele: ElementRef,
		public translate: TranslateService,
		public sessionService: SessionService,
		private cdr: ChangeDetectorRef,
		private toastService: ToastService) {
	}
	ngOnInit() {
		this.runAtUICore = window.runAtWeboot();
		this.service.getAFilter('license').subscribe(res => {
			let licenses: any[] = res.licenseFeatures;
			if (licenses) {
				licenses = licenses.filter((s: any) => s.featureId == 2 && s.isActive == 1);
				if (licenses && licenses.length == 1)
					this.has3DLicense = true;
			}
		});
		const showNameSC = this.sessionService.getSysconfigByKey('alarmlist.static.showName');
		this.showName = showNameSC ? (showNameSC['systemConfigValue'] == 'true' ? true : false) : false;

		const setedSpan = this.sessionService.getSysconfigByKey('alarm.refresh.span');
		if (setedSpan && setedSpan['systemConfigValue']) {
			this.refreshSpan = parseInt(setedSpan['systemConfigValue']);
			this.refreshSpan = this.refreshSpan * 1000;
			this.refreshStandardSpan = this.refreshSpan;
		}

		let showHisTab = this.sessionService.getSysconfigByKey('alarm.history.tab.show');
		this.isNeedHistoryAlarm = showHisTab ? (showHisTab['systemConfigValue'] == 'true' ? true : false) : false;

		this.fetchCustomColumn();
		this.authenticationService.get('', 'coreeventseverities').subscribe(
			res => {
				res.forEach(s => {
					this.showAlarmSeverities[s.eventLevel] = s.displayColor;
				})
				this.alarmSeverities = res;
				this.alarmSeverities.forEach(s => {
					s.count = '0';
				});
			}
		)

		this.theme = localStorage.getItem('theme');
		this.langKeys = [
			'general.alarm.severityName',
			'general.alarm.deviceName',
			'general.alarm.devicePosition',
			'general.alarm.deviceCategory',
			'general.alarm.corePointName',
			'notifyManagement.notifyServer.meaning',
			'general.alarm.occurValue',
			'general.alarm.endValue',
			's2.alarm.overturn',
			'general.alarm.birthTime',
			'general.alarm.confirmTime',
			'general.alarm.confirmerName',
			'general.alarm.clearTime',
			'general.idcDeviceMonitor.threshold',
			's2.button.remark',
			's2.alarm.standardTypeName',
			's2.alarm.stdSignalDescription',
			's2.alarm.stdSignalMeanings',
			's2.alarm.stdNote',
			's2.alarm.equipmentLogicCategory',
			's2.alarm.equipmentCategory',
			's2.alarm.alarmLogicCategory',
			's2.stationState.projectState'
		];
		this.getEventTemplate();
		if (this.sessionService.getSysconfigByKey('alarmlist.backcolor.severityshow') &&
			this.sessionService.getSysconfigByKey('alarmlist.backcolor.severityshow').systemConfigValue === 'true') {
			this.isBackColorAlarm = true;
		}
		if (this.sessionService.getSysconfigByKey('alarmlist.devicecatagory.show') &&
			this.sessionService.getSysconfigByKey('alarmlist.devicecatagory.show').systemConfigValue === 'false') {
			this.showDeviceCatagory = false;
		} else {
			this.showDeviceCatagory = true;
		}
		if (!this.showDeviceCatagory) {
			this.langKeys.splice(3, 1);
		}
		if (this.routeSub)
			this.routeSub.unsubscribe();
		this.routeSub = this.route.queryParams.subscribe(params => {
			this.currentFilterParms = {};
			if (params && params['convergented']) {
				this.currentFilterParms.convergented = [params['convergented']];
				this.activeTemplate = undefined;
			}
			if (params && params['levelId']) {
				this.currentFilterParms.eventSeverityIds = [params['levelId']];
				this.activeTemplate = undefined;
			}
			if (params && params['deviceCategoryId']) {
				this.currentFilterParms.deviceCategoryIds = [params['deviceCategoryId']];
				this.activeTemplate = undefined;
			}
			if (params && params['resourceStructureId']) {
				this.currentFilterParms.resourceStructureIds = [params['resourceStructureId']];
				this.activeTemplate = undefined;
			}
			if (params && params['deviceId']) {
				this.currentFilterParms.equipmentIds = [params['deviceId']];
				this.activeTemplate = undefined;
			}
			if (params && params['baseTypeId']) {
				this.currentFilterParms.baseEquipmentIds = [params['baseTypeId']];
				this.activeTemplate = undefined;
			}
			if (params && params['eventConfirmed']) {
				this.currentFilterParms.eventConfirmed = [params['eventConfirmed']];
				this.activeTemplate = undefined;
			}
			if (params && params['isReturn']) {
				this.isReturn = true;
			} else {
				this.isReturn = false;
			}

		});

		this.setSetting();

		this.authenticationService.getAuthenticationJson().subscribe(resJson => {
			this.enableAdvice = resJson['alarm.op.enableAdvice']
			this.enableAlarmSnapshot = resJson['alarm.op.enableAlarmSnapshot'];
			this.enableAlarmWorkOrder = resJson['alarm.op.enableAlarmWorkOrder'];
			this.enableAlarm3D = 'alarm.op.enable3D' in resJson ? resJson['alarm.op.enable3D'] : true;
			this.isByteDance = resJson['bytedace.web.enable'];
		});
	}

	ngAfterViewInit() {
		this.resizeObserver = new ResizeObserver(entries => {
			for (const entry of entries) {
				if (this.runAtUICore)
					this.tableHeight = entry.contentRect.height - 66 + 'px';
				else
					this.tableHeight = entry.contentRect.height - 80 + 'px';
				this.cdr.detectChanges();
			}
		});
		this.resizeObserver.observe(document.querySelector('#tableFrame'));
	}

	search(): void {
		this.refreshData();
	}

	setSetting(): void {
		this.columnHeaderKeys = ['general.common.noData', ' general.alarm.coreEventName', 'general.alarm.severityName',
			'general.alarm.deviceName', 'general.alarm.corePointName', 'general.alarm.birthTime', 'general.alarm.clearTime',
			'general.alarm.confirmTime',
			'general.alarm.confirmerName',
			'general.alarm.occureRemark',
			'general.alarm.occurValue',
			'general.alarm.comment',
			'general.pointsList.opForceEndTip',
			'general.pointsList.opConfirmTip',
			'general.common.succeed',
			'general.common.failed',
			'general.alarm.devicePosition',
			'general.alarm.deviceCategory',
			'general.alarm.baseLogicCategoryName',
			'general.pointsList.pointOperation',
			'notifyManagement.notifyServer.meaning',
			'general.menu.activeAlarm',
			'general.alarm.execResult',
			'general.alarm.remarkCount',
			's2.devicemonitor.signalColumns.checkBox',
			's2.devicemonitor.signalColumns.operation',
			'general.idcDeviceMonitor.threshold',
			'general.common.confirm',
			'general.common.end',
			'idcManage.common.remark',
			'general.alarm.allConfirmed',
			'general.alarm.hasConfirmed',
			'general.alarm.hasEnded',
			's2.alarm.overturn',
			'general.alarm.endValue',
			's2.button.remark',
			's2.alarm.standardTypeName',
			's2.alarm.stdSignalDescription',
			's2.alarm.stdSignalMeanings',
			's2.alarm.stdNote',
			's2.alarm.equipmentLogicCategory',
			's2.alarm.equipmentCategory',
			's2.alarm.alarmLogicCategory',
			's2.stationState.projectState'
		];
		this.translate.get(this.columnHeaderKeys).subscribe((values) => {
			this.columnHeaders = values;
		});
		this.translate.get(this.langKeys).subscribe((values) => {
			this.baseListTableOption1 = values;
		});
		this.getAlarmReasonList();
	}

	getAlarmReasonList() {
		this.service.getAlarmReasonList().subscribe(res => {
			this.alarmReason = res;

			this.refreshData();
		})
	}

	onResize($event, field) {
		const func = tableResizeFunc(this.currentColumns, this.ele);
		if ($event.width < this.minWidth) {
			$event.width = this.minWidth;
		}
		func($event, field);

		this.saveCustomColumn();
	}

	getTableRowClass(rowItem) {
		if (rowItem.confirmTime) {
			if (localStorage.getItem('theme') !== 'classic') {
				return 'alarmconfirm2';
			} else {
				return 'alarmconfirm';
			}
		}
		if (rowItem.endTime) {
			if (localStorage.getItem('theme') !== 'classic') {
				return 'alarmend2';
			} else {
				return 'alarmend';
			}
		}
		return '';
	}

	openFilterModal() {
		const modal = this.ngbModal.open(ParamsFilterComponent, {
			container: 'app-alarm-list-devui', size: 'lg', backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy'
		});
		modal.componentInstance.title = 's2.alarm.queryParameterSelection';
		let parms = cloneDeep(AlarmListFilterComponent.FILTER_PARAMS);
		if (!this.isByteDance)
			parms.splice(2, 1)
		modal.componentInstance.params = AlarmListFilterComponent.createFilterParams(
			parms, this.service, this.sessionService
		);
		modal.componentInstance.stat =
			(this.filterStat && cloneDeep(this.filterStat)) || {};
		modal.componentInstance.footer = {
			left: [
				{
					label: 's2.button.saveAsTemplate',
					onClick: componentInstance => {
						if (componentInstance.stat) {
							if (componentInstance.stat.StartTimeFrom &&
								componentInstance.stat.StartTimeTo &&
								componentInstance.stat.StartTimeFrom[0] >= componentInstance.stat.StartTimeTo[0]
							) {
								this.alertService.error({
									title: this.translate.instant('s2.versionmanagement.server.searchStartTimeNotEnd'),
									timer: null,
									showConfirmButton: true,
									confirmButtonText: this.translate.instant('general.common.ok')
								});
								return;
							}
						}
						this.openSaveTemplateModal(
							componentInstance.params,
							componentInstance.stat
						);
					}
				},
				{
					label: 's2.button.loadTemplate',
					onClick: componentInstance =>
						this.openLoadTemplateModal(componentInstance)
				}
			],
			right: [
				{
					label: 'general.common.cancel',
					type: 'phoenix-form-default',
					onClick: componentInstance => componentInstance.close()
				},
				{
					label: 's2.button.clearFilter',
					type: 'phoenix-form-default',
					onClick: componentInstance => componentInstance.clearFilter()
				},
				// {
				//   label: 's2.button.previewParams',
				//   type: 'primary',
				//   onClick: componentInstance =>
				//     this.openPreviewParamsModal(componentInstance)
				// },
				{
					label: 'general.common.find',
					onClick: componentInstance => {
						if (componentInstance.stat) {
							if (componentInstance.stat.StartTimeFrom &&
								componentInstance.stat.StartTimeTo &&
								componentInstance.stat.StartTimeFrom[0] >= componentInstance.stat.StartTimeTo[0]
							) {
								this.alertService.error({
									title: this.translate.instant('s2.versionmanagement.server.searchStartTimeNotEnd'),
									timer: null,
									showConfirmButton: true,
									confirmButtonText: this.translate.instant('general.common.ok')
								});
								return;
							}
						}
						componentInstance.close({ stat: componentInstance.stat })
					}
				}
			]
		};
		modal.result.then(res => {
			if (res) {
				this.createQueryFilterParams(res.stat);
				this.refreshData();

				const keys = Array.from(
					new Set([
						...Object.keys(this.filterStat || {}),
						...Object.keys(res.stat)
					])
				);
				this.activeTemplate = keys.every(
					key =>
						((this.filterStat && this.filterStat[key]) || [])
							.sort()
							.join(',') === (res.stat[key] || []).sort().join(',')
				)
					? this.activeTemplate
					: null;
				this.filterStat = res.stat;
			}
		});
	}

	createQueryFilterParams(filters) {
		this.currentFilterParms = {};
		if (this.showlevels) {
			this.currentFilterParms.eventSeverityIds = this.showlevels.split(',');
		}
		AlarmListFilterComponent.FILTER_MAP.forEach((value, key) => {
			if (filters[key]) {
				this.currentFilterParms[value.propertyName] = filters[key];
			}
		});
		this.clearAllSelected();
		this.clearLockPage();
	}

	openSaveTemplateModal(params, stat) {
		const childModal = this.ngbModal.open(SaveFilterTemplateComponent, {
			container: 'app-params-filter',
			backdrop: 'static'
		});
		childModal.result.then(res => {
			if (res) {
				if (this.templateList.some(value => {
					return value.templateName.trim() === res.templateName.trim();
				})) {
					this.toastService.showToast('error', '存在相同的模板名称__' + res.templateName, '');
					return;
				}
				const para = {
					personId: parseInt(this.sessionService.getPersonId()),
					templateName: res.templateName,
					filterType: this.FilterType,
					content: JSON.stringify({
						ParameterValues: {
							ParameterValue: params
								.map(item => item.key)
								.map(key => ({
									Name: key,
									Value: (stat[key] || []).filter(val => val).join(',') || null
								}))
						}
					}),
					description: res.description,
				};
				this.service.saveTemplate(para).subscribe(ret => {
					this.toastService.showToast('success', this.translate.instant('s2.common.saveTemplateSuccess'), '');
					this.getEventTemplate();
				});
			}
		});
	}

	isJSONString(val) {
		if (typeof val === 'string') {
			try {
				const obj = JSON.parse(val);
				return obj && typeof obj === 'object';
				// tslint:disable-next-line:no-empty
			} catch {
				//
			}
		}
		return false;
	}

	getEventTemplate() {
		this.service
			.getAFilter('activeeventfiltertemplates?filterType=' + this.FilterType)
			.subscribe((res: any) => {
				const map = new Map();
				res.forEach(template => {
					if (!this.isJSONString(template.content)) {
						map.set(
							template.liveEventFilterTemplateId,
							template.content
								.split(':')
								.filter(id => id)
								.map(id => ~~id)
						);
					} else {
						template.stat = {};
						JSON.parse(
							template.content
						).ParameterValues.ParameterValue.forEach(para =>
							para.Value && para.Value !== 'null'
								? (template.stat[para.Name] = para.Value.split(','))
								: void 0
						);
					}
				});
				const reducer = (accmulator, currentValue) =>
					mergeWith(accmulator, currentValue, (objValue, srcValue) =>
						Array.from(new Set((objValue || []).concat(srcValue || [])))
					);
				map.forEach((value, key) => {
					res.find(template => template.liveEventFilterTemplateId === key).stat = value
						.map(id => res.find(item => item.liveEventFilterTemplateId === id).stat)
						.reduce(reducer, {});
				});

				this.templateList = res;
			}, error => {
				setTimeout(() => {
					this.getEventTemplate();
				}, 200);
			});
	}

	openLoadTemplateModal(parent) {
		const modal = this.ngbModal.open(AlarmAllTempalteModalComponent, {
			container: 'app-params-filter',
			size: 'sm',
			backdrop: 'static'
		});
		modal.componentInstance.templateList = this.templateList;
		const reducer = (accmulator, currentValue) =>
			mergeWith(accmulator, currentValue, (objValue, srcValue) =>
				Array.from(new Set((objValue || []).concat(srcValue || [])))
			);
		modal.result.then(res => {
			if (res) {
				const oldStat = parent.stat ? cloneDeep(parent.stat) : {};
				res
					.map(
						id => this.templateList.find(template => template.liveEventFilterTemplateId === id).stat
					)
					.reduce(reducer, parent.stat);

				if (Object.keys(parent.stat).some(
					key => parent.stat[key] && parent.stat[key].length > 10
				)) {
					parent.stat = oldStat;
					this.alertService.warning({
						title: this.translate.instant('s2.common.warning'),
						html: `<div>${this.translate.instant(
							's2.alarm.maxSelect'
						).replace('{}', 10)}</div><small class="form-text text-muted">${'参数合并失败！'}</div>`,
						timer: null,
						showConfirmButton: true,
						confirmButtonText: this.translate.instant('general.common.ok')
					});
				}

				if (parent.stat && parent.stat.EventConfirm && parent.stat.EventConfirm.length > 1) {
					parent.stat.EventConfirm.splice(0, 1);
				}
				if (parent.stat && parent.stat.EventEnd && parent.stat.EventEnd.length > 1) {
					parent.stat.EventEnd.splice(0, 1);
				}

				parent.refresh();
			}
		});
	}

	openPreviewParamsModal(parent, editTemplate?) {
		//
	}

	hasFilter() {
		return (
			this.filterStat &&
			Object.keys(this.filterStat).some(
				key => this.filterStat[key] && this.filterStat[key].length
			)
		);
	}

	queryEventTemplate(template?) {
		this.filterStat = template ? cloneDeep(template.stat) : {};
		this.createQueryFilterParams(this.filterStat);
		this.activeTemplate = template;
		this.refreshData();
	}

	editEventTemplate(template) {
		const modal = this.ngbModal.open(ParamsFilterComponent, {
			size: 'lg',
			backdrop: 'static'
		});
		modal.componentInstance.title = template.templateName;
		let parms = AlarmListFilterComponent.FILTER_PARAMS;
		if (!this.isByteDance)
			parms.splice(2, 1)
		modal.componentInstance.params = AlarmListFilterComponent.createFilterParams(
			parms, this.service, this.sessionService
		);
		modal.componentInstance.stat = cloneDeep(template.stat);
		modal.componentInstance.footer = {
			left: [
				{
					label: 's2.button.saveAs',
					onClick: componentInstance => {
						this.openSaveTemplateModal(
							componentInstance.params,
							componentInstance.stat
						);
					}
				},
				{
					label: 's2.button.loadTemplate',
					onClick: componentInstance =>
						this.openLoadTemplateModal(componentInstance)
				}
			],
			right: [
				{
					label: 'general.common.cancel',
					type: 'phoenix-form-default',
					onClick: componentInstance => componentInstance.close()
				},
				{
					label: 's2.button.clearFilter',
					type: 'phoenix-form-default',
					onClick: componentInstance => componentInstance.clearFilter()
				},
				// {
				//   label: 's2.button.previewParams',
				//   type: 'primary',
				//   onClick: componentInstance =>
				//     this.openPreviewParamsModal(componentInstance, template)
				// },
				{
					label: 's2.button.saveAndQuery',
					onClick: componentInstance => {
						const para = {
							activeEventFilterTemplateId: template.activeEventFilterTemplateId,
							personId: template.personId,
							templateName: template.templateName,
							filterType: template.filterType,
							content: JSON.stringify({
								ParameterValues: {
									ParameterValue: componentInstance.params
										.map(item => item.key)
										.map(key => ({
											Name: key,
											Value:
												(componentInstance.stat[key] || [])
													.filter(val => val)
													.join(',') || null
										}))
								}
							}),
							description: template.description,
						};
						this.service.updateTemplate(para).subscribe(ret => {
							this.toastService.showToast('success', this.translate.instant('s2.common.saveTemplateSuccess'), '');
							this.getEventTemplate();
							componentInstance.close({ stat: componentInstance.stat });
						});
						this.activeTemplate = template;
					}
				},
				{
					label: 'general.common.find',
					onClick: componentInstance => {
						const keys = Array.from(
							new Set([
								...Object.keys(componentInstance.stat),
								...Object.keys(template.stat)
							])
						);
						this.activeTemplate = keys.every(
							key =>
								(componentInstance.stat[key] || []).sort().join(',') ===
								(template.stat[key] || []).sort().join(',')
						)
							? template
							: null;

						componentInstance.close({ stat: componentInstance.stat });
					}
				}
			]
		};
		modal.result.then(res => {
			if (res) {
				this.createQueryFilterParams(res.stat);
				this.refreshData();

				const keys = Array.from(
					new Set([
						...Object.keys(this.filterStat || {}),
						...Object.keys(res.stat)
					])
				);
				this.activeTemplate = keys.every(
					key =>
						((this.filterStat && this.filterStat[key]) || [])
							.sort()
							.join(',') === (res.stat[key] || []).sort().join(',')
				)
					? this.activeTemplate
					: null;
				this.filterStat = res.stat;
			}
		});
	}

	deleteEventTemplate(template) {
		const content =
			this.translate.instant('general.common.confirmDelete') + template.templateName;
		this.alertService.deleteConfirm(content).then(res => {
			if (res.value) {
				this.service.delTemplate(template.activeEventFilterTemplateId).subscribe(
					res => {
						this.toastService.showToast('success', this.translate.instant('s2.common.deleteTemplateSuccess'), '');
						this.getEventTemplate();
					},
					err => this.toastService.showToast('error', this.translate.instant('s2.common.deleteTemplateFailed'), '')
				);
			}
		});
	}

	showMask(): void {
		if (this.allSelectedItems.length === 0) {
			return;
		}
		if (this.canMaskCount != this.allSelectedItems.length) {
			this.toastService.showToast('warning', this.translate.instant('general.alarm.hasNoPermissionMask'), '', { timeOut: 3000 });
		}

		const modal3 = this.ngbModal.open(DeviceShieldConfigComponent, {
			backdrop: 'static',
			backdropClass: 'bounced-hierarchy',
			windowClass: 'window-hierarchy',
		});
		modal3.result.then(res => {
			if (res) {
				const maskParam: any = {};
				maskParam.startTime = res.startTime;
				maskParam.endTime = res.endTime;
				maskParam.comment = res.reason;
				maskParam.timeSpanStr = this.translate.instant('general.mask.ismasked');
				maskParam.timeGroupCategory = res.timeGroupCategory;
				maskParam.timeGroupSpans = res.timeGroupSpans;

				this.bulkBlock(maskParam);
			}
		})
	}

	bulkBlock(maskParam): void {
		if (maskParam.startTime && maskParam.endTime) {
			const content = '';

			if (maskParam.startTime >= maskParam.endTime && maskParam.timeGroupCategory == 1) {
				const timeJudge = this.translate.instant('general.mask.timeJudge');
				this.toastService.showToast('error', timeJudge, content);
				return;
			} else {
				const tit = this.translate.instant('general.alarm.mask');
				const success = this.translate.instant('general.common.succeed');
				const batchObj: any = {
					reason: maskParam.comment,
					endTime: maskParam.endTime,
					startTime: maskParam.startTime,
					timeGroupCategory: maskParam.timeGroupCategory,
					timeGroupSpans: maskParam.timeGroupSpans,
				};
				// 勾选告警屏蔽
				if (Array.isArray(this.allSelectedItems) && this.allSelectedItems.length > 0) {
					let canMaskItems = this.allSelectedItems;
					if (this.isByteDance) {
						canMaskItems = canMaskItems.filter(s => this.sessionService.hasPermission(120 + s.eventLevel));
					}
					batchObj.equipmentIds = canMaskItems.map(e => { return e.equipmentId; });
					batchObj.stationIds = canMaskItems.map(e => { return e.stationId; });
					batchObj.eventIds = canMaskItems.map(e => { return e.eventId; });
					this.authenticationService.create(batchObj, 'eventmasks/batchcreate')
						.subscribe(() => {
							this.toastService.showToast('success', tit + success, '');
							this.clearAllSelected();
							this.clearLockPage();
						});
				}

			}
		} else {
			const timeNotEmpty = this.translate.instant('general.mask.timeNotEmpty');
			const content = this.translate.instant('general.mask.confirmBulkblock');
			this.toastService.showToast('warning', timeNotEmpty, content);
			return;
		}
	}

	clearAllSelected(): void {
		this.allSelected = false;
		this.allSelectedItems = [];
		this.items.forEach(element => {
			element.selected = false;
		});
	}

	opEvent(opType: string): void {
		if (this.allSelectedItems.length === 0) {
			return;
		}

		this.service.selectedItems = this.allSelectedItems;
		const modal = this.ngbModal.open(AlarmCommonCommentComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
		modal.componentInstance.opType = opType;
		modal.componentInstance.reasonList = this.alarmReason;
		if (opType === 'comment') {
			modal.componentInstance.theTitle = this.translate.instant('general.alarm.remark');
			modal.componentInstance.isBatch = true;
		} else if (opType === 'confirm') {
			modal.componentInstance.theTitle = this.columnHeaders['general.common.confirm'];
			modal.componentInstance.isBatch = true;
			let confirmItems = this.allSelectedItems.filter(s => !s.confirmTime || s.confirmTime === '');
			if (this.isByteDance) {
				confirmItems = this.allSelectedItems.filter(s => (s.confirmTime === null || s.confirmTime === '') && this.sessionService.hasPermission(130 + s.eventLevel));
			}
			this.service.selectedItems = confirmItems;
			if (this.NoConfirmCount != this.allSelectedItems.length) {
				this.toastService.showToast('warning', this.columnHeaders['general.alarm.hasConfirmed'], '', { timeOut: 3000 });
			}
		} else if (opType === 'end') {
			if (this.NoEndCount != this.allSelectedItems.length) {
				this.toastService.showToast('warning', this.columnHeaders['general.alarm.hasEnded'], '');
			}
			modal.componentInstance.isBatch = true;
			modal.componentInstance.theTitle = this.columnHeaders['general.common.end'];
		}
		modal.result.then(res => {
			if (res && res.ok) {
				if (opType === 'confirm' || opType === 'end') {
					this.clearAllSelected();
				}
				this.clearLockPage();
				this.refreshData();
			}
		});
	}

	ngOnDestroy(): void {
		this.clearTimer();
		if (this.subscribeOfAlarm) {
			this.subscribeOfAlarm.unsubscribe();
		}
		clearTimeout(this.lockTimer);
		if (this.routeSub)
			this.routeSub.unsubscribe();
		this.alive = false;
	}

	sortForseverityId(x, y) {
		if (x.eventLevel !== y.eventLevel) {
			return x.eventLevel - y.eventLevel;
		} else {
			return new Date(y.startTime).getTime() - new Date(x.startTime).getTime();
		}
	}

	refreshData() {
		if (this.alive) {
			this.clearTimer();
			this.GetData();
		}
	}

	GetData() {
		const personId = this.sessionService.getPersonId();
		let param = '';
		if (this.isReturn && this.service.lastPageStatus) {
			this.currentFilterParms = this.service.lastPageStatus.currentFilterParms;
			this.serverPaging = this.service.lastPageStatus.serverPaging;
			this.sortColumn = this.service.lastPageStatus.sortColumn;
			this.searchText = this.service.lastPageStatus.searchText;
			this.activeTemplate = this.service.lastPageStatus.activeTemplate;
			if (this.activeTemplate && this.activeTemplate.stat) {
				this.filterStat = cloneDeep(this.activeTemplate.stat);
			} else {
				this.filterStat = this.service.lastPageStatus.filterStat;
			}
			if (this.service.lastPageStatus.levelId) {
				this.currentFilterParms.eventSeverityIds = this.service.lastPageStatus.levelId;
			}
			this.isReturn = false;
		}
		if (this.currentFilterParms) {
			Object.keys(this.currentFilterParms).forEach(key => {
				param += ('&' + key + '=' + this.currentFilterParms[key].join(','));
			});
		}

		const pageParms = {};
		Object.assign(pageParms, this.currentFilterParms);
		pageParms['page'] = [this.serverPaging.number];
		pageParms['size'] = [this.serverPaging.size];
		pageParms['sort'] = [this.sortColumn.name + ',' + this.sortColumn.direction];

		if (this.searchText !== '') {
			param += ('&keywords=' + this.searchText);
			pageParms['keywords'] = [this.searchText];
		}

		forkJoin([this.service.getEventByTemplate(pageParms),
		this.service.getFilterStaticsEvent(param)]).subscribe((res: any) => {
			if (res[0]) {
				if (!this.isPageLocked) {
					res[0].content.forEach(c => {
						c.maintainState = c.maintainState == 1 ? this.translate.instant('general.common.no') : this.translate.instant('general.common.yes');
					});
					this.items = res[0].content;
					this.setSelected();
					const newPaging = {
						number: res[0].number,
						size: res[0].size,
						totalElements: res[0].totalElements
					};
					this.serverPaging = newPaging;
					this.items.forEach(obj => {
						let ind = this.alarmReason.findIndex(thg => {
							return thg.id === obj.eventReasonType
						})
						if(ind >= 0) {
							obj.eventReasonTypeName = this.alarmReason[ind]['name'];
						} else {
							obj.eventReasonTypeName = '';
						}
					})
					this.currentItems = this.items;
				}
			}
			if (res[1] && this.alarmSeverities) {
				this.alarmSeverities.forEach(s => {
					s.count = '0';
				});
				this.alarmSeverities.forEach(s => {
					if (res[1][s.eventLevel.toString()]) {
						s.count = res[1][s.eventLevel.toString()].toString();
					}
				});
			}
			this.adjStartTime = new Date().getTime();
			this.intervalId = setTimeout(() => {
				const offset = new Date().getTime() - (this.adjStartTime + this.refreshStandardSpan);
				if (offset > 0 && offset > 1000) {
					this.refreshSpan = 1000; // 如果超过刷新间隔，应该立刻取，但为了服务器压力，还是设置为1秒再取
				} else {
					this.refreshSpan = this.refreshStandardSpan - offset;
				}
				// console.log("定时到" + DateUtil.dataToTime(new Date()));
				this.refreshData();
			}, this.refreshSpan);
		}, error => {
			setTimeout(() => {
				this.refreshData();
			}, 200);//容错，0.2秒继续请求
		});
	}

	setSelected() {
		_.map(this.items, i => {
			i.selected = this.allSelectedItems.findIndex(s => s.sequenceId === i.sequenceId) !== -1;
		});
	}

	excelExport() {
		const tableOption = {};
		const exportColumns = this.currentColumns.filter(i => i.field != 'checkBox' && i.field != 'operation')
		for (let i = 0; i < exportColumns.length; i++) {
			if (exportColumns[i]['visible'])
				tableOption[exportColumns[i]['lkey']] = this.baseListTableOption1[exportColumns[i]['lkey']];
		}
		this.fileName = this.columnHeaders['general.menu.activeAlarm'] + '.xlsx';
		const xlsTemplateJson = [['', '', '', '', '', this.columnHeaders['general.menu.activeAlarm']], []];
		xlsTemplateJson.push(_.map(tableOption));
		// console.log(tableOption)
		const aPageParms = {};
		Object.assign(aPageParms, this.currentFilterParms);
		aPageParms['page'] = [0];
		aPageParms['size'] = [300000];
		aPageParms['sort'] = [this.sortColumn.name + ',' + this.sortColumn.direction];

		this.service.getEventByTemplate(aPageParms).subscribe((result) => {
			result['content'].forEach(element => {
				const ev = new DeviceEvent();
				for (let i = 0; i < exportColumns.length; i++) {
					if (!exportColumns[i]['visible'])
						continue;
					switch (exportColumns[i]['field']) {
						case "eventLevel": {
							ev.severityName = element.eventSeverity;
							break;
						}
						case "equipmentName": {
							ev.coreSourceName = element.equipmentName;
							break;
						}
						case "equipmentPosition": {
							ev.devicePosition = element.equipmentPosition;
							break;
						}
						case "baseEquipmentName": {
							if (this.showDeviceCatagory) {
								ev.deviceCategoryName = element.baseEquipmentName;
							}
							break;
						}
						case "eventName": {
							ev.corePointName = element.eventName;
							break;
						}
						case "meanings": {
							ev.occurRemark = element.meanings;
							break;
						}
						case "eventValue": {
							ev.occurValue = element.eventValue;
							break;
						}
						case "endValue": {
							ev.remarkCount = element.endValue;
							break;
						}
						case "reversalNum": {
							ev.reversalNum = element.reversalNum;
							break;
						}
						case "startTime": {
							ev.birthTime = element.startTime;
							break;
						}
						case "confirmTime": {
							ev.confirmTime = element.confirmTime;
							break;
						}
						case "confirmerName": {
							ev.confirmerName = element.confirmerName;
							break;
						}
						case "endTime": {
							ev.clearTime = element.endTime;
							break;
						}
						case "startCompareValue": {
							ev.startCompareValue = element.startCompareValue;
							break;
						}
						case "description": {
							ev.description = element.description;
							break;
						}
						case "standardTypeName": {
							ev.standardTypeName = element.standardTypeName;
							break;
						}
						case "stdSignalDescription": {
							ev.stdSignalDescription = element.stdSignalDescription;
							break;
						}
						case "stdSignalMeanings": {
							ev.stdSignalMeanings = element.stdSignalMeanings;
							break;
						}
						case "stdNote": {
							ev.stdNote = element.stdNote;
							break;
						}
						case "equipmentLogicCategory": {
							ev.equipmentLogicCategory = element.equipmentLogicCategory;
							break;
						}
						case "alarmLogicCategory": {
							ev.alarmLogicCategory = element.alarmLogicCategory;
							break;
						}
						case "maintainState": {
							ev.maintainState = element.maintainState == 1 ? this.translate.instant('general.common.no') : this.translate.instant('general.common.yes');
							break;
						}
					}
				}
				xlsTemplateJson.push(_.values(ev));
			});
			/* generate worksheet */
			const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet(xlsTemplateJson);
			/* generate workbook and add the worksheet */
			const wb: XLSX.WorkBook = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
			/* save to file */
			XLSX.writeFile(wb, this.fileName);
		});
	}

	clearTimer() {
		if (this.intervalId) {
			clearTimeout(this.intervalId);
		}
	}

	setPageble(from, to) {
		to.last = from.last;
		to.number = from.number;
		// to.size = from.size;
	}

	changeAllSelected(e) {
		this.allSelected = e.currentTarget.checked;
		this.items.forEach(element => {
			element.selected = this.allSelected;
		});
		if (this.allSelected) {
			this.allSelectedItems = this.allSelectedItems.concat(this.items.filter(s => {
				return !this.allSelectedItems.find(o => o.sequenceId === s.sequenceId);
			}));
		} else {
			_.map(this.items, i => {
				const theIndex = this.allSelectedItems.findIndex(s => s.sequenceId === i.sequenceId);
				if (theIndex !== -1) {
					this.allSelectedItems.splice(theIndex, 1);
				}
			});
		}
		this.NoEndCount = this.allSelectedItems.filter(s => s.endTime === null || s.endTime === '').length;
		this.NoConfirmCount = this.allSelectedItems.filter(s => s.confirmTime === null || s.confirmTime === '').length;
		if (this.isByteDance) {
			this.NoConfirmCount = this.allSelectedItems.filter(s => (s.confirmTime === null || s.confirmTime === '') && this.sessionService.hasPermission(130 + s.eventLevel)).length;
		}
		this.canMaskCount = this.allSelectedItems.length;
		if (this.isByteDance) {
			this.canMaskCount = this.allSelectedItems.filter(s => this.sessionService.hasPermission(120 + s.eventLevel)).length;
		}
	}

	changeSelected(e, item) {
		if (e.currentTarget.checked) {
			if (!this.allSelectedItems.find(o => o.sequenceId === item.sequenceId)) {
				this.allSelectedItems.push(item);
			}
		} else {
			const theIndex = this.allSelectedItems.findIndex(s => s.sequenceId === item.sequenceId);
			if (theIndex !== -1) {
				this.allSelectedItems.splice(theIndex, 1);
			}
		}
		this.NoEndCount = this.allSelectedItems.filter(s => s.endTime === null || s.endTime === '').length;
		this.NoConfirmCount = this.allSelectedItems.filter(s => s.confirmTime === null || s.confirmTime === '').length;
		if (this.isByteDance) {
			this.NoConfirmCount = this.allSelectedItems.filter(s => (s.confirmTime === null || s.confirmTime === '') && this.sessionService.hasPermission(130 + s.eventLevel)).length;
		}
		this.canMaskCount = this.allSelectedItems.length;
		if (this.isByteDance) {
			this.canMaskCount = this.allSelectedItems.filter(s => this.sessionService.hasPermission(120 + s.eventLevel)).length;
		}
	}

	onPageChange(no) {
		if (this.serverPaging.number !== no) {
			this.allSelected = false;
		}
		this.clearLockPage();
		this.serverPaging.number = no;
		this.refreshData();
	}

	lockPage() {
		if (this.isPageLocked === false) {
			const spanModal = this.ngbModal.open(AlarmLockSpanShowComponent, {
				container: 'app-alarm-list-devui',
				backdrop: 'static',
			});
			spanModal.result.then((config) => {
				if (config) {
					this.lockSpan = config.lockSpan * 1000;
					this.lockAlarm.nativeElement.classList.remove('icon-suoding');
					this.lockAlarm.nativeElement.classList.add('icon-jiesuo');
					this.isPageLocked = true;
					this.lockName = this.translate.instant('general.alarm.unlock');
					this.lockTimer = setTimeout(() => {
						this.clearLockPage();
					}, this.lockSpan);
				}
			});
		} else {
			this.clearLockPage();
		}
	}

	clearLockPage() {
		this.isPageLocked = false;
		this.lockAlarm.nativeElement.classList.remove('icon-jiesuo');
		this.lockAlarm.nativeElement.classList.add('icon-suoding');
		this.lockName = this.translate.instant('general.alarm.lock');;
		clearTimeout(this.lockTimer);
	}

	gotoDetail(item) {
		if (!item) {
			return;
		}
		this.service.lastPageStatus = {
			sortColumn: this.sortColumn,
			serverPaging: this.serverPaging,
			currentFilterParms: this.currentFilterParms,
			searchText: this.searchText,
			activeTemplate: this.activeTemplate,
			filterStat: this.filterStat
		};
		this.router.navigate(['/entry/alarms/detail/'], {
			queryParams: {
				returnUrl: '/entry/alarms?isReturn=true',
				resourceStructureId: item.resourceStructureId,
				sequenceId: item.sequenceId,
				stationId: item.stationId,
				deviceId: item.equipmentId,
				navFromAlarmList: true,
			}
		});
	}
	// 专家建议
	gotoAdvice(item) {
		if (item.baseTypeId) {
			const modal = this.ngbModal.open(RealtimeAlarmExpertAdviceModalComponent, {
				container: 'app-alarm-list-devui',
				size: 'lg',
				backdrop: 'static',
			});
			modal.componentInstance.event = item;
		} else {
			this.toastService.showToast('warning', this.translate.instant('s2.alarm.noBaseTypeId'), '');
		}
	}
	// 告警设备3D定位弹窗
	gotoThreed(item) {
		if (!item) {
			return;
		}
		this.service.getSceneId(item.equipmentId).subscribe((res: any) => {
			const sceneId = res && res[0] && res[0].dimensionDesignerId;
			const eqId = item.equipmentId;
			const modal = this.ngbModal.open(ThreedModalComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy', size: 'lg' });
			modal.componentInstance.title = this.translate.instant('s2.alarm.location');//告警定位
			modal.componentInstance.sceneId = sceneId;
			modal.componentInstance.eqId = eqId;
		})
	}

	// 告警开始结束抓图弹窗
	gotoSnapshot(item) {
		if (!item) {
			return;
		}
		const snapShotList = [];
		forkJoin([this.service.getSnapshotIp(),
		this.service.getSnapshotUrl(item['sequenceId'])]).subscribe(res => {
			if (res[0] && res[0]['data'] && res[0]['data']['minioPort'] && res[0]['data']['minioIp']
				&& res[1] && res[1]['length']) {
				const list = res[1] as Array<any>;
				list.map(item => {
					const index = item['snapshotUrl'].lastIndexOf("/");
					const count = Number(item['snapshotUrl'].slice(index + 1));
					for (let i = 0; i < count; i++)
						snapShotList.push({
							'snapshotUrl': `http://${res[0]['data']['minioIp']}:${res[0]['data']['minioPort']}/${item['snapshotUrl']}/${i + 1}.jpg`,
							'operationType': item['operationType'] == '1' ? '告警开始' : '告警结束',
							'cameraName': (item.cameraName || item.cameraId)
						})
				})
				if (snapShotList.length > 0) {
					const modal = this.ngbModal.open(SnapshotModalComponent, {
						container: 'app-alarm-list-devui',
						backdrop: 'static',
						backdropClass: 'bounced-hierarchy',
						windowClass: 'window-hierarchy',
						size: 'lg'
					});
					modal.componentInstance.title = this.translate.instant('s2.alarm.snapshot');//告警抓图
					modal.componentInstance.snapShotList = snapShotList;
				}
			}
		});

	}

	createAlarmWorkOrder(item) {
		if (!item || !item.sequenceId) {
			return;
		}
		this.service.createAlarmWorkOrder(String(item.sequenceId)).subscribe((res: any) => {
			this.toastService.showToast(
				'success',
				this.translate.instant('s2.alarm.createWorkOrderSuccess'),
				null
			);
			setTimeout(() => {
				this.refreshData();
			}, 1000);
		}, error => {
			this.toastService.showToast(
				'error',
				this.translate.instant('s2.alarm.createWorkOrderError'),
				null
			);
		})
	}

	customColumns() {
		const modal = this.ngbModal.open(CustomColumnsComponent, {
			container: 'app-alarm-list-devui',
			backdrop: 'static',
		});
		if (this.savedCustomColumnId) {
			this.configbleColumns.forEach((c: any) => {
				if (!this.currentColumns.find(s => s.field == c.field)) {
					c.visible = false;
					c.headerText = this.translate.instant(c.lkey);
					this.currentColumns.push(c);
				}
			})
		}
		modal.componentInstance.columns = cloneDeep(this.savedCustomColumnId ? this.currentColumns.slice(1) : this.configbleColumns);
		// modal.componentInstance.showWidth = true;
		modal.result.then((config) => {
			if (config) {
				this.currentColumns = this.freezedColumns.concat(config);
				this.saveCustomColumn();
			}
		});
	};

	fetchCustomColumn() {
		this.service
			.getAFilter('activeeventfiltertemplates?filterType=' + this.ColumnCustomType)
			.subscribe((res: any) => {
				if (res && res.length > 0) {
					this.currentColumns = this.freezedColumns.concat(JSON.parse(res[0].content));
					this.savedCustomColumnId = res[0].activeEventFilterTemplateId;
				} else {
					this.currentColumns = this.freezedColumns.concat(this.configbleColumns);
				}
				this.currentColumns.forEach(col => {
					col['headerText'] = this.translate.instant(col.lkey)
				})
			}, error => {
				setTimeout(() => {
					this.fetchCustomColumn();
				}, 200);
			});
	}

	saveCustomColumn() {
		const para = {
			personId: parseInt(this.sessionService.getPersonId()),
			templateName: this.ColumnCustomType,
			filterType: this.ColumnCustomType,
			content: JSON.stringify(this.currentColumns.slice(1)),
			description: 'Event Page Column Custom',
		};
		if (this.savedCustomColumnId) {
			para['activeEventFilterTemplateId'] = this.savedCustomColumnId;
			this.service.updateTemplate(para).subscribe(ret => {
				// this.toastService.showToast('success', this.translate.instant('general.common.saveSuccess'), '');
				this.fetchCustomColumn();
			});
		} else {
			this.service.saveTemplate(para).subscribe(ret => {
				// this.toastService.showToast('success', this.translate.instant('general.common.saveSuccess'), '');
				this.fetchCustomColumn();
			});
		}
	}

	onSortChange(event, field) {
		this.sortColumn.name = field;
		this.sortColumn.direction = event.direction.toString();

		this.clearLockPage();
		this.refreshData();
	}
}