import { E } from '@angular/cdk/keycodes';
import { Component, OnInit } from '@angular/core';
import { ToastService } from '@app/communal/providers';
import { CoreeventseverityService } from '@app/communal/providers/coreeventseverity.service';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';
import { fromEvent } from 'rxjs';

@Component({
  selector: 'app-event-severity-config',
  templateUrl: './event-severity-config.component.html',
  styleUrls: ['./event-severity-config.component.scss']
})
export class EventSeverityConfigComponent implements OnInit {

  eventSeverityItems: Array<any> = [];
  languageKeys = [
    'general.common.cancel',
    'general.common.ok',
    'general.alarm.enableSuccess',
    'general.alarm.disabledSuccess',
    'general.common.saveSuccess',
    'general.common.saveFailed'];
  languageValues: any = [];
  colorShow = false;
  selectIndex: any;
  presetColors = [
    '#FF2626',
    '#F39924',
    '#EDD951',
    '#92C3E8'
  ]

  constructor(
    private toastService: ToastService,
    private coreeventseverityService: CoreeventseverityService,
    private translateService: TranslateService) { }

  ngOnInit() {
    fromEvent(document, 'click').subscribe((event) => {
      this.selectIndex = null;
    });
    this.translateService.get(this.languageKeys).subscribe(res => {
      this.languageValues = res;
    });
    this.coreeventseverityService.get('','coreeventseverities/all').subscribe(result => {
      result.map(item=>{item.displayColor =item.displayColor.toUpperCase()});
      this.eventSeverityItems = result;
    });
  }

  eventSeverityEnabled(item: any) {
    item.enabled = !item.enabled;
  }

  saveEventSeverity() {
    let j = 0;
    for (let i = 0; i < this.eventSeverityItems.length; i++) {
      this.coreeventseverityService.updateCoreEventSeverity(this.eventSeverityItems[i]).subscribe(res => {
        j++;
        if (j == this.eventSeverityItems.length) {
          this.toastService.showToast('success', this.languageValues['general.common.saveSuccess'], '');
        }
      });
    }
  }

  enableAlarmLevel(item) {
    const params = {
      eventLevel: item.eventLevel,
      eventSeverity: item.eventSeverity,
      enable: item.enable,
    }
    this.coreeventseverityService.updateEventEnable(params).subscribe(
      res => {
        if(item.enable) {
          this.toastService.showToast('success', this.languageValues['general.alarm.enableSuccess'], '');
        } else {
          this.toastService.showToast('success', this.languageValues['general.alarm.disabledSuccess'], '');
        }
      },
      err => {
        this.toastService.showToast('error', err.error.err_msg, null);
      }
    )
  }

  changeComplete($event, idx) {
    this.eventSeverityItems[idx]['displayColor'] = $event.color['hex'].toUpperCase();
  }

  selectColor(idx) {
    this.colorShow = true; 
    this.selectIndex = idx;
  }

}
