import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Injectable()
export class RegisterUserService {
  language: any;

  constructor(private http: HttpClient) {
 
  }

  registerUser( param: any){
      const header = new HttpHeaders().set('content-Type', 'application/json');
      return this.http.post('siteweb-flow/api/processInstance/create', param);
  }



  // 批量上传文件接口
  uploadFiles(files: File[]) {
      const formData = new FormData();
      console.log('上传文件列表:', files);

      // 将所有文件添加到FormData中
      files.forEach((file, index) => {
          console.log(`文件 ${index}:`, file.name, file.size, file.type);
          formData.append('files', file);
      });

      // 对于文件上传，不需要设置特殊的头部，让浏览器自动处理
      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData);
  }



  // 测试单文件上传
  uploadSingleFile(file: File) {
      const formData = new FormData();
      formData.append('files', file);
      formData.append('operationType', '2');

      console.log('测试单文件上传:', file.name);
      console.log('文件类型:', file.type);
      console.log('文件大小:', file.size);

      // 对于文件上传，让浏览器自动处理头部信息
      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData);
  }

}
