<app-phoenix-panel
    title="{{ groupNode.groupName | translate }}"
    [extra]="true"
    [panelClass]="'viewport100 with-scroll'"
>
  <form
      class="form-inline-header d-flex justify-content-between align-items-baseline">
    <div class="text-left">
      <button class="btn btn-primary" type="button" (click)="add($event)">
        <i class="iconfont icon-add1"></i>
        <span>{{ 'general.common.add' | translate }}</span>
      </button>
      
      <button class="btn btn-primary ml-1" [disabled]="!hasControlPermission" type="button" (click)="deleteAll()">
        <i class="iconfont icon-delete2"></i>
        <span>{{'idcManage.deviceManagement.deleteInBatches' | translate}}</span>
      </button>

      <button class="btn btn-primary ml-1" [disabled]="!hasControlPermission" type="button" (click)="cardOperate('lost')">
        <i class="iconfont icon-close-rect"> </i>
        <span>{{ 'general.common.cardLost' | translate }}</span>
      </button>

      <button class="btn btn-primary ml-1" [disabled]="!hasControlPermission" type="button" (click)="cardOperate('copy')">
        <i class="iconfont icon-ren-copy"> </i>
        <span>{{ 'general.common.cardCopy' | translate }}</span>
      </button>

      <button class="btn btn-primary ml-1" [disabled]="!hasControlPermission" type="button" (click)="excelExport()">
        <i class="iconfont icon-export"> </i>
        <span>{{ 'general.common.export' | translate }}</span>
      </button>
    </div>
    <div><span>{{ 'resource.itDevice.property.totalSum' | translate }}</span>:{{total}}</div>
    <div class="search-container">
      <input
          type="text"
          class="form-control"
          name="searchText"
          placeholder="{{ 'admin.role.placeholder' | translate }}"
          [(ngModel)]="searchText"
          (ngModelChange)="search($event)"
      />
      <span class="input-group-addon"
      ><i class="ion-ios-search-strong"></i
      ></span>
    </div>
  </form>
  <div class="card-list-container overflow-y-auto">
    <ng2-smart-table
        #table
        class="phoenix-table"
        [settings]="settings"
        [source]="source"
        (delete)="hasControlPermission && deleteAll(true, $event, 'cardName')"
        (edit)="edit($event)"
        (userRowSelect)="selectRows($event)"
        (page)="onPageChange($event)"
    ></ng2-smart-table>
  </div>
</app-phoenix-panel>
