import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CoreModule } from '@app/core/core.module';
import { AppTranslationModule } from '@app/app.translation.module';
import { AlarmFocusPanelRouting } from './alarm-focus-panel.routing';
import { AlarmFocusPanelComponent } from './alarm-focus-panel.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SelectModule } from 'ng-select';
import { Daterangepicker } from 'ng2-daterangepicker';
import { Ng2SmartTableModule } from 'ng2-smart-table';
import { CommunalModule } from '@app/communal/communal.module';
import { FilterTransferModule } from '@app/pages/site/components/params-filter/filter-transfer/filter-transfer.module';
import { DataTableModule, DevUIModule, PaginationModule } from 'ng-devui';
import { AlarmListService } from '../monitor/alarm-list/alarm-list.service';
import { SeverityRendererComponent } from './alarm-focus-panel-severityrenderer.component';
import { CustomColumnsModule } from '@app/pages/site/components/custom-columns/custom-columns.module';

@NgModule({
    imports: [
        CommonModule,
        AppTranslationModule,
        NgbModule,
        SelectModule,
        FormsModule,
        ReactiveFormsModule,
        Ng2SmartTableModule,
        CoreModule.forRoot(),
        CommunalModule,
        Daterangepicker,
        FilterTransferModule,
        DevUIModule,
        DataTableModule,
        PaginationModule,
        AlarmFocusPanelRouting,
        CustomColumnsModule
    ],
    declarations: [
        AlarmFocusPanelComponent,
        SeverityRendererComponent,
    ],
    providers: [AlarmListService]
})
export class AlarmFocusPanelModule { }
