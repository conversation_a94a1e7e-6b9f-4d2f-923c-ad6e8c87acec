<app-phoenix-panel [panelClass]="'viewport100 d-flex'">
  <form novalidate class="phoenix-form" #ngForm="ngForm">
    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.deviceCode'|translate}} </label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceCode" required #assetDeviceCode="ngModel"
        [(ngModel)]="assetDevice.assetCode">
      <i class="star"></i>
      <span class="error" *ngIf="
            (assetDeviceCode.errors?.required ||
              (assetDevice.assetCode && assetDevice.assetCode.trim().length === 0)) &&
            (assetDeviceCode.touched || !ngFormValid)
          ">{{ 'asset.assetDevice.deviceCodeNotNull' | translate }}</span>
    </div>

    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.deviceName'|translate}} </label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceName" required #assetDeviceName="ngModel"
        [(ngModel)]="assetDevice.assetName">
      <i class="star"></i>
      <span class="error" *ngIf="
            (assetDeviceName.errors?.required ||
              (assetDevice.assetName && assetDevice.assetName.trim().length === 0)) &&
            (assetDeviceName.touched || !ngFormValid)
          ">{{ 'asset.assetDevice.deviceNameNotNull' | translate }}</span>
    </div>


    <div class="form-group row">
      <label for="assetCategory"
        class="col-sm-2 form-control-label">{{'asset.assetDevice.deviceType'|translate}}</label>
      <ng-select class="col-sm-8 p-0 phoenix-select" [options]="assetTypeList" name="assetCategory"
        [(ngModel)]="assetCategory" notFoundMsg="{{'general.common.noData' | translate}}" [allowClear]="true">
      </ng-select>
    </div>

    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.brand'|translate}} </label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceBrand" #assetDeviceBrand="ngModel"
        [(ngModel)]="assetDevice.brand">

    </div>


    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.model'|translate}} </label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceModel" #assetDeviceModel="ngModel"
        [(ngModel)]="assetDevice.model">

    </div>


    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.capacityParameter'|translate}} </label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceCapacityParameter"
        #assetDeviceCapacityParameter="ngModel" [(ngModel)]="assetDevice.capacityParameter">

    </div>


    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.settingPosition'|translate}} </label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceSettingPosition"
        #assetDeviceSettingPosition="ngModel" [(ngModel)]="assetDevice.settingPosition">

    </div>

    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.serialNumber'|translate}}</label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceserialNumber" #assetDeviceserialNumber="ngModel"
        [(ngModel)]="assetDevice.serialNumber">
    </div>

    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.manufactor'|translate}}</label>
      <input type="text" class="col-sm-8 form-control" name="assetDeviceManufactor" #assetDeviceManufactor="ngModel"
        [(ngModel)]="assetDevice.manufactor">
    </div>

    <!-- 扩展字段 -->
    <div class="form-group row" *ngIf="assetExtend && assetExtend.length>0">
      <label class="col-sm-2 form-control-label">{{'asset.assetDevice.extendField'|translate}}</label>
      <span class="col-sm-8 line"></span>
    </div>
  
    <div class="form-group row" *ngFor="let item of assetExtend; let i = index">
      <label class="col-sm-2 form-control-label">{{item.extName}} </label>
      <ng-container *ngIf="item.extDataType === 'text' || item.extDataType === 'number'">
        <input [type]="item.extDataType" class="col-sm-8 form-control"
          [(ngModel)]="item['extValueConfiguration']['extValue']" [name]="item.extDataType+i" #ref="ngModel"
          [required]="item.extNecessary">
      </ng-container>

      <ng-container *ngIf="item.extDataType === 'date'">
        <input type="text" class="col-sm-8 form-control" daterangepicker [options]="dateOptions"
          [(ngModel)]="item['extValueConfiguration']['extValue']" [name]="item.extDataType+i" #ref="ngModel"
          [required]="item.extNecessary" (applyDaterangepicker)="onValueChange($event,item)">
      </ng-container>

      <ng-container *ngIf="item.extDataType === 'equipment'">
        <input class="col-sm-7 form-control" readonly
          [ngModel]="item['extValueConfiguration']['eqName']" [name]="item.extDataType+i"
          #ref="ngModel" [required]="item.extNecessary" />
        <div class="btn-list col-sm-1">
          <button type="button" class="btn phoenix-form-btn__primary" debounceDirective
            (debounceClick)="selectDevice(item['extValueConfiguration'])">{{
            'general.common.select' | translate}}</button>
          <button type="button" class="btn phoenix-form-btn__primary" debounceDirective
            (debounceClick)="item['extValueConfiguration']['extValue'] = ''; item['extValueConfiguration']['eqName'] = ''">{{
            'general.common.clear' | translate}}
          </button>
        </div>
      </ng-container>
      
      <!-- 绑定表 -->
      <ng-container *ngIf="item.extDataType === 'table'">
        <nz-select
          class="col-sm-8 form-control no-padding"
          [(ngModel)]="item['extValueConfiguration']['extValue']"
          [name]="item.extDataType + i"
          #ref="ngModel"
          [required]="item.extNecessary"
          (nzScrollToBottom)="loadMore(item.extId)"
          [nzPlaceHolder]="'general.common.pleaseSelect' | translate"
          nzAllowClear
          nzShowSearch
          nzServerSearch
          (nzOnSearch)="onSearch($event, item.extId)"
          [nzDropdownRender]="renderTemplate"
        >
          <nz-option *ngFor="let o of optionListMap[item.extId]" [nzValue]="o[item.labelValue.value]" [nzLabel]="o[item.labelValue.label]"></nz-option>
          <nz-option *ngIf="defaultOptionMap[item.extId]" [nzLabel]="defaultOptionMap[item.extId].label" [nzValue]="defaultOptionMap[item.extId].value" nzHide></nz-option>
        </nz-select>
        <ng-template #renderTemplate>
          <div *ngIf="isLoadingMap[item.extId] === 'noMore'" class="d-flex justify-content-center"> {{ 'general.common.total' | translate }} {{ optionListMap[item.extId].length }} {{ 'general.common.items' | translate }}，{{ 'general.common.noMore' | translate }}</div>
          <nz-spin *ngIf="isLoadingMap[item.extId] === true"></nz-spin>
        </ng-template>
      </ng-container>

      <ng-container *ngIf="item.extNecessary">
        <i class="star"></i>
        <span class="error" *ngIf="(!item['extValueConfiguration']['extValue']) &&
          (!ngFormValid)
            ">{{ item.extName }}{{ 'asset.assetDevice.notNull' | translate }}</span>
      </ng-container>
    </div>






    <div class="form-group row">
      <div class="offset-sm-2 operation">
        <button type="button" class="btn phoenix-form-btn__primary" debounceDirective
          (debounceClick)="submit(this.submitted=true && ngForm.valid)">
          {{ 'general.common.ok' | translate }}
        </button>
        <button type="button" class="btn phoenix-form-btn__default" data-dismiss="modal" (click)="cancel()">
          {{ 'general.common.cancel' | translate }}
        </button>
      </div>
    </div>
  </form>
</app-phoenix-panel>