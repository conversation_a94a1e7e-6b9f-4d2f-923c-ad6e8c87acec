import { Component, OnInit, ElementRef, <PERSON>Child, HostListener } from '@angular/core';
import { LocalDataSource } from 'ng2-smart-table';
import { TranslateService } from '@ngx-translate/core';
import { ToastService, FileService } from '@communal/providers';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import * as XLSX from 'xlsx';
import { AlertService } from '@app/communal/providers';
import { ExcelValidationService } from '@app/pages/asset/excel-validation.service';
import { SwalComponent } from '@sweetalert2/ngx-sweetalert2';
import { ExportExcelFileService } from "@app/communal/providers/exportExcelFile.service";
import * as _ from 'lodash';
import { RackManageService } from '@app/pages/idc-manage/components/rack-management/rack-manage.service';
import { ExcelService } from '@app/communal/providers/excel.service';
import { RackSignalEditComponent } from './components/rack-signal-edit/rack-signal-edit.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { CssVariableService } from '@app/theme/theme.css-variable.service';
@Component({
  selector: 'app-rack-signal-config',
  templateUrl: './rack-signal-config.component.html',
  styleUrls: ['./rack-signal-config.component.scss']
})
export class RackSignalConfigComponent implements OnInit {
  @ViewChild('excelImportSwal') private excelImportSwal: SwalComponent;
  tableSource = new LocalDataSource();
  tableSettings: any;
  serverPaging = {
      number: 0,
      size: undefined,
      totalElements: 0
  };
  selectedRow: any;
  excelData: any[];
  rackImportKeyList: any[] = [];
  validateResult = []
  downloadErrList: any[];
  CHARTS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  searchNameList: Array<any>;
  searchName: string;
  searchText = ''; 
  tableColumns:any = []
  selectedData = []
  constructor(
    private translate: TranslateService,
    private toastService: ToastService,
    private fileService: FileService,
    private element: ElementRef,
    private baseService: BaseCRUDService,
    private alertService: AlertService,
    private excelValidationService: ExcelValidationService,
    private exportExcelFileService: ExportExcelFileService,
    private rackService: RackManageService,
    private excelService: ExcelService,
    private model: NgbModal,
    private cssVariableService: CssVariableService
  ) {}

  ngOnInit() {
    this.rackImportKeyList = [
      { key: 'computerRackName', value: this.translate.instant('idcManage.rackSignal.rackName') },
      { key: 'openExpression', value: this.translate.instant('idcManage.rackSignal.activationRate') },
      { key: 'powerExpression', value: this.translate.instant('idcManage.rackSignal.powerRate') }
    ]
    this.initTableSettings();
    this.tableSource.onChanged().subscribe(change => {
      if (change.action === 'page') {
        this.selectedData = [];  // 清空选中的数据
      }
    })

    // this.loadData();
  }

  // 添加窗口大小改变的监听器
  @HostListener('window:resize', ['$event'])
  onResize(): void {
    this.resizeHandler();
  }

  // 窗口大小变化后
  resizeHandler(): void {
    const sizeIsChange = this.updatePageSizeIfChanged();
    // 当列表在第一页并且表格行数发生变化，用户调整窗口高度时，始终保持表格满屏输出。可能比较吃接口调用
    if (this.serverPaging.number > 0 || !sizeIsChange) return
    this.loadData();
  }

  /**
   * 更新表格高度并检查每页显示行数是否有变化
   * @returns 如果每页显示行数有变化则返回true，否则返回false
   */
  updatePageSizeIfChanged(): boolean {
    const tableContainer = document.getElementById('app-rack-signal-config');
    // 每页显示行数是否有变化
    let sizeIsChange = false
    if (tableContainer) {
      // 获取容器高度
      // TODO，刷新浏览器时，containerHeight总会多出32px
      const containerHeight = tableContainer.offsetHeight;
      // 更新表格每页显示的行数 = （内容区 - 表格头高度 - 滚动条高度）/ 表格行高度
      const newSize = this.cssVariableService.getTablePageSize(containerHeight);
      if (newSize !== this.serverPaging.size) {
        this.serverPaging.size = newSize
        sizeIsChange = true
      }
    }
    return sizeIsChange
  }

  /**
   * 视图初始化后的生命周期钩子
   */
  ngAfterViewInit(): void {
    // TODO，刷新浏览器时，containerHeight总会多出32px，延时300毫秒可解决
    // 在组件内容初始化后调用一次，确保表格大小正确
    this.updatePageSizeIfChanged();
    // 初始化后请求数据
    this.loadData();
    [300, 600, 900, 1200].forEach(interval => {
      setTimeout(() => {
        this.resizeHandler();
      }, interval);
    });
  }

  /**
   * 处理分页切换事件
   * 
   * @param num - 页码数
   *           - 如果num为-1则提前返回
   */
  onPageChange(num: number): void {
    if (num === -1) {
      return
    }
    this.serverPaging.number = num;
    this.loadData();
  }

  // 覆盖父类方法，加入分页和搜索功能
  loadData(): void {
    const params: any = {
      page: this.serverPaging.number, // 页码从0开始
      size: this.serverPaging.size
    };
    // 如果有搜索关键字，添加到请求参数
    if (this.searchText) {
      params.keyword = this.searchText;
    }
    // 排序参数，默认按启用日期降序
    params.sort = 'computerRackId,asc';
    this.baseService.get(params, 'computerrackssignals').subscribe((res) => {
      if (res) {
        // 更新总数，用于分页
        const newPaging = {
          number: this.serverPaging.number,
          size: this.serverPaging.size,
          totalElements: res.total
        };
        this.serverPaging = newPaging;
        // 更新当前数据列表
        // this.currentDataList = res.records || [];
        // 加载数据到表格
        // this.source = new LocalDataSource(this.currentDataList);
        this.tableSource.load(res.records || [])
      }
    });
  }

  // 处理搜索事件
  search(): void {
    // 重置页码为第一页
    this.serverPaging.number = 0;
    // 调用获取数据方法
    this.loadData();
  }

  initTableSettings() {
    const perPage = Math.floor((window.innerHeight - (60 + 16*2 + 1*2 + 55 + 38 + 67.5)) / 38);
    this.tableSettings = {
      hideSubHeader: true,
      mode: 'external',
      selectMode: 'multi',
      pager: {
        display: false,
        sort: false,
        perPage
      },
      noDataMessage: this.translate.instant('general.common.noData'),
      actions: {
        columnTitle: this.translate.instant('general.common.action'),
        add: false,
        edit: false,
        delete: false,
        position: 'right',
        custom: [
          {
            name: 'edit',
            title: '<i class="iconfont operation-icon icon-edit1" title="编辑"></i>'
          },
          {
            name: 'delete',
            title: '<i class="ion-trash-a" title="删除"></i>'
          }
        ]
      },
      columns: {
        computerRackName: {
          title: this.translate.instant('idcManage.rackSignal.rackName'),
          sort: false
        },
        openExpressionStr: {
          title: this.translate.instant('idcManage.rackSignal.activationRate'),
          sort: false
        },
        powerExpressionStr: {
          title: this.translate.instant('idcManage.rackSignal.powerRate'),
          sort: false
        }
      },
      rowClassFunction: (row) => {
        return 'cursor-pointer';
      }
    };
    this.tableColumns = Object.keys(this.tableSettings.columns).map(key => {
      return {
        name: key,
        title: this.tableSettings.columns[key].title
      };
    });
  }

  // search(query = '') {
  //   if (query.length < 1) {
  //     this.tableSource.setFilter([]);
  //     return;
  //   }
  //   query = query.trim();
  //   this.searchNameList = this.getSearchField(
  //     this.tableColumns,
  //     query
  //   );
  //   const filterConf =
  //     this.searchNameList.length !== 0
  //       ? this.searchNameList
  //       : [{ field: this.searchName, search: query }];
  //   this.tableSource.setFilter(filterConf, false);
  // }
  getSearchField(columns, query = '') {
    const searchList = [];
    if (columns && columns.length !== 0) {
      columns.forEach((column) => {
        const fieldObj = { field: column.name, search: query };
        searchList.push(fieldObj);
      });
    }
    return searchList;
  }
  onCustom(event: any) {
    this.selectedRow = event.data;  // 保存选中行
    if (event.action === 'delete') {
      this.deleteSignal(event.data,'single');
    } else if (event.action === 'edit') {
      this.openEditModal(event.data);
    }
  }
  openEditModal(data: any) {
    const modalRef = this.model.open(RackSignalEditComponent, {
      backdrop: 'static',
      container: '#app-rack-signal-config',  // 设置父容器
      size: 'lg',  // 设置弹窗大小
      windowClass: 'rack-signal-edit-modal'  // 添加自定义样式类
    });
    modalRef.componentInstance.editData = { ...data };
    modalRef.result.then((result) => {
      if (result && result === 'success') {
        this.loadData();
      }
    });
  }
  userRowSelect(e) {
    this.selectedData = e.selected
  }
  
  deleteSignal(data: any,type: string) {
    let ids, tipName = ''
    if (type === 'batch') {
      if (this.selectedData.length === 0)
        return
      ids = this.selectedData.map(item => item.computerRackId).join(',')
      tipName = '所选机架'
    } else {
      ids = data.computerRackId
      tipName = data.computerRackName
    }
    this.alertService.deleteConfirm(`确认删除 ${tipName} 吗`).then((res) => {
      if (res.value) {
        this.baseService.delete(ids,'computerrackssignals').subscribe((res) => {
          this.toastService.showToast('success', this.translate.instant('general.common.deleteSuccess'), null);
          this.loadData();
          this.selectedData = []
          const headerCheckbox = document.querySelector('ng2-smart-table thead input[type="checkbox"]') as HTMLInputElement;
          if (headerCheckbox) { // 更新表头复选框状态 
            headerCheckbox.checked = false;
          }
        }, (err) => {
          this.toastService.showToast('error', this.translate.instant('general.common.failed'),null);
        });
      }
    });
  }

  downTemplate() {
    const excel = localStorage.getItem('lang') === 'zh' ? 'RackSignalsTemplate' : 'RackSignalsTemplate_en';
    let fileName = this.translate.instant('idcManage.rackSignal.template')
    this.fileService.downloadAssets(`xlsx/${excel}.xlsx`, `${fileName}.xlsx`).subscribe(res => {
      this.toastService.showToast('success', this.translate.instant('general.common.downloadSuccess'), null);
    }, err => {
      this.toastService.showToast('error', this.translate.instant('general.common.downloadFailure'), null);
    });
  }

  onImport() {
    this.element.nativeElement.querySelector(".hidden-file").click();
  }

  onFileChange(event: any) {
    const target: DataTransfer = event.target as DataTransfer;
    const reader: FileReader = new FileReader();
    reader.onload = (e: any) => {
      const bstr: string = e.target.result;
      const wb: XLSX.WorkBook = XLSX.read(bstr, { type: 'binary' });
      const wsNameArr = wb.SheetNames;
      const data: any[] = [];
      wsNameArr.forEach(item => {
        const ws: XLSX.WorkSheet = wb.Sheets[item];
        data.push(XLSX.utils.sheet_to_json(ws, { header: 1 }))
      })
      this.importData(data[0])
    };
    reader.readAsBinaryString(target.files[0]);
    event.target.value = null;
  }

  importData(data: any[]) {
    if (data.length <= 1) {
      return;
    }
    this.excelData = data.slice(1); // 导出二维数组数据
    this.excelData = this.excelData.filter(item => item.length > 0);
    this.excelData.forEach(item => {
      this.rackImportKeyList.forEach((key, index) => {
        item[index] = item[index] || '';
      })
    })
    const modelNameList = data.map(item => item[0] || ''); 
    const validateRules = [
      [{ 'rule': 'required' }, { 'rule': 'uniqueField', 'params': modelNameList }],//机架名称唯一
      [{ 'rule': 'required' }],
      [{ 'rule': 'required' }]
    ];
    this.validateResult = this.excelValidationService.dataValidate(this.excelData, validateRules, 1);
    this.downloadErrList = [this.validateResult];
    if (this.validateResult.length > 0) {
      this.excelImportSwal.fire();
    } else {
      this.sendExcelData(this.excelData);
    }
  }
  sendExcelData(data) {
    const sendData = [];
    data.forEach(item => {
      const obj = {};
      this.rackImportKeyList.forEach((i, index) => {
        obj[i.key] = item[index];
      });
      sendData.push(obj);
    })
    this.send(sendData);
  }
  send(data) {
    this.baseService.create(data, 'computerrackssignals/import').subscribe(res => {
      if (res && res.length > 0) {
        this.showErrorForSwal(res);
      } else {
        this.toastService.showToast(
          'info',
          this.translate.instant('idcManage.rackManage.importSuccess'),
          null
        );
        this.loadData();
      }
    }, err => {
      this.toastService.showToast(
        'error',
        this.translate.instant('idcManage.rackManage.importFailed'),
        null
      );
    })
  }
  showErrorForSwal(errorList) {
    const errorDetailList = [];
    errorList.forEach(err => {
      const errColumn = this.CHARTS[this.rackImportKeyList.findIndex(item => item.key === err.errorName)];
      errorDetailList.push(`行:${err.rowIndex + 2},列:${errColumn},${err.message}`);
    })
    this.validateResult = errorDetailList;
    this.downloadErrList = [errorDetailList];
    this.excelImportSwal.fire();
  }
  exportErrorExcel() {
    const titleData = [this.rackImportKeyList];
    const originData = [this.excelData];
    const sheetNameData = ['机架信号配置列表'];
    const downloadErrList = _.clone(this.downloadErrList);
    if (downloadErrList.length > 1) {
      downloadErrList.forEach(item => {
        item.shift();
      })
    }
    const workbookData = this.rackService.exportExcel(titleData, originData, downloadErrList, sheetNameData);
    this.excelService.exportExcelFile(workbookData, '机架信号配置').subscribe(
      res => {
        this.toastService.showToast('success', this.translate.instant('general.common.downloadSuccess'), null);
        return true;
      },
      err => {
        this.toastService.showToast(
          'error',
          this.translate.instant('report.batteryAlarmHistoryReport.downloadExcelFileFail'),
          null
        );
        return false;
      }
    );
  }

  onExport() {
    const currentData: any = this.tableSource.getAll();
    currentData.then((res: any) => {
      let tableData: any = res
      if (!tableData || tableData.length === 0) {
        this.toastService.showToast('error', this.translate.instant('general.common.noData'), null);
        return;
      }

      let titleKeys = Object.keys(this.tableSettings.columns)
      let titleNames = []
      titleKeys.forEach(key => {
        titleNames.push(this.tableSettings.columns[key].title)
      })
      let exportData = tableData.map(item => {
        let filteredItem = {};
        titleKeys.forEach(key => {
          if (item.hasOwnProperty(key)) {
            filteredItem[key] = item[key];
          }
        });
        return filteredItem;
      })
      const sheetData = this.exportExcelFileService.exportExcelSheetData(0, titleNames, exportData, "sheet1");
      const workbookData = this.exportExcelFileService.exportExcelWorkSheet("", sheetData);
      this.exportExcelFileService.exportExcelFile(workbookData, '机架信号配置列表').subscribe(res => {
        this.toastService.showToast('success', this.translate.instant("energyPanorama.message.exportSuccess"), null);
        return
      }, err => {
        this.toastService.showToast(
          'error',
          this.translate.instant("energyPanorama.message.exportFailed"),
          null
        );
        return
      })

    })
  }
}