import { Injectable } from '@angular/core';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class AssetDeviceService extends BaseCRUDService {
    constructor(private http: HttpClient) {
        super(http, 'idcassetdevices')
    }
    importFromEqu() :Observable<any>{
        return this.http.get<any>("idcassetdevices/equsync");
    }
    batchDelete(ids:number[]) :Observable<any>{
        return this.http.post<any>("batchdelete",ids);
    }
    loadExtFieldOptionData(extFieldId: number, labelValue: string, pageNumber: number, pageSize: number): Observable<any> {
        return this.http.get<any>(`loadextfieldoptiondata?extFieldId=${extFieldId}&labelValue=${labelValue}&pageNumber=${pageNumber}&pageSize=${pageSize}`);
    }
    loadExtFieldById(extValId: number): Observable<any> {
        return this.http.get<any>(`getextfieldvaluelabeldatabyextvalid?extValId=${extValId}`);
    }
}