import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';

@Injectable()
export class CoreeventseverityService extends BaseCRUDService {

  constructor(private http: HttpClient) {
    super(http, 'coreeventseverities');
  }

  getCoreEventSeverities(): Observable<any> {
    return this.getAll();
  }

  updateCoreEventSeverity(coreEventSeverity: any): Observable<any> {
    return this.update(coreEventSeverity);
  }

  updateEventEnable(params: any): Observable<any> {
    return this.http.put('coreeventseverities/enable', params);
  }

  addAlarmReason(params: any): Observable<any> {
    return this.http.post('eventreasontypes', params);
  }

  editAlarmReason(params: any): Observable<any> {
    return this.http.put('eventreasontypes', params);
  }

  deleteAlarmReason(params: any): Observable<any> {
    return this.http.delete(`eventreasontypes/${params}`, );
  }
}
