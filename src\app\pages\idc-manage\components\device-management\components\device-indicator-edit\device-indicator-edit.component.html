<DIV class="row">
    <div class="col-6">
        <ba-card baCardClass="viewport100 with-scroll" class="w-100">
            <ng2-smart-table #table class="phoenix-table" [settings]="settings" [(source)]="source"
                (edit)="edit($event)" (userRowSelect)="selectedRow($event)" (delete)="delete($event)">
            </ng2-smart-table>
        </ba-card>
    </div>
    <div class="col-6 ">
        <ba-card baCardClass="viewport100 with-scroll" class="w-100" cha>
            <div class="addButton">
                <button class="btn btn-primary" type="button" (click)="cancel()">
                    <i class="ion-plus-round"></i>
                    <span style="margin-left: 8px;">{{'general.common.add' | translate}}</span>
                </button>
            </div>
            <form #ngForm="ngForm" class="phoenix-form">
                <!--业务分类 树型下拉框-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.businessId' |
                        translate}}</label>
                    <div class="col-sm-9 p-0 phoenix-select business">
                        <input type="text" name="businessTypeName" class="form-control tree-select"
                            (click)="showTreeview = !showTreeview" [(ngModel)]="businessTypeName" (blur)="inputBlur()"
                            autocomplete="off">
                        <div class="treeview" [hidden]="showTreeview">
                            <tree class="tree_data" #treeComponent [settings]="treeSettings" [tree]="tree"
                                (nodeSelected)="logEvent($event)"></tree>
                        </div>
                    </div>
                </div>
                <!--指标定义-->
                <div class="form-group row" *ngIf="indexDefinitionList.length > 0">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.indicatorDefinition'
                        | translate}}</label>
                    <ng-select class="col-sm-9 p-0 phoenix-select" notFoundMsg="{{'general.common.noData' | translate}}"
                        [options]="indexDefinitionList" [clearable]="true" [allowClear]="true"
                        bindLabel="complexIndexDefinitionName" bindValue="complexIndexDefinitionId"
                        name="complexIndexDefinitionId" autocomplete="off"
                        [(ngModel)]="indicatorData.complexIndexDefinitionId" (selected)="definitionSelected($event)">
                    </ng-select>
                </div>
                <!--指标名称-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.indicatorName' |
                        translate}}</label>
                    <input name="complexIndexName" class="col-sm-9 form-control"
                        [ngClass]="{'errors':complexIndexName.errors && (complexIndexName.touched || !ngFormValid)}"
                        type="text" required maxlength="255" #complexIndexName="ngModel"
                        [(ngModel)]="indicatorData.complexIndexName">
                    <span class="star"></span>
                </div>
                <!--指标质量-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.indicatorQuality' |
                        translate}}</label>
                    <input name="checkExpression" class="col-sm-9 form-control" type="text" maxlength="255"
                        [(ngModel)]="indicatorData.checkExpression">
                </div>
                <!--指标计算顺序-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.indicatorCalcOrder' |
                        translate}}</label>
                    <input name="afterCalc" class="col-sm-9 form-control" type="number" min="1" max="9999"
                        [(ngModel)]="indicatorData.afterCalc" 
                        (ngModelChange)="onCalcOrderChange($event)">
                </div>
                <!--表达式-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.formula' |
                        translate}}</label>
                    <div class="col-sm-9 form-control"
                        [ngClass]="{'errors':expression.errors && (expression.touched || !ngFormValid)}">
                        <div class="form-group row">
                            <button class="btn btn-xs btn-primary" (click)="openExpress(1)">
                                <span>{{'idcManage.capacity.fromComplexIndex' | translate}}</span>
                            </button>
                            &nbsp;&nbsp;
                            <button class="btn btn-xs btn-primary" (click)="openExpress(2)">
                                <span>{{'idcManage.capacity.fromCorePoint' | translate}}</span>
                            </button>
                        </div>
                        <div class="form-group row">
                            <textarea #expression="ngModel" readonly class="form-control" name="expression"
                                [(ngModel)]="indicatorData.expression" required
                                placeholder="{{'idcManage.hierarchy.indicatorEdit.formulaPrompt'|translate}}"></textarea>
                        </div>
                    </div>
                    <span class="star"></span>
                </div>
                <!--表达式-->
                <div class="form-group row" *ngIf="isIndex||isPoint">
                    <label class="col-sm-2 form-control-label"
                        *ngIf="isIndex">{{'idcManage.capacity.associateComplexIndex' | translate}}</label>
                    <label class="col-sm-2 form-control-label" *ngIf="isPoint">{{'idcManage.capacity.associateCorePoint'
                        | translate}}</label>
                    <div class="col-sm-9 form-control">
                        <div *ngIf="isIndex" class="form-group row pointcontent">
                            <ul>
                                <li class="items" *ngFor="let item of selectIndexList; let ins = index">

                                    <span
                                        title="{{item.globalResourceName}}--{{item.complexIndexName}}">{{item.complexIndexName}}</span>
                                    <span
                                        title="{{item.globalResourceName}}--{{item.complexIndexName}}">{{item.complexIndexId}}</span>
                                </li>
                            </ul>

                        </div>
                        <div class="form-group row pointcontent index-allocation-padding" *ngIf="isPoint">
                            <ul class="index-allocation-ul">
                                <li class="items" *ngFor="let item of selectPointList; let ins = index">
                                    <span
                                        title="{{item.resourceStructureName}}--{{item.equipmentName}}--{{item.signalName}}">{{item.resourceStructureName}}--</span>
                                    <span
                                        title="{{item.resourceStructureName}}--{{item.equipmentName}}--{{item.signalName}}">{{item.equipmentName}}--</span>
                                    <span
                                        title="{{item.resourceStructureName}}--{{item.equipmentName}}--{{item.signalName}}">{{item.signalName}}--</span>
                                    <span
                                        title="{{item.resourceStructureName}}--{{item.equipmentName}}--{{item.signalName}}">{{item.signalId}}</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!--计算周期-->
                <!-- <div class="form-group"> -->
                    <cron-selector [labelName]="'idcManage.hierarchy.indicatorEdit.countPeriod' | translate"
                        [cron]="indicatorData.calcCron" [invalid]="countPeriodRequired"
                        [expression]="indicatorData.expression" (getCron)="getCalcCron($event)">
                    </cron-selector>
                <!-- </div> -->
                <!--存储周期-->
                <!-- <div class="form-group"> -->
                    <cron-selector [labelName]="'idcManage.hierarchy.indicatorEdit.savePeriod' | translate"
                        [cron]="indicatorData.saveCron" [invalid]="saveCronRequired" [defaultCron]="defaultCron"
                        [expression]="indicatorData.expression" (getCron)="getSaveCron($event)">
                    </cron-selector>
                <!-- </div> -->
                <!--单位-->
                <!-- <div class="form-group"> -->
                    <indicator-unit-selector [labelName]="'idcManage.hierarchy.indicatorEdit.unit' | translate"
                        [unit]="indicatorData.unit" (getUnit)="getUnit($event)">
                    </indicator-unit-selector>
                <!-- </div> -->
                <!--精度-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.precision' |
                        translate}}</label>
                    <!-- <select class="col-sm-9 form-control" name="accuracy" [(ngModel)]="indicatorData.accuracy">
                        <option *ngFor="let item of accuracyOptionList" value="{{item}}">{{item}}</option>
                    </select> -->
                    <ng-select class="col-sm-9 p-0 phoenix-select" notFoundMsg="{{'general.common.noData' | translate}}"
                        [options]="accuracyOptionList" [clearable]="true" [allowClear]="true"
                        name="accuracy"
                        [(ngModel)]="indicatorData.accuracy" (selected)="accuracySelected($event)">
                    </ng-select>
                </div>
                <!--是否差值计算-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.iscalctype' |
                        translate}}</label>
                    <!-- <select class="col-sm-9 form-control" name="calcType" [(ngModel)]="indicatorData.calcType">
                        <option></option>
                        <option value="1">{{'idcManage.common.isShow' | translate}}</option>
                    </select> -->
                    <ng-select class="col-sm-9 p-0 phoenix-select" notFoundMsg="{{'general.common.noData' | translate}}"
                        [options]="iscalctypeList" [clearable]="true" [allowClear]="true"
                        name="calcType"
                        [(ngModel)]="indicatorData.calcType" (selected)="calcTypeSelected($event)">
                    </ng-select>
                </div>
                <!--描述-->
                <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.description' |
                        translate}}</label>
                    <textarea class="col-sm-9 form-control" name="description"
                        [(ngModel)]="indicatorData.remark"></textarea>
                </div>
                <!--批量生成指标-->
                <!-- <div class="form-group row">
                    <label class="col-sm-2 form-control-label">{{'idcManage.hierarchy.indicatorEdit.batchIndicator' |
                        translate}}</label>
                    <div class="col-sm-9 form-control radio-group">
                        <label *ngFor="let item of batchTypes; let i = index" class="custom-control custom-radio"
                            data-toggle="tooltip" data-placement="top" title="{{item.tooltip | translate}}">
                            <input type="radio" [(ngModel)]="indicatorData.batchType" name="'option'"
                                [value]="item.value" />
                            <span>{{ item.name| translate }}</span>
                        </label>
                    </div>
                </div> -->

                <!--确定/取消按钮-->
                <div class="form-group row">
                    <div class="offset-sm-2 operation">
                        <button type="button" class="btn phoenix-form-btn__primary" debounceDirective
                            (debounceClick)="submitClick(this.submitted = true && ngForm.valid)">
                            {{'general.common.ok' | translate}}</button>
                        <button type="button" (click)="cancel()"
                            class="btn phoenix-form-btn__default">{{'deviceBook.common.cancel'|translate}}</button>
                        <!-- 模态框（Modal） -->
                        <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
                            aria-hidden="true" data-backdrop="false">
                            <div class="modal-dialog">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title" id="myModalLabel">
                                            {{'idcManage.capacity.modalTip' | translate}}
                                        </h4>
                                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">
                                            &times;
                                        </button>
                                    </div>
                                    <div class="modal-body d-flex justify-content-around">
                                        <label class="checkbox-inline custom-checkbox nowrap"
                                            *ngFor="let item of fields; let i = index" id='i' key='i'>
                                            <input type="checkbox" [(ngModel)]="item.checked" name="option"
                                                [value]="item.value" />
                                            <span>{{ item.name| translate }}</span>
                                        </label>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-default" data-dismiss="modal">
                                            {{'general.common.close'|translate}}
                                        </button>
                                        <button type="button" class="btn btn-primary" (click)="modalSubmit()">
                                            {{'general.common.ok' | translate}}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </ba-card>
    </div>
</DIV>