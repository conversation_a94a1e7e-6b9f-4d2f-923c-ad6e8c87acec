<div class="modal-content">
    <div class="modal-header">
      <h4 class="modal-title">{{'s2.shieldevent.config'| translate}}</h4>
      <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="close()">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form #ngForm="ngForm" class="phoenix-form">
        <div class="form-group">
          <label class="control-label">{{'s2.shieldevent.starttime' | translate}}</label>
          <div class="input-group">
            <input
              class="form-control"
              name="startTime"
              type="text"
              readonly
              [(ngModel)]="startTime"
              placeholder="{{ 'general.mask.startTime' | translate }}"
              (applyDaterangepicker)="selectTime($event, 'startTime')"
              daterangepicker
              [options]="optionsStartTime"
              [disabled]="dateDisabled"
        />
          </div>
          <label class="control-label">{{'s2.shieldevent.endtime' | translate}}</label>
          <div class="input-group">
            <input
              class="form-control"
              name="endTime"
              type="text"
              readonly
              [(ngModel)]="endTime"
              placeholder="{{ 'general.mask.endTime' | translate }}"
              (applyDaterangepicker)="selectTime($event, 'endTime')"
              daterangepicker
              [options]="optionsEndTime"
              [disabled]="dateDisabled"
            />
          </div>
          <label *ngFor="let s of shieldtype; let i = index" class="custom-control custom-radio">
            <input type="radio" [(ngModel)]="shieldt" name="shieldt" [value]="s.value" (click)="changeShieldtype(s.value)" />
            <span style = "padding-left: 0px;">{{ s.name| translate }}</span>
          </label>
        </div>
        <div class="content">
          <ul class="weeks">
            <li class="week item" *ngFor="let week of weeks;">
              <div class="weektime">
                <span>{{ week.name }}</span>
                <div class="name week-name" [ngClass]="{
                  'selectcolor': shieldt===2&&week['selected'],
                  'defaultcolor': (shieldt===2&&!week['selected'])||shieldt===1
                   }" (click)="isAllCheck(week.name)"></div>
                <span>{{'s2.shieldevent.time' | translate}}</span>
              </div>
              <ul class="spantime">
                <li *ngFor="let span of week['time']" class="time" (click)="isCheck(week.name,span.index)" [ngClass]="{
                    'selectcolor': shieldt===2&&span['selected'],
                    'defaultcolor': (shieldt===2&&!span['selected'])||shieldt===1
                  }"><span class="name" title="{{span.title}}"></span></li>
              </ul>
            </li>
          </ul>
        </div>
        <div class="form-group">
          <label class="control-label">{{'s2.Authority.Department.description' | translate}}</label>
          <div class="row flex_row">
            <input class="form-control" type="text" name="reason" #description="ngModel" required [(ngModel)]="reason"
              style="width: 95%">
            <i class="star"></i>
          </div>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary cancel-btn" data-dismiss="modal"
        (click)="close()">{{'selectResource.cancel' | translate}}</button>
      <button type="button" class="btn btn-primary" debounceDirective
        (click)="submitMenu()">{{ 's2.shieldevent.save' | translate}}</button>
      <button type="button" class="btn btn-primary" debounceDirective *ngIf="showDelete"
        (click)="delete()">{{ 's2.shieldevent.delete' | translate}}</button>
    </div>
  </div>