import { Component, OnInit, Injector } from '@angular/core';
import { BaseEditComponent } from '@app/communal/components/base-edit/baseEditComponent';
import { AssetExtend } from '../asset-extend.model';
import { ExtFidldConfigurationsService } from '../extfield-configurations.service';

// 定义扩展数据绑定表的接口
interface ExtDataSource {
  table: string;
  label: string;
  value: string;
}

@Component({
  selector: 'app-asset-extend-edit',
  templateUrl: './asset-extend-edit.component.html',
  styleUrls: ['./asset-extend-edit.component.scss']
})
export class AssetExtendEditComponent extends BaseEditComponent implements OnInit {
  assetExtend: AssetExtend = new AssetExtend();
  extDataSource: ExtDataSource = {} as ExtDataSource;

  constructor(public extFidldConfigurationsService: ExtFidldConfigurationsService, injector: Injector) {
    super(injector, extFidldConfigurationsService, {
      baseUrl: 'extfieldconfigurations', translatePrefix: 'asset.extendField'
    });
    this.initTranslateMessage([]);
}

  ngOnInit() {
    this.extFidldConfigurationsService.getAll('extfieldconfigurations/' + this.activatedRoute.snapshot.params['id']).subscribe((res: any) => {
      this.assetExtend = res;
      if (this.assetExtend.extDataType === 'table') {
        try {
          this.extDataSource = JSON.parse(this.assetExtend.extDataSource);
        } catch (err) {
          console.error('JSON格式错误:', err.message);
        }
      }
   })
  }

  updateData(value){
    this.assetExtend.extNecessary = value;
  }

  checkComponentsArgs(){
    if(this.assetExtend.extName.trim().length ===0){
      return false;
    } 
    if(!this.assetExtend){
      return false;
    }
    return true;
  }

  beforeSubmitAction() {
    // 如果扩展数据类型为表，则将扩展数据源转换为字符串
    if (this.assetExtend.extDataType === 'table') {
      this.assetExtend.extDataSource = JSON.stringify(this.extDataSource);
    }
      return this.assetExtend;
  }

}
