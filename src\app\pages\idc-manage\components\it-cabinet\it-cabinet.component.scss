:host {
    display: block;
    height: 100%;

    ::ng-deep {
        .container-fluid {
            height: 100%;
            overflow: hidden; // 避免横向滚动条
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
        .left-panel {
            height: 100%;
            padding-right: 0;
            .left-box {
                height: 100%;
                display: flex;
                flex-direction: column;
                margin-right: 0;

                .search-box {
                    display: flex;
                    flex-direction: row; 
                    padding: 8px 8px 0 8px;
                }
            }
            .tree-container {
                    flex: 1;
                    overflow: auto;
                    margin-top: 10px;
                }
        }

        .right-panel {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden; // 避免横向滚动条

            .date-select {
                flex: none;
                padding-bottom: 5px;
                display: flex;
                // margin-right: 10px;
                .select-width {
                    width: 150px;
                }
                .node-title {
                    flex: 50%;
                    margin: auto;
                }
            }
            .date-box {
               
                margin-left: 10px;
                .daterange-group-box {
                    height: inherit;
                    input {
                        height: 34px !important;
                        line-height: 34px !important;
                         width: 330px;
                    }
                }
            }

            .right-row-1 {
                flex: 3;
                margin-bottom: 10px;
                min-height: 0;
                overflow: hidden; // 避免超出

                .col-6 {
                    height: 100%;
                }
                .row-1-item {
                    padding-right: 0 !important;
                }
                .row-1-item2 {
                    padding-right: 0 !important;
                }
            }

            .right-row-2,
            .right-row-3 {
                flex: 3.5;
                margin-bottom: 10px;
                min-height: 0;
                overflow: hidden; // 防止溢出

                &:last-child {
                    margin-bottom: 0;
                }

                .col-12 {
                    height: 100%;
                }
            }
            #powerBarChart,#powerTrendChart,#loadRateChart {
                height: 100%;
                width: 100%;
                canvas {
                    height: 95% !important;
                    width: 95%;
                }
            }
        }

        .item-box {
            height: 100%;
            padding: 0 10px;
            display: flex;
            flex-direction: column;

            &-title {
                flex: none;
                display: flex;
                align-items: center;
                height: 30px;
            }

            &-content {
                flex: 1;
                min-height: 0;
                display: flex;
                overflow: hidden; // 防止内容溢出
            }
        }

        .row-1-box {
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 5px 0;
            width: 100%;
            overflow: hidden; // 避免横向滚动条

            .powerLoadChart {
                flex: 70%;
                width: 80%;
                height: 80% !important;
                min-width: 0; // 解决 flex 计算错误
                min-height: 0; // 解决内容撑开问题
            }

        }

        .chart-container {
            flex: 1;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden; // 防止溢出
        }

        .flex-layout {
            display: flex;
        }

        .border-style {
            box-shadow: var(--shadow-box-inset, none);
            background: var(--bg-panel-body);
            border: 0;
        }

        .title-icon {
            width: 5px;
            height: 15px;
            background-color: var(--link-active-color);
        }

        .font-padding {
            margin: 0 8px;
            font-size: 1rem;
            color: #fff; // 设计规范目前只有字节有图表标题颜色，先写死
        }

        .form-control {
            height: 32px;
            padding: 4px 8px;
        }

        nz-tree {
            .ant-tree {
                font-size: 14px;
            }
        }

        .power-info {
            margin-top: 0;
            flex: 30%;
            .rated-power {
                margin-bottom: 30px;
            }
            .number-val {
               font-size: 24px;
               margin-bottom: 12px;
               color: #fff;
            }
            .number-title {
               font-size: 14px;
               color: rgba(223, 238, 255, 0.8)
            }
        }
    }
}