<app-phoenix-panel [panelClass]="'viewport100 d-flex'">
  <form novalidate class="phoenix-form" #ngForm="ngForm">
    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{ 'asset.extendField.extName' | translate }} </label>
      <input type="text" class="col-sm-6 form-control" name="extName" required #extName="ngModel"
        [(ngModel)]="assetExtend.extName" (ngModelChange)="checkDuplicate($event)" />
      <i class="star"></i>
      <span class="error" *ngIf="
            (extName.errors?.required ||
              (assetExtend.extName && assetExtend.extName.trim().length === 0)) &&
            (extName.touched || !ngFormValid)
          ">{{ 'asset.extendField.extNameNotNull' | translate }}</span>
      <span class="error" *ngIf="duplicate">{{ 'asset.assetDevice.assetExtendNameNotRepeat' | translate }}</span>
    </div>

    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{ 'asset.extendField.extDataType' | translate }} </label>
      <nz-select class="col-sm-6 form-control" [(ngModel)]="assetExtend.extDataType" name="extDataType" required
        #extDataType="ngModel">
        <nz-option nzLabel="{{ 'asset.extendField.text' | translate }}" nzValue="text"></nz-option>
        <nz-option nzLabel="{{ 'asset.extendField.number' | translate }}" nzValue="number"></nz-option>
        <nz-option nzLabel="{{ 'asset.extendField.date' | translate }}" nzValue="date"></nz-option>
        <nz-option nzLabel="{{ 'asset.extendField.bindDevice' | translate }}" nzValue="equipment"></nz-option>
        <nz-option nzLabel="{{ 'asset.extendField.bindTable' | translate }}" nzValue="table"></nz-option>
      </nz-select>
      <i class="star"></i>
      <span class="error" *ngIf="
            (extDataType.errors?.required ||
              (assetExtend.extDataType && assetExtend.extDataType.trim().length === 0)) &&
            (extDataType.touched || !ngFormValid)
          ">{{ 'asset.extendField.extDataTypeNotNull' | translate }}</span>
    </div>

    <!-- 表名（数据库表名） -->
    <div class="form-group row" *ngIf="assetExtend.extDataType === 'table'">
      <label class="col-sm-2 form-control-label" [title]="'asset.extendField.tableName' | translate">{{ 'asset.extendField.tableName' | translate }}</label>
      <input class="col-sm-6 form-control" name="tableName" [(ngModel)]="extDataSource.table" />
    </div>

    <!-- 字段名（下拉框显示名称） -->
    <div class="form-group row" *ngIf="assetExtend.extDataType === 'table'">
      <label class="col-sm-2 form-control-label" [title]="'asset.extendField.fieldNameTips' | translate">{{ 'asset.extendField.fieldName' | translate }}</label>
      <input class="col-sm-6 form-control" name="fieldName" [(ngModel)]="extDataSource.label" />
    </div>

    <!-- 字段值（下拉框存储值） -->
    <div class="form-group row" *ngIf="assetExtend.extDataType === 'table'">
      <label class="col-sm-2 form-control-label" [title]="'asset.extendField.fieldValueTips' | translate">{{ 'asset.extendField.fieldValue' | translate }}</label>
      <input class="col-sm-6 form-control" name="fieldValue" [(ngModel)]="extDataSource.value" />
    </div>

    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{ 'asset.extendField.extNecessary' | translate }} </label>
      <nz-select class="col-sm-6 form-control" [ngModel]="assetExtend.extNecessary" name="extNecessary" required
        #extNecessary="ngModel" (ngModelChange)="updateData($event)">
        <nz-option nzLabel="{{ 'common.yes' | translate }}" [nzValue]="true"></nz-option>
        <nz-option nzLabel="{{ 'common.no' | translate }}" [nzValue]="false"></nz-option>
      </nz-select>
      <i class="star"></i>
      <span class="error" *ngIf="
            extNecessary.errors?.required &&
            (extNecessary.touched || !ngFormValid)
          ">{{ 'asset.extendField.extNecessaryNotNull' | translate }}</span>
    </div>

    <div class="form-group row">
      <label class="col-sm-2 form-control-label">{{ 'asset.extendField.extOrder' | translate }} </label>
      <input type="number" class="col-sm-6 form-control" name="extOrder" #extOrder="ngModel"
        [(ngModel)]="assetExtend.extOrder" />
    </div>

    <div class="form-group row">
      <label for="remarks" class="col-sm-2 form-control-label">{{ 'asset.extendField.extDesc' | translate }}</label>
      <textarea class="col-sm-6 form-control" name="extDesc" [(ngModel)]="assetExtend.extDesc"
        maxlength="255"></textarea>
    </div>

    <div class="form-group row">
      <div class="offset-sm-2 operation">
        <button type="button" debounceDirective (debounceClick)="
            submit(this.submitted = true && ngForm.valid && checkComponentsArgs() && !duplicate)
            " class="btn phoenix-form-btn__primary">
          {{ 'deviceBook.common.ok' | translate }}
        </button>
        <button type="button" (click)="cancel()" class="btn phoenix-form-btn__default">
          {{ 'deviceBook.common.cancel' | translate }}
        </button>
      </div>
    </div>
  </form>
</app-phoenix-panel>