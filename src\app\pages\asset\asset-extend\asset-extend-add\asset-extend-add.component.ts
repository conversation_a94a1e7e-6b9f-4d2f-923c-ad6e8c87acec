
import { forkJoin, Observable } from 'rxjs';
import { Component, OnInit, Injector } from '@angular/core';
import { BaseAddComponents } from '@app/communal/components/base-add/baseAddComponents';
import { AssetExtend } from '../asset-extend.model';
import { ExtFidldConfigurationsService } from '../extfield-configurations.service';
import * as _ from 'lodash';


// 定义扩展数据绑定表的接口
interface ExtDataSource {
  table: string;
  label: string;
  value: string;
}

@Component({
  selector: 'app-asset-extend-add',
  templateUrl: './asset-extend-add.component.html',
  styleUrls: ['./asset-extend-add.component.scss']
})
export class AssetExtendAddComponent extends BaseAddComponents implements OnInit {
  assetExtend: AssetExtend = new AssetExtend();
  duplicate = false;
  assetExtendList: any = [];
  // 扩展数据绑定表的格式 { "table": "zg_roommap", "label": "RoomName", "value": "RoomId" }
  extDataSource: ExtDataSource = {} as ExtDataSource;

  constructor(public extFidldConfigurationsService: ExtFidldConfigurationsService, injector: Injector) {
    super(injector, extFidldConfigurationsService, { baseUrl: 'extfieldconfigurations', translatePrefix: 'asset.extendField' });
    this.initTranslateMessage([]);
  }

  ngOnInit() {
    forkJoin([this.service.getAll('extfieldconfigurations?extTable=assetDevice')]).subscribe(res => {
      this.assetExtendList = res[0];
    })
  }

  updateData(value){
    this.assetExtend.extNecessary = value;
  }

  checkComponentsArgs() {
    if (this.assetExtend.extName.trim().length === 0) {
      return false;
    }
    if (!this.assetExtend) {
      return false;
    }
    return true;
  }

  checkDuplicate(event) {
    this.duplicate = false;
    if (this.assetExtendList && this.assetExtendList.length > 0) {
      _.map(this.assetExtendList, item => {
        if (event === item.extName) {
          this.duplicate = true;
        }
      })
    }
  }

  beforeSubmitAction() {
    this.assetExtend.extTable = 'assetDevice';
    // 如果扩展数据类型为表，则将扩展数据源转换为字符串
    if (this.assetExtend.extDataType === 'table') {
      this.assetExtend.extDataSource = JSON.stringify(this.extDataSource);
    }
    return this.assetExtend;
  }

}
