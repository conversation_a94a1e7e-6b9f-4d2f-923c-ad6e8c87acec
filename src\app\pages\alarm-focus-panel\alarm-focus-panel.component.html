<!-- 容器高度设置为 100%，并使内容分成上下两部分 -->
<div style="height: 100%; display: flex; flex-direction: column; gap: var(--portal-gap-size);">
  <!-- 上半部分 -->
  <div class="halfpannel">
      <div style="flex: 1; display: flex; flex-direction: column; overflow: hidden; height: 100%;">
        <div class="header-part">
          <h5 class="modal-title">{{ 'general.alarm.oneAndTwoAlarm' | translate }}</h5>
          
          <div class="d-flex align-items-center justify-content-between">
            <!-- 左侧按钮组 -->
            <div class="d-flex gap-2">
              <button class="btn btn-primary"
                [disabled]="selectedItemsOneandTwo.length === 0"
                (click)="batchConfirm(1)"
                *ngIf="this.sessionService.hasPermission(1)"
                style="width: 80px;">
                <em class="iconfont icon-confirm"></em>
                {{ 'general.common.confirm' | translate }}
              </button>
            </div>
      
            <!-- 右侧内容 -->
            <div class="d-flex align-items-center justify-content-end flex-grow-1">
              <!-- 统计 em 图标 -->
              <div class="alarm-container">
                <div *ngFor="let myitem of alarmSeveritiesOneAndTwo" class="alarm-item">
                  <em class="iconfont icon-alarmlevel1"
                    [ngStyle]="{ 'color': myitem.displayColor }"
                    title="{{myitem.severityName}}">
                  </em>
                  <span>&nbsp;{{ myitem.count }}&nbsp;&nbsp;</span>
                </div>
              </div>
              <div class="set-custom-columns">
                <div class="custom-col">
                  <a [title]="'s2.button.customColumns' | translate" style="cursor: pointer;" (click)="customColumns(1)">
                    <span class="operation-icon">
                      <i class="iconfont icon-dingzhilie" style="font-size: 16px;"></i>
                    </span>
                  </a>
                </div>
              </div>
              <!-- 搜索框 -->
              <div class="search-container">
                <i class="ion-ios-search-strong"></i>
                <input type="text" class="form-control"
                  name="searchTextOneAndTwo"
                  placeholder="{{ 'admin.role.placeholder' | translate }}"
                  [(ngModel)]="searchTextOneAndTwo"
                  (keyup.enter)="searchChangeType(1)"
                  (ngModelChange)="searchChangeType(1)" />
              </div>
            </div>
          </div>
        </div>
        
        <div id="tableFrameFocus" class="tableFrameFocus" style="flex: 1; ">
          <!-- 新的d-data-table实现 -->
          <d-data-table
            [dataSource]="oneandtwoDisplayData"
            [scrollable]="true"
            [fixHeader]="true"
            [containFixHeaderHeight]="true"
            tableHeight="100%"
            [tableWidthConfig]="oneandtwoTableWidthConfig"
            [tableOverflowType]="'overlay'"
            [resizeable]="true"
            [onlyOneColumnSort]="true"
            [borderType]="'borderless'"
            [striped]="false">
            <thead dTableHead>
              <tr dTableRow>
                <!-- <th dHeadCell>#</th> -->
                <ng-container *ngFor="let col of (oneandtwoTableWidthConfig)">
                    <th dHeadCell *ngIf="col['field'] === 'checkBox'" [resizeEnabled]="false"
                    (resizeEndEvent)="onOneAndTwoResize($event, col.field)" [sortable]="false"
                    [showSortIcon]="false" >
                        <label class="checkbox-inline custom-checkbox nowrap">
                          <input type="checkbox" [ngModel]="allSelectedOneAndTwo" (change)="changeAllSelectedOneAndTwo($event)">
                          <span></span>
                        </label>
                    </th>
                    <th dHeadCell *ngIf="col['field'] !== 'checkBox'" [resizeEnabled]="true"
                        (resizeEndEvent)="onOneAndTwoResize($event, col.field)" [sortable]="false"
                        [showSortIcon]="false" >{{ getColDisplayName(col.field) }}</th>
                </ng-container>
            </tr>
            </thead>
            <tbody dTableBody>
              <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow
                    [ngClass]="{
                      'confirmed-row': rowItem?.confirmTime,
                      'unconfirmed-row': !rowItem?.confirmTime,
                      'selected': rowItem?.selected
                    }">
                                <ng-container *ngFor="let col of (oneandtwoTableWidthConfig)">
                        <td dTableCell *ngIf="col['field'] === 'checkBox'">
                            <label class="custom-checkbox"><input type="checkbox"
                                    (change)="changeSelectedOneAndTwo($event,rowItem)" [(ngModel)]='rowItem.selected'>
                                <span></span>
                            </label>
                        </td>
                        <td dTableCell
                            *ngIf="col['field'] !== 'eventSeverity' && col['field'] !== 'checkBox' && col['field'] !== 'eventReasonType'"
                            class="ellipsis-cell" [title]="rowItem[col.field]">
                            {{ rowItem[col.field] }}</td>
                        <td dTableCell *ngIf="col['field'] === 'eventSeverity'"  class="ellipsis-cell">
                            <div class="form-inline" style="display: inline;" [title]="rowItem.eventSeverity">
                                <span>
                                    <em class="iconfont icon-alarmlevel1"
                                        [ngStyle]="{ 'color': showAlarmSeverities[rowItem.eventLevel]}"></em>&nbsp;&nbsp;{{
                                    rowItem.eventSeverity }}
                                </span>
                            </div>
                        </td>
                        <td dTableCell *ngIf="col['field'] === 'eventReasonType'"  class="ellipsis-cell">
                          <div class="form-inline" style="display: inline;" [title]="rowItem.eventReasonTypeName">
                              <span>
                                  {{rowItem.eventReasonTypeName }}
                              </span>
                          </div>
                      </td>
                    </ng-container>
                </tr>
            </ng-template>
          </tbody>
          <ng-template #noResultTemplateRef>
              <div style="text-align: center; margin-top: 20px">{{ 'general.idcDeviceMonitor.noData' | translate}}
              </div>
          </ng-template>
            
          </d-data-table>
          
        </div>
        <div *ngIf="oneandtwoSum >15" style="overflow-y:auto;min-height: 80px;">
          <phoenix-table-pager [source]="oneandtwoPagerConfig" (changePage)="onPageChange(1, $event)">
          </phoenix-table-pager>
        </div>
      </div>
  </div>

  <div class="halfpannel">
      <!-- 下半部分 -->
      <div style="flex: 1; display: flex; flex-direction: column; overflow: hidden; height: 100%;">
        <div class="header-part">
          <h5 class="modal-title">{{ 'general.alarm.threeAndFourAlarm' | translate }}</h5>
          <div class="d-flex align-items-center justify-content-between">
            <!-- 左侧按钮组 -->
            <div class="d-flex gap-2">
              <button class="btn btn-primary"
                [disabled]="selectedItemsThreeandFour.length === 0"
                (click)="batchConfirm(2)"
                *ngIf="this.sessionService.hasPermission(1)"
                style="width: 80px;">
                <em class="iconfont icon-confirm"></em>
                {{ 'general.common.confirm' | translate }}
              </button>
            </div>

            <div class="d-flex align-items-center justify-content-end flex-grow-1">
              <div class="alarm-container">
                <div *ngFor="let myitem of alarmSeveritiesThreeAndFour" class="alarm-item">
                  <em class="iconfont icon-alarmlevel1"
                    [ngStyle]="{ 'color': myitem.displayColor }"
                    title="{{myitem.severityName}}">
                  </em>
                  <span>&nbsp;{{ myitem.count }}&nbsp;&nbsp;</span>
                </div>
              </div>
              <div class="set-custom-columns">
                <div class="custom-col">
                  <a [title]="'s2.button.customColumns' | translate" style="cursor: pointer;" (click)="customColumns(2)">
                    <span class="operation-icon">
                      <i class="iconfont icon-dingzhilie" style="font-size: 16px;"></i>
                    </span>
                  </a>
                </div>
              </div>
              <div class="search-container">
                <i class="ion-ios-search-strong"></i>
                <input type="text" class="form-control"
                  name="searchTextThreeAndFour"
                  placeholder="{{ 'admin.role.placeholder' | translate }}"
                  [(ngModel)]="searchTextThreeAndFour"
                  (keyup.enter)="searchChangeType(2)"
                  (ngModelChange)="searchChangeType(2)" />
              </div>
            </div>
          </div>
        </div>  

        <div class="tableFrameFocus" style="flex: 1; margin-top: 5px;">
          <!-- 新的d-data-table实现 -->
          <d-data-table
            [dataSource]="threeandfourDisplayData"
            [scrollable]="true"
            [fixHeader]="true"
            [containFixHeaderHeight]="true"
            tableHeight="100%"
            [tableWidthConfig]="threeandfourTableWidthConfig"
            [tableOverflowType]="'overlay'"
            [resizeable]="true"
            [onlyOneColumnSort]="true"
            [borderType]="'borderless'"
            [striped]="false">
            <thead dTableHead>
              <tr dTableRow>
                <!-- <th dHeadCell>#</th> -->
                <ng-container *ngFor="let col of (threeandfourTableWidthConfig)">
                    <th dHeadCell *ngIf="col['field'] === 'checkBox'" [resizeEnabled]="false"
                    (resizeEndEvent)="onThreeAndFourResize($event, col.field)" [sortable]="false"
                    [showSortIcon]="false" >
                        <label class="checkbox-inline custom-checkbox nowrap">
                          <input type="checkbox" [ngModel]="allSelectedThreeAndFour" (change)="changeAllSelectedThreeAndFour($event)">
                          <span></span>
                        </label>
                    </th>
                    <th dHeadCell *ngIf="col['field'] !== 'checkBox'" [resizeEnabled]="true"
                        (resizeEndEvent)="onThreeAndFourResize($event, col.field)" [sortable]="false"
                        [showSortIcon]="false" >{{ getColDisplayName(col.field) }}</th>
                </ng-container>
            </tr>
            </thead>
            <tbody dTableBody>
              <ng-template let-rowItem="rowItem" let-rowIndex="rowIndex">
                <tr dTableRow
                    [ngClass]="{
                      'confirmed-row': rowItem?.confirmTime,
                      'unconfirmed-row': !rowItem?.confirmTime,
                      'selected': rowItem?.selected
                    }">
                                <ng-container *ngFor="let col of (threeandfourTableWidthConfig)">
                        <td dTableCell *ngIf="col['field'] === 'checkBox'">
                            <label class="custom-checkbox"><input type="checkbox"
                                    (change)="changeSelectedThreeAndFour($event,rowItem)" [(ngModel)]='rowItem.selected'>
                                <span></span>
                            </label>
                        </td>
                        <td dTableCell
                            *ngIf="col['field'] !== 'eventSeverity' && col['field'] !== 'checkBox' && col['field'] !== 'eventReasonType'"
                            class="ellipsis-cell" [title]="rowItem[col.field]">
                            {{ rowItem[col.field] }}</td>
                        <td dTableCell *ngIf="col['field'] === 'eventSeverity'"  class="ellipsis-cell">
                            <div class="form-inline" style="display: inline;" [title]="rowItem.eventSeverity">
                                <span>
                                    <em class="iconfont icon-alarmlevel1"
                                        [ngStyle]="{ 'color': showAlarmSeverities[rowItem.eventLevel]}"></em>&nbsp;&nbsp;{{
                                    rowItem.eventSeverity }}
                                </span>
                            </div>
                        </td>
                        <td dTableCell *ngIf="col['field'] === 'eventReasonType'"  class="ellipsis-cell">
                          <div class="form-inline" style="display: inline;" [title]="rowItem.eventReasonTypeName">
                              <span>
                                  {{rowItem.eventReasonTypeName }}
                              </span>
                          </div>
                      </td>
                    </ng-container>
                </tr>
            </ng-template>
            </tbody>
            <ng-template #noResultTemplateRef>
              <div style="text-align: center; margin-top: 20px">{{ 'general.idcDeviceMonitor.noData' | translate}}
              </div>
          </ng-template>
          </d-data-table>
          
        </div>
        <div *ngIf="threeandfourSum >15" style="overflow-y:visible;min-height: 80px; z-index: 1055 !important;">
          <phoenix-table-pager [source]="threeandfourPagerConfig" (changePage)="onPageChange(2, $event)">
          </phoenix-table-pager>
        </div>
      </div>
  </div>
</div>
