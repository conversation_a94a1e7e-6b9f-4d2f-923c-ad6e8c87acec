{"general": {"home": "Home", "connectError": "The server is not connected!", "reconnectSever": "Reconnecting to server......", "noPermission": "You do not have permission to access the page", "sessionTimeout": "Session Timeout Please login again", "backPage": "Back Page", "count": "Count", "toplogy": {"online": "Online", "offline": "Offline(1.Current node is offline 2.Children has node is offline)", "masked": "Masking", "expandAll": "expandAll", "collapseAll": "collapseAll"}, "file": {"extNameNoSame": "The file content does not match the extension"}, "status": {"alarm": "Alarm", "normal": "Normal"}, "switchpage": {"add": "Customize", "addfromhmi": "From HMI", "addfrommenu": "From Menu", "setting": "Settings", "span": "<PERSON><PERSON> Span (second)", "starting": "Switching...", "stoping": "Stoped", "prev": "Prev", "next": "Next", "start": "Start Switch", "stop": "Stop Switch", "quad": "Settings", "topLeft": "topleft", "topRight": "topright", "bottomLeft": "bottomleft", "bottomRight": "bottomright"}, "time": {"hour": "hour", "minute": "minute", "second": "second", "day": "date", "date": "date", "month": "Month", "week": "Week", "year": "Year", "everyday": "every day", "everyyear": "every year", "everyweek": "every week", "everymonth": "every month", "everyhour": "every hour", "everymin": "every minute", "everySec": "every second", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday"}, "cron": {"cronExpressionSetting": "Cron Expression Setting", "custom": "Custom", "timeExpression": "Time Expression", "crontabFullExpression": "Cron Tab Full Expression", "secondTips": "Be careful when configuring the second-level trigger strategy, as it may affect the system.", "dateStart": "th, execute every", "numDays": "days", "hourStart": "o'clock, execute every", "numHours": "hours", "minStart": "th minute, execute every", "numMins": "minutes", "monthStart": "th month, execute every", "numMonths": "months", "secondStart": "th second, execute every", "numSeconds": "seconds", "yearStart": "year, execute every", "numYears": "years", "lastDayOfTheMonth": "last day of the month", "lastWeekOfTheMonth": "last", "nearestWorking": "the nearest working day after the", "eachMonth": "th of each month", "multipleSelect": "multiple select", "from": "from the", "cycleFromWeek": "", "cycleFrom": "", "ofEveryWeek": "th(Monday is 1,Sunday is 7) of every week", "ofEveryMonth": "th of every month", "ofEveryDay": "o'clock of every day", "ofEveryHour": "th minute of every hour", "ofEveryYear": "th month of every year", "ofEveryMinute": "th second of every minute", "week": "th week", "ofTheWeek": "th(Monday is 1,Sunday is 7) of the", "th": "", "ofTheMonth": "th(Monday is 1,Sunday is 7) of the month", "year": "Year", "last5Runs": "Last 5 Running Time", "calculatingResult": "Calculating result...", "noResult": "No result meeting the condition!", "only": "Latest 100 years, only", "results": "results！", "leaveBlank": "leave blank", "include": "specify", "exclude": "not specify", "expression": "Expression", "confirm": "Confirm", "reset": "Reset", "cancel": "Cancel", "second": "Second", "minute": "Minute", "hour": "Hour", "day": "Day", "week2": "Week", "month": "Month"}, "dashboard": {"alarmPosition": "Alarm Position ", "alarmLevelSatic": "Alarm Severity Statistics", "oneLevelAlarm": "Critical", "secondLevelAlarm": "Major", "threeLevelAlarm": "Minor", "fourLevelAlarm": "Observation", "allAlarm": "All", "onlineRate": "Device Online Rate", "noAlarm": "No Alarm", "maskedAlarm": "Alarm Masked", "onLine": "Online", "offLine": "Offline", "unregister": "Unregister", "alarmTrend": "Alarm Trend", "realTime": "Real Time", "usersByCountry": "Users by Country", "revenue": "Revenue", "feed": "Feed", "toDoList": "To Do List", "calendar": "Calendar", "newVisits": "New Visits", "purchases": "Purchases", "activeUsers": "Active Users", "returned": "Returned"}, "forms": {"option1": "Option 1"}, "menu": {"syncMenuItem": "Synchronize permission point to menu", "ttsconfig": "TTS Setting", "dashboard": "Dashboard", "activeAlarm": "Active Alarm", "editors": "Editors", "ck_editor": "CKEditor", "components": "Components", "tree_view": "Tree View", "charts": "Charts", "chartist_js": "Chartist.Js", "ui_entry": "UI entry", "typography": "Typography", "buttons": "Buttons", "icons": "Icons", "modals": "Modals", "grid": "Grid", "form_elements": "Form Elements", "form_inputs": "Form Inputs", "form_cores": "Form cores", "tables": "Tables", "basic_tables": "Basic Tables", "smart_tables": "Smart Tables", "maps": "Maps", "google_maps": "Google Maps", "leaflet_maps": "Leaflet Maps", "bubble_maps": "Bubble Maps", "line_maps": "Line Maps", "pages": "Pages", "login": "<PERSON><PERSON>", "register": "Register", "menu_level_1": "Menu Level 1", "menu_level_1_1": "Menu Level 1.1", "menu_level_1_2": "Menu Level 1.2", "menu_level_1_2_1": "Menu Level 1.2.1", "external_link": "External Link", "alarm": "Alarm List", "home": "Home", "misc": "Misc", "monitor": "Monitor", "device": "Device Search", "deviceDetail": "<PERSON><PERSON>", "backstage": "Backstage", "team": "Team", "time": "Time", "sysconfig": "Sysconfig", "shiftchange": "Shift Change", "shiftchangeReport": "Shift Change Report", "emergency": "Emergency", "servicesRegister": "Services Register", "report-entry": "Maintenance Analysis", "cmdb": "Configuration Management", "themesetting": "Set theme", "userTheme": "User Theme", "aliassetting": "<PERSON>", "humanList": "Organizations List", "hierarchy": "Hierarchy", "baseInformation": "Base Info", "updatePassword": "Update Pwd", "forgetPassword": "Forget Pwd", "currentPwd": "Current Pwd"}, "alarm": {"width": "<PERSON><PERSON><PERSON>", "alarmDetails": "Alarm Detail", "alarmDevice3dLocation": "Alarm Device 3D Location", "createAlarmWorkOrder": "Create Alarm Work Order", "alarmStartEndSnapshot": "See Alarm Start End Snapshot", "cameraName": "Camera Name", "evnetType": "Evnet Type", "pageNumber": "Img Number", "expertAdvice": "Expert Advice", "first": "First Page", "end": "Last Page", "prePage": "Previous Page", "nextPage": "Next Page", "perPageCount": "Per Page", "filterResult": "Total Filterd ", "lock": "Lock", "unlock": "UnLock", "filterSetting": "Filter", "customFilter": "Custom Condition", "title": "Alarm List", "coreEventName": "Alarm Name", "severityName": "Severity", "comment": "Comment", "coreSourceName": "Alarm Source", "deviceCategory": "Category", "corePointName": "Alarm Name", "occurValue": "Occur Values", "endValue": "End Values", "eventReasonTypeName": "End Reason", "occureRemark": "Remark", "birthTime": "Birth Time", "startTimeFrom": "Birth Time later than", "startTimeTo": "Birth Time Earlier than", "clearTime": "Clear Time", "meanings": "Meanings", "clearedByName": "Clear User", "confirmTime": "Confirm Time", "roomDetail": "Room Detail", "allConfirmed": "The selected alarms have all been confirmd", "hasConfirmed": "Some of the selected alarms have been confirmed or the current user does not have permission to confirm", "hasNoPermissionMask": "Some of the selected alarms are not blocked by the current user", "hasEnded": "Some of the selected alarms have ended", "confirmerName": "Confirm User", "confirmComment": "Confirm Remark", "deviceName": "Device Name", "devicePosition": "Position", "moreAlarmRecord": "More Alarm Record", "remarkCount": "Remark Count", "noPopInThisLogin": "UnPopup", "PopInThisLogin": "Popup", "unconfirmedAlarm": "All Unconfirmed Alarm", "unfinishedAlarm": "All Unfinished Alarm", "desc": " Content", "batch": "<PERSON><PERSON> ", "close": "Close", "remark": "Remark", "alarmCount": " Alarms", "opUser": "Operation User", "inAll1": "Total ", "inAll2": " Alarms", "inAllPage": "Page", "inAll3": "Records", "opLog": "Operation Logs", "last7Reord": "History Recent 7 Days", "moreAlarm": "More Alarm", "alarmLevel": "Alarm Level", "alarmBaseTypeName": "Alarm Basetype", "theSelected": " the Selected", "confirmAll": "Confirm All", "alarmOccurValue": "Occur Value", "alarmSource": "Alarm Source", "reason": "Reason", "mask": "Mask ", "setLockSpan": "Lock Span Setting", "lblLockSpan": "Unit (second)", "unlockPage": "Unlock Page", "lockPage": "Lock Page", "noImg": "No Image", "oneAndTwoAlarm": "Critical & Major", "threeAndFourAlarm": "Minor & Observation", "enableSuccess": "Enable Success", "disabledSuccess": "Disabled Success"}, "battery": {"batteryModel": "Battery Model", "batteryString": "Battery String", "batteryStringOverview": "Battery String Overview", "factry": "Factory", "type": "Type", "batteryCellModelId": "Battery Cell Model ID", "deviceId": "Device ID", "deviceName": "Device Name", "batteryStringId": "Battery String ID", "vendor": "<PERSON><PERSON><PERSON>", "model": "Model", "voltageType": "Voltage Type", "ratedVoltage": "Rated voltage(V)", "ratedCapacity": "Rated Capacity(Ah)", "initialIR": "Initial IR(μΩ)", "floatChargeVoltage": "Float Charge Voltage(V)", "evenChargeVoltage": "Even Charge Voltage(V)", "tempCompensationFactor": "Temperature Compensation Coefficient(mV)", "terminationVoltage": "Termination Voltage(V)", "chargingCurrentLimitFactor": "Charging Current Limiting Coefficient(CA)", "dischargeCurrentLimitFactor": "Discharge Current Limit <PERSON>efficient(CA)", "length": "Length(mm)", "width": "Width(mm)", "height": "Height(mm)", "weight": "Weight(Kg)", "vendorNotNull": "<PERSON><PERSON><PERSON> can't be null", "modelNotNull": "Model can't be null", "ratedVoltageNotNull": "Rated Voltage can't be null", "ratedCapacityNotNull": "Rated Capacity can't be null", "initialIRNotNull": "Initial IR can't be null", "floatChargeVoltageNotNull": "Float Charge Voltage can't be null", "evenChargeVoltageNotNull": "Even Charge Voltage can't be null", "tempCompensationFactorNotNull": "Temperature Compensation Coefficient can't be null", "terminationVoltageNotNull": "Termination Voltage can't be null", "chargingCurrentLimitFactorNotNull": "Charging Current Limiting Coefficient can't be null", "dischargeCurrentLimitFactorNotNull": "Discharge Current Limit Coefficient can't be null", "standbyPowerNotNull": "Standby Time can't be null", "startUsingTimeNotNull": "Start Using Time can't be null", "cellCountNotNull": "Cell Count can't be null", "maxChargingCurrentNotNull": "Max Charging Current can't be null", "currentTransformerTypeNotNull": "Current Transformer Type can't be null", "batteryStringNameNotNull": "Battery String Name can't be null", "deviceNotNull": "Associated Devices can't be null", "batteryModelNotNull": "Associated Device Model can't be null", "lengthNotNull": "Length can't be null", "widthNotNull": "Width can't be null", "heightNotNull": "Height can't be null", "weightNotNull": "Weight can't be null", "ratedCapacityInvalid": "Rated Capacity Invalid", "batteryStringName": "Battery String Name", "standbyPower": "Standby Time(min)", "timeLimitNotEmpty": "Standby Time can't be null", "timeLimitType": "Standby Time can only be a number", "timeLimitMax": "Standby Time can't more than 9999 mins", "timeLimitMin": "Standby Time can't less than 1 mins", "cellCountLimitMax": "Cell Count can't more than 999", "cellCountLimitMin": "Cell Count can't less than 1", "startUsingTime": "Start Using Time", "cellCount": "Cell Count", "currentTransformerType": "Current Transformer Type", "maxChargingCurrent": "Max Charging Current(C)", "maxFloatChargeVoltage": "Max Float Charge Voltage(V)", "device": "Associated Device", "alarm": "Alarm", "battery": "Battery", "lessThanOneYear": "Less than 1 year", "one2ThreeYear": "1~3 year", "three2FiveYear": "3~5 year", "moreThanFiveYear": "More than 5 years", "usagePeriod": "Usage Period", "noAlarm": "No Alarm", "hasAlarm": "Has <PERSON>", "communicationOutage": "Communication Outage", "doubleOutofRangeValue": "Input number can't more than 999999.99", "invalidInput": "Invalid Input"}, "batteryStringDash": {"maxRelatedDeviceNum": "The number of related devices can't over"}, "batteryCell": {"realData": "Real Date", "performCompare": "Perform Compare", "realDischarge": "Real Discharge Curv", "hisdischarge": "History Discharge Curv", "alarms": "Current Alarms", "controls": "Control List", "batteryCellOverview": "Battery OverView", "vendorModel": "Vender/Model", "totalVoltage": "Total Voltage(V)", "totalCurrent": "Total Current(A)", "temperature": "Ambient Temperature(℃)", "maxMinTemp": "Temp <PERSON>/Min(℃)", "humidity": "Ambient Humidity", "averageVoltage": "Average Voltage(V)", "maxMinVoltage": "Voltage Max/Min(V)", "averageIR": "Average IR(μΩ)", "maxMinIR": "IR Max/Min(μΩ)", "batteryCellCount": "Cell Count", "realTimeSignal": {"title": "Realtime Signal", "batteryCellId": "ID", "batteryCellName": "Name", "cellTemp": "Cell Template", "cellTempStatus": "Temp Status", "cellVoltage": "Cell Voltage", "cellVoltageStatus": "Voltage Status", "cellIR": "Cell IR", "stopVoltage": "Discharge cut-off voltage(V):", "stopCapacity": "Discharge cut-off capacity(AH):", "dischargeSpan": "Discharge time(Min):", "cellIRStatus": "IR Status"}, "performanceContrast": {"title": "Performance", "voltage": "Voltage", "temperature": "Temp", "IR": "IR", "cell": "Cell", "emptyTips": "No Data！"}, "dischargesupervision": {"title": "Discharge Test", "curve": "Curve", "triggerconditions": "<PERSON><PERSON> Conditions", "startdischarge": "Discharge", "enddischarge": "End", "dischargetime": "Span", "dischargevoltage": "Discharge Voltage", "dischargeCurrent": "Discharge Current", "dischargecapacity": "Discharged Capacity", "cellTemperature": "Cell Temperature", "cellVoltage": "Cell Voltage", "dischargevoltageexceed": "Discharge Voltage Exceed", "dischargecapacityexceed": "Discharge Capacity Exceed", "dischargetimeexceed": "Discharge Time Exceed"}, "historycurve": {"title": "History Curve", "totalvoltagecurve": "总体放电曲线", "cellvoltagecurve": "单体放电曲线", "startDischargeTime": "Start Time", "endDischargeTime": "End Time", "quickSelect": "Quick Select", "cellvoltage": "Cell Voltage", "dischargehistoryreport": "历史数据报表.xlsx", "dischargestatus": "充/放电状态：", "discharge": "Discharge", "constrasttime": "对比时间段：", "totalVoltage": "电池组总电压(V)", "totalCurrent": "电池组充放电电流(A)", "remainCapacityRate": "电池组剩余容量百分比", "cellName": "#Cell", "voltage": "Voltage", "temp": "Temp", "excelName": "蓄电池历史放电数据", "sheetName": "放电数据", "reportName": "放电报表.xlsx"}}, "deviceSearch": {"deviceName": "Device Name", "devicePosition": "Device Position", "deviceSearch": "Search", "alarmSeverity": "Alarm Severity", "deviceSearchResult": "Search Results", "deviceResultName": "Name", "deviceResultBuilding": "Building", "deviceResultFloor": "Floor", "deviceResultRoom": "Room"}, "devicedetail": {"deviceMask": "<PERSON>", "deviceDiseases": "Device Diseases", "triggerValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "diagnoseTime": "DiagnoseTime", "itemName": "DiseaseDesc", "healthIndex": "Health Index", "subhealthIndex": "Sub-health Index", "description": "Description", "idx": "NO.", "exportName": "DataOfSignal&Alarm"}, "pointsList": {"alarmLevel": "Alarm Level", "pointName": "Name", "pointValue": "Value", "pointStatus": "Status", "pointOperation": "Action", "statusStartedTip": "Alarm Started", "statusNoStartTip": "Normal", "statusEndedTip": "Alarm Ended", "statusNoEndTip": "Not Finished", "statusConfirmdTip": "Alarm Confirmed", "statysNoConfirmTip": "Alarm Unconfirmed", "opSetValueTip": "Set Value", "opMaskTip": "Mask Alarm", "opCancelMaskTip": "UnMask Alarm", "opForceEndTip": "Force End Alarm", "opConfirmTip": "Confirm Alarm", "points": "Live Points", "commandSet": "Setup ", "commandValue": "Value", "commandCancel": "Cancel", "commandSend": "OK", "pleaseInput": "Please Input Value for Setup"}, "login": {"noMenu": "The user has not been configured with menu permissions and cannot enter the system. Please contact the administrator to configure it correctly.", "title": "Sign in to SiteWeb", "usrname": "User", "pwd": "Password", "toggle": "changes?", "register": "New to SiteWeb? Sign up!", "forgotpwd": "Forgot password?", "btn_login": "Sign in", "zh": "中文", "en": "EN", "welcome": "Welcome", "errInfo": "Wrong user name or password or verification code", "accountExpired": "The login user has expired, please switch user to log in again.", "accountDisabled": "Login user is not available, please switch user to log in again", "accountLocked": "Login user is locked, please switch user to log in again", "accountOverError": "The current user password has been incorrectly entered too many times, please try again in 3 minutes", "overMaxOnlineUsers": "The number of currently allowed login users is full, please try again later", "passwordExpired": "The password has expired, please change the password and log in again", "freezeAccount_1": "User account is freeze and it will be unfreezed after ", "freezeAccount_2": "seconds", "userNotFound": "Username does not exist", "wrongPassword": "Wrong username or password.", "wrongPassword2": "Total number of attempts: ", "wrongAccessTime": "This account can't login for current time!", "wrongIPAccessTime": "This IP can't login for current time!", "telephone": "Phone", "telephonePlaceholder": "Phone number", "picCode": "Image", "picCodePlaceholder": "Type the characters in the image", "messageCode": "Code", "messageCodePlaceholder": "Enter code", "touristLogin": "tourist Login", "forgetPassword": "Forget Password", "passwordNotInValidTime": "password Not In Valid Time, pls contact administrator to reset pwd", "accountIvalidTimeoneMonth": "The account is valid for less than one month. If you need to continue using it, please contact the administrator as soon as possible to extend the validity period.", "pwdLogin": "Password login", "PhoneLogin": "Password-free login", "getImgCode": "send code", "resend": "resend", "resetPassword": "forget password?", "firstLogin": "Change password for first login!", "usePwdLogin": "Login with account & pwd", "useSMScode": "Login with SMS verification code", "second": "second", "title2": "Sign in to SiteWeb", "noPhoneError": "The user has no mobile!", "securityFileIntegrityFail": "System integrity failure, please contact the administrator!", "forcehack": "Please don't brute force login!", "imageCodeError": "The image code is wrong!", "describe": "Describe", "registerDescribe": "Please enter the applicant's headquarters and other information!", "attachment": "Attachment Upload", "attachmentPlaceholder": "Please select attachment files", "attachmentInfo": "Support image, Word, Excel files, single file not exceeding 1MB"}, "resetPassword": {"title": "reset Password", "newPassword": "New pw", "confirmPassword": "Confirm", "passwordLess": "short", "passwordInconsistent": "inconsistent", "resetFail": "update error", "resetSuccess": "update success", "low": "low", "middle": "middle", "high": "high"}, "logout": {"title": "Log out", "changeLang": "Change zh/en"}, "systemAdmin": "Sys-Admin", "common": {"fontsize": "Font Size", "all": "all", "noData": "There are no eligible data", "notData": "There are no eligible data", "action": "Action", "ok": "OK", "cancel": "Cancel", "add": "Add", "edit": "Edit", "error": "Error", "confirmDelete": "Please confirm whether to delete: ", "confirmDelete2": "Please confirm whether to delete?", "delete": "Delete", "succeed": " Success", "failed": " Failed", "downloadFailure": "download attachment failure", "select": "Select", "clear": "Clear", "upload": "Upload", "uploadAll": "Upload All", "download": "Download", "progress": "Progress", "startTime": "Start Time", "endTime": "End Time", "find": "Search", "findAll": "Display ALL", "resetQuery": "Reset Query", "searchFilter": "Please enter the name to query", "save": "Save", "saveSuccess": "Save Success", "saveFailed": "Save Failed", "export": "Export", "exportAll": "Export All", "addTemplate": "AddTemplate", "templateList": "TemplateList", "exportIng": "Exporting......", "exportFailed": "Export Failed", "exportZipSuccess": "Export Success", "import": "Import", "cardCopy": "Card Copy", "cardLost": "Card Lost", "status": "Status", "selectIcon": "Select Icon", "confirm": "Confirm", "end": "End", "close": "close", "refresh": "Refresh", "switchTo": "Switch to", "horizontal": "Horizontal", "vertical": "Vertical", "batchProcessing": "Batch Confirmation", "deleteCantNot": "Can't delete", "yes": "Yes", "no": "No", "aboutAccountName": "   And delete related accounts: ", "tip": "prompt", "commit": "Commit", "reject": "Reject", "commitSuccess": "Commit Success", "startTimeNotNull": "Start Time Not Null", "endTimeNotNull": "End Time Not Null", "endTimeNotValid": "The end time must be greater than the start time", "uploadImage": "Upload Image", "uploadFailure": "Failed to upload attachment", "downloadAll": "Download All", "downloadSuccess": "Download Success", "reason": "Reason", "time": "Time", "comments": "opinion", "maxLength128": "Maximum length is 128", "modify": "Modify", "goBack": "Back", "editCantNot": "Can't edit", "detail": "Detail", "rank": "Rack equipment", "deleted": "Deleted", "deleteSuccess": "Delete Success", "reset": "Reset", "sort": "Sort", "day": "Day", "hour": "Hour", "minute": "Minute", "second": "Second", "exitEdit": "Exit Edit", "lineNumber": "Serial number", "comingSoon": "Coming soon, so stay tuned", "noInformation": "No basic information is currently included", "ascendingText": "Ascending", "descendingText": "Descending", "photograph": "photograph", "openCameraFailed": "Failed to open the camera, please check whether the camera is connected properly!", "backList": "Back to List", "itEquipmentList": "IT Devices", "buildConfiguration": "Build Configuration", "uploadCnfig": "Upload config", "initSampler": "Init Sampler", "allUpload": "Upload all", "oneUpload": "Upload current", "allDelete": "Delete all", "oneDelete": "Delete current", "deleteConfig": "Delete config", "filterError": "<PERSON><PERSON>", "bind": "Bind", "unbind": "Unbind", "description": "Description", "columnExportTips": "The number of columns exceeds Excel limit, please use horizontal export!", "notLicense3D": "3DView is not authorized", "exporting": "Exporting, please wait...", "alarmfittingTips1": "The alarm convergence occurs at ", "alarmfittingTips2": ", and the convergence event is ", "alarmfittingTips3": ", view alarm convergence page?", "clearfilter": "Clear filters", "pleaseInput": "Please input", "pleaseSelect": "Please select", "keywords": "Keywords", "total": "Total", "items": "items", "noMore": "No more", "forward": "Forward", "backward": "Backward"}, "selfprocotocol": {"ipnotempty": "ip cannot empty", "ipformarterror": "ip format error", "portnotempty": "port cannot empty", "portformarterror": "port format error", "addrnotempty": "address cannot empty", "addrformarterror": "address format error", "dllnotempty": "dll cannot empty", "intervalnotempty": "interval cannot empty", "intervalformarterror": "interval format error", "cannotupload": "cannot upload repeat", "noerrorrow": "no erro rows", "uploadsuccess": "upload success", "buildconfigerror1": "cannot excute option,there are errors in select items", "buildconfigerror2": "cannot excute option,should upload config file", "sametemplate": "have get same template", "configexsit": "have genrate config", "notchecked": "pls check item", "initsuccess": "Init success", "noupload": "pls upload config", "deletesuccess": "delete success", "defaulttemplatecannotdelete": "default template cannot delete", "readpropertynotempty": "wr property cannot empty", "readpropertyformaterror": "wr property format error", "edit": "config", "deleteedit": "delete config"}, "drivertemplate": {"name": "Template name", "description": "Description", "templatedrivertype": "Templatedriver type", "isDefaultTemplate": "Isdefault", "placeholder": "Pls enter key words", "intputName": "Input name", "intputDescription": "Input description", "editTree": "Edit tree", "isDisk": "IsDisk", "addString": "Add float", "path": "Path", "inputpath": "Input path", "NotNull": "Value not empty", "templateName": "Template Name", "inputtemplateName": "Inputtemplate Name", "isUpload": "IsUpload", "Upload": "Upload", "UploadCondition": "Upload Condition", "UploadWhenCreate": "Upload When Create", "UploadWhenGenrate": "Upload When Genrate", "isFill": "IsFill", "treeStruct": "TreeStruct", "addRoot": "AddRoot", "toBack": "ToBack", "folder": "<PERSON><PERSON><PERSON><PERSON>"}, "mask": {"batchMaskAlarm": "Batch Suppress Alarm", "maskManagement": "Suppression Management", "deviceMaskList": "Suppressed Equipment", "alarmMaskList": "Suppressed Alarm", "deviceBulkblock": "<PERSON><PERSON> Suppress", "blockAll": "Suppress All", "maskMorethan": "Selected suppression exceeds 1000, please reduce the range", "deviceId": "EquipmentID", "deviceName": "Equipment Name", "deviceLocation": "Equipment Position", "userId": "Suppression User Id", "userName": "Suppression User", "comment": "Suppression Reason", "operationTime": "Setting Time", "startTime": "Suppression Start Time", "endTime": "Suppression End Time", "confirmDeleteDeviceMask": "Confirm Delete Suppression of Equipment: ", "confirmDeleteAllDeviceMask": "Confirm Delete Suppression of All Equipment", "commentNotEmpty": "Suppression Reason Can Not Empty", "corePointName": "Alarm Name", "confirmDeleteAlarmMask": "Confirm Delete Suppression of Alarm: ", "confirmDeleteAllAlarmMask": "Confirm Delete Suppression of All Alarm", "masking": "Suppression Status", "actived": "Actived Status", "confirmBulkblock": "Confirm Batch Suppress Alarm", "confirmBulkblock2": "Confirm Batch Suppress Equipment", "confirmUnblock": "Confirm Unsuppress Suppression", "confirmUnblockAll": "Confirm Suppress All Equipment", "confirmUnblockBatch": "Confirm Unsuppress Batch Suppression of ", "confirmDeleteAll_1": "Confirm Unsuppress All Suppression of ", "confirmDeleteAll_2": "", "confirmDeleteAll_3": "", "confirmDeleteAll_2_1": "Confirm Unblock All Suppression", "confirmDeleteAll_3_1": "Confirm Unblock Selected Suppression", "mask": "Suppress", "unblock": "Unsuppress", "UnblockBatch": "Unsuppress", "UnblockAll": "Unsuppress All", "blockByConfig": "Suppress by Config", "timeJudge": "Start time cannot be later than end time", "timeNotEmpty": "Please fill in the Suppression Span", "ismasked": "yes", "isnotmasked": "no", "isActived": "yes", "isnotActived": "no", "operation": "Action", "resName": "Structure Name", "eqName": "Equipment Name", "eventID": "Event ID", "eventName": "Event Name", "setMaskPeriod": "Set Suppression Period", "maskTimePeriod": "Suppression Time Period", "set": "Set", "notSelectMask": "Please select suppression object first!", "editParams": "<PERSON>", "jumpTo": "Jump to Page"}, "dateOptions": {"January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "Su": "Sun", "Mo": "Mon", "Tu": "<PERSON><PERSON>", "We": "Wed", "Th": "<PERSON>hu", "Fr": "<PERSON><PERSON>", "Sa": "Sat"}, "scene": {"sceneName": "Scene Name", "status": "Status", "subSceneName": "Sub-Scene Name", "operation": "Action", "currentScene": "Current Scene", "useSubScene": "Use this sub-scene"}, "roleGraphicPage": {"roleName": "Role Name", "graphicPageName": "Graphic Page Name", "editRoleGraphicPage": "Edit Role Graphic Page", "delRoleGraphicPage": "Delete Role Graphic Page?"}, "userGraphicPage": {"userName": "User Name", "graphicPageName": "Graphic Page Name", "departmentName": "Department Name", "editUserGraphicPage": "Edit User Graphic Page", "delUserGraphicPage": "Delete User Graphic Page?"}, "airConditionerAiGroup": {"aiGroupTitle": "AI air-conditioner group", "aiOverheatingTitle": "AI overheating area", "aiGroupName": "AI Group Name", "aiGroupId": "AI Group ID", "description": "description", "aiOverheatingAreaName": "AI Overheating Area", "aiOverheatingAreaId": "Area ID", "overheatingDescription": "description", "inputEmpty": "empty", "alreadyExist": "already exist", "selectEmpty": "empty", "deleteCode412": "this ai group has related to overheating areas, delete failed", "relatedAiGroup": "Related AI group", "alreadyRelatedAiGroup": "Already related AI group", "confirmSelect": "select confirm", "confirmSelectAiGroup": "this will change the AI group of this airconditioner and its points, please confirm", "notSelectAiGroup": "Please select AI group first"}, "idcDeviceMonitor": {"relatedEventConfig": "Related Event Config", "signalConfig": "Signal Config", "signalNotRelateEvent": "The Signal Is Not Associated With An Event", "signalBatchApply": "Signal Batch Set", "alarmConditionApplyInEquip": "Alarm Set In Device", "alarmConditionBatchApplyAcrossEquip": "Alarm Set Across Device", "applyItem": "Apply Item", "selectedSignal": "Selected Signal", "deviceToBeApplied": "Device To Be Applied", "selectAllDevice": "Select All Device", "onlyShowSelectedDevice": "Only Show Selected Device", "apply": "Apply", "signalName": "Signal Name", "storagePeriod": "Storage Period(s)", "threshold": "<PERSON><PERSON><PERSON><PERSON>", "absoluteThreshold": "Absolute Threshold", "percentageThreshold": "Percentage Threshold", "statisticalPeriod": "Statistical Period(h)", "equipmentPosition": "Device Position", "pleaseSelectSignal": "Please Select Signal", "pleaseSelectApplyItem": "Please Select Item To Apply ", "pleaseSelectApplyDevice": "Please Select Device To Apply", "batchApplySuccess": "Batch Apply Succeed", "batchApplyFail": "Batch Apply Fail", "coverEventCondition": "Cover the conditions of the origin event with the selected conditions", "applyMethod": "Apply Method", "alarmRule": "Alarm Rule", "signalListToSelect": "Signal List To Select", "selectAllSignal": "Select All Signals", "onlyShowSelectedSignal": "Only Show Selected Signals", "pleaseSelectApplyCondtion": "Please Select Condition To Apply", "selectedSignalApplySuccess": "Selected Signals Apply Successfully!", "selectedSignalAllNotRelateEvent": "Selected Signals All Not Related Event! The Conditions Can't Apply", "partOfSignalApply": "The signals above not related event and can't apply, the others apply Successfully.", "alarmLevelNotFound": "Alarm Level Not Found: ", "noAlarmLevel": "No Alarm Level", "canNotConfigBatteryOrHvc": "Large batches of templates for battery or hvc are at risk for dynamic configuration. The system has been set to off. If necessary, please contact the administrator to turn on the switch in the system settings.", "noDynamicConfigPermission": "No dynamic configuration permission", "online": "Online", "offline": "Offline", "pleaseEnterStartTime": "Please Enter Start Time", "pleaseEnterEndTime": "Please Enter End Time", "pleaseEnterReason": "Please Enter Reason", "stationOnProjectCannotMask": "The station is on project, can't set mask", "stationOnMaskCannotProject": "The station is on mask, can't set project", "stationAction": "Operation", "stationConnectState": "Connect State", "visible": "Is it visible", "isEnable": "Enable", "stationDevice": "Station Device", "returnToTree": "Return to device tree", "historySignal": "History Signal", "signalDate": "Signal Date", "signalValue": "Signal Value", "nameExist": "Name Exist", "noData": "No Data", "allSignal": "All", "aSignal": "Analog", "dSignal": "Digital", "showPrecision": "Dispaly Accuracy", "staticsPeriod": "Statistic Period", "absThreshold": "Absolute <PERSON><PERSON><PERSON>", "startExpression": "Start", "suppressExpression": "Suppress", "canOnlyDragEquipNode": "Can Only Drag Equipment Node", "canOnlyDropOnSameParentNode": "Can Only Drop On Same Parent Node", "noCurrentSignalValue": "No Current Signal Value"}, "virtualEquipment": {"virtualEquipment": "Virtual Equipment", "virtualSignal": "Virtual Signal", "instanceSignal": "Instance Signal", "virtualEquipmentName": "Virtual Equipment Name", "virtualStationName": "Virtual Station", "virtualHouseName": "Virtual House", "virtualMonitorUnit": "Virtual Monitor Unit", "virtualEquipmentTemplate": "Virtual Equipment Template", "virtualEquipmentType": "Virtual Equipment Type", "virtualPortName": "virtual Port Name", "equipmentInfo": "Equipment Info", "batchAdd": "Batch Import", "export": "Export Selected Config", "exportAll": "Export All", "virtualSignalName": "Virtual Signal Name", "originalEquipmentName": "Original Equipment Name", "originalSignalName": "Original Signal Name", "originalChannelNo": "Original Channel No", "virtualSamplerUnitName": "Virtual Sampler Unit Name", "succeedOrNot": "Succeed Or Not", "feedback": "<PERSON><PERSON><PERSON>", "importErrotMsg": "Import failed, do you want to download the error message?", "equipmentList": "Equipment List", "signalList": "Signal List", "relatedEventList": "Related Event List", "virtualEquipPreview": "Virtual Equipment Preview", "signalName": "Signal Name", "eventName": "Event Name", "equipmentName": "Equipment Name", "houseName": "Room Name", "selectedSignalNum": "Selected Signal Count", "selectedEventNum": "Selected Event Count", "removeConfigAll": "Remove Config All", "createConfig": "Create Config", "pleaseEnterVitualEqName": "Please Enter Name", "pleaseSelectMonitor": "Please Select", "pleaseSelectEqType": "Please Select", "virtualComPort": "Virtual Com Port", "pleaseSelectComPort": "Please Select", "pleaseSelectConfig": "Please Select Config", "createVirtualEqSuccess": "Create vitual equipment succeeded, and need to create and deliver config manually", "createVirtualEqNameExist": "A device with the same name already exists in the system, please change the name and try again", "saveSelectConfig": "Save Selected Config", "removeSelectConfig": "Remove Selected Config", "virtualEqNameExist": "Name already exists", "confrimRemoveAllConfig": "Are you sure to remove all config?"}, "about": {"officialVersion": "Official Version", "trialVersion": "Trial Version", "featureList": "Feature List", "expireDate": "Expire Date", "updateLicense": "Update License", "update": "Update", "actived": "Actived", "inactivated": "Inactivated", "exportC2Vfile": "Export C2V file", "importV2Cfile": "Import V2C file", "select": "Select", "uploadV2Ctips": "Please upload .V2C file", "clientName": "Client Name", "contractNo": "Contract No", "contractNonotnull": "Contract No can't be null!", "uploadSuccess": "Upload Success", "uploadFail": "Upload Fail", "uploadFail2": "Upload failed, please check file format", "noLicense": "This function is not activated. Please update the License first.", "licenseExpiringSoon": "The license is about to expire, please update the license in time.", "licenseExpired": "The license has expired, please update the license in time.", "licenseError": "The license is abnormal, please update the license in time."}, "alarmVideoLink": {"linkName": "alarmVideoLinkName", "linkDescription": "alarmVideoLinkDescription", "linkType": "Link Type", "snapshotCount": "Snapshot Count", "snapshotInterval": "Snapshot Interval（ms）", "videoPopupWindow": "Video Popup Window", "snapshot": "Snapshot", "alarmStatus": "Alarm Status", "alarmStart": "Alarm Start", "alarmEnd": "Alarm End", "notificationStatus": "alarmVideoLinknotificationStatus", "nameNotNull": "alarmVideoLinknameNotNull", "linkRule": "alarmVideoLinkRule", "addAlarmTriggerRule": "addAlarmTriggerRule", "setCamera": "setAlarmAndCamera", "deviceBaseTypeName": "deviceBaseTypeName", "close": "close", "noMoreReminder": "noMore<PERSON><PERSON><PERSON>", "resumeReminder": "resume<PERSON><PERSON><PERSON>", "closeAllPreviews": "closeAllPreviews", "equimentList": "equimentList", "alarmList": "alarmList", "cameraList": "cameraList", "alarmVideoLinkConfigTemplate": "alarm Video Link Config Template", "importConfig": "import Config", "exportConfig": "export Config", "alarmBaseTypeName": "alarmBaseTypeName"}, "shiftManagement": {"shiftGroupId": "Shift Group Id", "shiftName": "Shift Name", "shiftStartTime": "Shift Start Time", "shiftEndTime": "Shift End Time", "shiftPeriod": "Shift Period", "shiftColor": "Shift Color", "shiftGroupName": "Group Name", "shiftGroupDescrip": "Group Descrip", "addShiftGroup": "Add Shift Group", "editShiftGroup": "Edit Shift Group", "shiftGroupList": "Shift Group List", "shiftPersonlist": "Shift Person List", "num": "Number", "shiftGroupPerson": "Group Person", "shiftGroupNameShort": "Group", "shiftGroupPersonShort": "Person", "shiftNameShort": "Shift", "department": "Department", "role": "Role", "phone": "Phone", "sequence": "Sequence", "moveUp": "Move Up", "moveDown": "Move Down", "scheduleDate": "Schedule Date", "chooseShift": "<PERSON><PERSON>", "editSchedule": "Edit Schedule", "addSchedule": "Add Schedule", "delSchedule": "Del <PERSON>", "chooseSevenDays": "Choose <PERSON>", "choose7Days": "Choose 7 Days", "previousWeek": "Previous Week", "addShiftGroupPerson": "Add Shift Group Person", "groupNumberEmpty": "Group Member Empty", "startTime": "Start Time", "nextWeek": "Next Week"}, "chartDataSeparateConfig": {"chartStyleName": "Style Name", "chartStyleOptionJson": "Style Option Json", "chartStyleThumbnail": "Style Thumbnail", "chartType": "Type", "codeEdit": "Code Edit", "nameNotNull": "nameNotNull", "chartCustomThemeName": "Custom Theme Name", "echartsThemeName": "Echarts Theme Name", "chartDefaultTheme": "Default Theme", "chartApiName": "Api Name", "chartApiCategory": "Api Category", "chartApiUrl": "Api URL", "chartApiMethod": "Request Method", "chartApiParamSchema": "<PERSON><PERSON> <PERSON>", "chartApiParamIn": "<PERSON><PERSON>", "chartApiTransformFunction": "Api Transform Function", "addParam": "Add Param", "fieldName": "Field Name", "fieldMeaning": "Field Meaning", "paramArray": "Param Format Array", "paramRequired": "Param Required", "defaultValue": "Default Value", "paramSource": "Param Source", "input": "Input", "router": "Router", "dataDictionary": "Data Dictionary", "component": "Component", "systemConfig": "System Config", "sessionName": "SessionStorage Name", "paramComponent": "Param Component", "paramDataDictionary": "ParamData Dictionary", "basicSignalSelectorComponent": "Signal Selector Component", "treeSelectorComponent": "Tree Selector Component", "indicatorSelectorComponent": "Indicator Selector Component", "timeRangeComponent": "Time Range Component", "hierarchySelectorComponent": "Hierarchy Selector Component", "selectHierarchy": "Select Hierarchy", "paramSetting": "Parameter Setting", "addChildParam": "Add child param", "routerName": "Router Name", "chartThemeJson": "Echarts Theme Json"}, "tts": {"commonConfig": "common Config", "alarmConfig": "alarm Config", "preAlarmConfig": "preAlarm Config", "broadcastConfirmedPreAlarm": "if broadcast Confirmed PreAlarm", "ttsPreAlarmEnable": "TTS PreAlarm Enable", "strategyName": "strategy Name", "strategyType": "strategy Type", "alarm": "alarm", "preAlarm": "preAlarm", "internalMessage": "internal Message", "strategyNameNotNull": "strategy Name NotNull", "description": "description", "enable": "enable", "effectiveStartTime": "effective Start Time", "positive": "filter", "negative": "negate", "effectiveEndTime": "effective EndT ime"}, "alarmNotification": {"Signal": "Signal", "playbackOrder": "playbackOrder", "playbackOrder1": "<PERSON>larm,2Prealarm,3Internal Message", "playbackOrder2": "1Alarm,3Internal Message,2Prealarm", "playbackOrder3": "2<PERSON><PERSON>arm,<PERSON>larm,3Internal Message", "playbackOrder4": "2Prealarm,3Internal Message,1Alarm", "playbackOrder5": "3Internal Message,1Alarm,2Prealarm", "playbackOrder6": "3Internal Message,2Prealarm,1Alarm", "ComplexIndex": "Complex Index", "AlarmCount": "Alarm Count", "selectIndicator": "Select Indicator", "selectSignal": "Select Signal", "safeMsgName": "Safe SMS Name", "safeMsgDescription": "Safe SMS Description", "safeMsgTemplate": "Content Template", "safeMsgField": "Template Field", "safeMsgTemplateNotNull": "SafeMsg Template Not Null", "safeMsgNameNotNull": "SafeMsg Name Not Null", "sendTime": "Sending Time", "sms": "sms", "voice": "voice", "email": "email", "receiveMode": "Receive Mode", "receiver": "Receiver", "sendCycle": "Sending Cycle", "signalAliasName": "Signal Alias Name", "indicatorAliasName": "Indicator <PERSON><PERSON> Name", "bindMax": "bindMax", "templateLimit": "Tips: If the template is more than 60 words, it will be divided into 2 messages to be sent.", "safeMsgUsedStatusNotDelete": "SafeMsg Used Status Not Delete", "tTSSeverity": "Associated Alarm Levels", "tTSAlarmStatus": "Associated Alarm Status", "tTSMsgURL": "New Message Notify Service URL", "tTSCycleMsgURL": "Message Loop Playback Service URL", "ttsSpeakTimes": "Repeat Broadcast Times", "ttsSpeakRate": "TTS Speak Rate", "ttsSpeakRateRange": "Max 10，Min 0.1，Step 0.1", "ttsSpeakVolumeRange": "Max 1，Min 0，Step 0.1", "sortByEventLevel": "TTS Speak Sort By Event Level", "ttsSpeakVolume": "TTS Speak Volume", "firstPushAlarm": "No Alarm Broadcast After Login", "alarmField": "Alarm Field", "alarmContentTemplate": "Content Template for Alarm Start", "alarmEndContentTemplate": "Content Template for Alarm End", "alarmContentTemplateRemark": "Tips: You can edit the notification content template here. The attributes in {} will be replaced with corresponding event fields, please do not modify at will", "alarmPolicyName": "Event Notification Policy Name", "alarmPolicyNode": "Notification Policy Node", "alarmPolicyNameNotNull": "Event Notification Policy Name Can't be Null", "description": "Description", "notificationTemplate": "Notification Template", "notificationTemplateNotNull": "Notification Template Can't be Null", "usedStatusNotDelete": "The policy is currently in use and cannot be deleted.", "notificationStatus": "Enable", "eventStart": "Event Start", "eventEnd": "Event End", "eventDelay": "Event Delay(s)", "filterConditions": "Filter Conditions", "hasSet": "Has been set", "set": "Set", "view": "View", "alarmNotificationPolicyFilter": "Alarm Notification Policy Filter", "ttsFilter": "TTS Filter", "notificationProcess": "Notification Process", "gateWayList": "Gateway List", "sNotificationPolicyProcess": " 's Notification Policy Process", "save": "save", "back": "back", "edit": "edit", "closeEditMode": "Close Edit Mode", "callandmessage": "Call Up & Sent Message", "onlyCallUp": "Only call up", "onlySentMessage": "Only sent message", "name": "Name", "title": "Title", "jobNumber": "Job No.", "mustSelect": "You must select one or more people to send", "mustSelectPersonOrShiftGroup": "You must select one or more people/shiftgroup to send", "selectSentPerson": "Select Send Person", "selectSentPersonOrShiftGroup": "Select Send Person/shiftgroup", "onlyShowSelected": "Only Show Selected", "onlyShowSelectedShiftGroup": "Only Show Selected", "sendPolicy": "Send Policy", "timeoutSetting": "Timeout Setting", "timeout": "Timeout(s)", "selectPerson": "select Person", "selectShiftGroup": "select ShiftGroup", "filterFirst": "Please select filter condition fisrt", "cyclicBroadcastActiveAlarm": "Cyclic Broadcast Active Alarms", "notBroadcastConfirmedAlarm": "Not Broadcast Confirmed Alarms", "notBroadcastEndAlarm": "Not Broadcast End Alarms", "alarmEnable": "Enable TTS Speech", "alarmMsg": "Alarm Msg", "prealarmMsg": "Prealarm Msg", "systemMsg": "System Msg", "id": "Index", "content": "Content", "emptyQueue": "Empty Queue", "emptyQueueConfirm": "Empty Queue Confirm?", "emptyQueueSuccess": "Empty QueueS uccess", "ttsQueue": "TTS Queue"}, "gatewayList": {"title": "Gateway Service List", "save": "Save", "cancel": "Cancel", "delete": "Delete", "gatewayType": "Gateway Type", "gatewayURL": "Gateway URL", "description": "Description", "name": "Name", "add": "Add", "useAdd": "New Gateway", "useEdit": "Existing Gateway", "confirmDelete": "Confirm to delete?", "saveError": "Please complete the parameters first!"}, "project": {"startTime": "Start Time", "endTime": "End Time", "batchSet": "Batch Set", "batchRelease": "Batch Release", "allRelease": "Release All", "setProject": "Set Project", "noTime": "Please enter a complete time period!", "updateError": "Update error!", "confiltc": "Devices Update Fail", "reason": "Reason", "confirmDelete": "Confirm to delete?"}}, "report": {"common": {"catagoryName": "Report Type Name", "catagoryDesc": "Report Type Description", "catagoryOrder": "Display Order", "customReport": "Custom Report", "timerReport": "Timer Report", "reportInformation": "Report Information", "reportName": "Report Name", "reportDescription": "Report Description", "isPublic": "Public", "queryParameters": "Query Parameters", "QueryParameterDisplay": "Query Parameter Display", "outputParameterDisplay": "Output Parameter Display", "ReportNameRequired": "Report name is required", "grouptNameRequired": "Group Name is required", "reportEntryPrompt": "Required parameter, cannot be empty", "queryParamRequried": "Please complete the query parameters", "templateFile": "Template File", "templateFileRequired": "Template file is required", "configFile": "Config File", "configFileRequired": "Config file is required", "chart": "Chart", "time": "time", "value": "value", "download": "Download", "csv": "View CSV File", "display": "Display", "hide": "<PERSON>de", "saveConfig": "Save Parameter Configuration", "addGroup": "Add Group", "editGroup": "Edit Group", "groupName": "Group Name", "parentGroup": "Parent Group", "editHierarchy": "Edit Hierarchy", "addTemplate": "Add Template", "editTemplate": "Edit Template", "reportCatagory": "Report Catagory", "reportCatagoryRequired": "Report Catagory cannot be empty"}, "timingTaskReport": {"taskName": "Task Name", "reportTemplate": "Report Template", "storageCycle": "Storage Cycle", "startTime": "Start Time", "endTime": "End Time", "reportTaskName": "Task Name", "reportTemplateType": "Template Type", "report": "Report", "recipient": "Recipient", "recipientTip": "Multiple recipients should be separated by commas", "cc": "Cc", "ccTip": "Multiple cc's should be separated by commas"}, "historyDataReport": {"historyData": "History Data Period", "device": "Equipment", "corepoint": "Corepoint", "corepointValue": "Corepoint Value", "acquisitionTime": "Sample Time", "historyDataTrend": "History Data Trend", "resultTitle": "Search Result List", "historyDataTips": "Only can select most 10 Equipment", "submitStr": "Confirm", "close": "Close", "selectDevice": "Please select Equipment", "selectCorepoint": "Please select core point", "backupInfo": "Has selected {{date}}", "room": "Room", "basicType": "Equipment Type", "excelFileName": "History Data Curve Report.xlsx", "selectPoint": "Select Core Point", "selectCorePoint": "Selected Core Point", "deleteAll": "Delete All", "delectConfirm": "Confirm to delete all"}}, "maintainMange": {"daily-inspect": {"title": "Daily Inspection"}}, "maintain": {"faultHandlingTask": {"eventSeveritySetting": "Alarm Level & Work Order Delay", "delayTime": "Work Order Delay", "eventSeveritySaveHint": "The system will dispatch the alarm work order according to the selected alarm level and work order delay", "responsiblitySetting": "Duty Group Associated to Device Type", "availableDeviceCategoriesNotSelected": "Havn't select optional device type", "unavailableDeviceCategoriesNotSelected": "Havn't select selected device type", "addAailableDeviceCategoriesSuccess": "Add optional device type success", "addAailableDeviceCategoriesError": "Add optional device type fail", "removeUnavailableDeviceCategoriesSuccess": "Delete selected device type success", "removeUnavailableDeviceCategoriesError": "Delete selected device type fail", "availableDeviceCategories": "Optional Device Type", "unavailableDeviceCategories": "Selected Device Type", "notifyRule": "Work Order Notification Rules", "timeoutRule": "Work Order Timeout Rules", "ruleId": "Rule ID", "deviceBaseTypeName": "Device Basic Type", "alarmBaseTypeName": "Alarm Basic Type", "groupLeaderTimeout": "Timeout period to notify the group leader(min)", "executiveTimeout": "Timeout period to notify the executive(min)", "managerTimeout": "Timeout period to notify the group manager(min)"}}, "workflow": {"common": {"desktop": "Desktop", "users": "Users", "groups": "groups", "agree": "Agree", "disagree": "Disagree", "approver": "Approver", "approvedTime": "Approved Time", "currentNode": "currentNode", "approvedResult": "Result", "remark": "Remark"}, "tasks": "Task List", "processes": "Process List", "instances": "Instances", "taskid": "TaskId", "history": {"title": "Seach Condition", "startTime": "Fill Time  From", "to": "To", "reset": "Reset", "search": "Search", "searchResult": "Search Result"}, "user": {"title": "Users", "cardtitle": "Information", "placeholder": "UserID Or UserName", "userId": "UserId", "userName": "UserName", "userEmail": "Email", "action": "Action"}, "group": {"title": "Groups", "placehoder": "GroupName", "membersTitle": "Group Members"}}, "verification": {"taskSummation": {"completion": "完成情况", "completionMaintenance": "完成维护", "expireDate": "剩余天数", "totalDevice": "<PERSON><PERSON>", "onlineCount": "Online Counts", "equipmentClass": "设备类", "maintenancePersonal": "维护人员"}, "verification": {"title": "设备维护管理", "it": "IT", "elec": "配电", "havc": "暖通", "roomNumber": "房间号", "assetNumber": "资产编号", "assetName": "资产名称", "deviceInspectDetail": "设备巡检详情", "jobCompletion": "岗位完成情况", "personalCompletion": "人员完成情况", "deviceCompletion": "设备完成情况", "monthlyMaintenanceReport": "月度维护报告", "unmaintainedReport": "未维护报告"}}, "profile": {"title": "Profile"}, "deviceProbe": {"common": {"normal": "The equipment is operating normally!"}}, "emergency": {"relatedPerson": {"title": "Related Person"}, "baseInfo": {"title": "Emergency", "malfunction_1": "双路市电断电", "happenTime": "Time", "eventType": "Type", "eventLevel": "Level", "eventReason": "Reason", "lessTime": "Time Left"}, "realTime": {"title": "Real-time Data", "impactDevices": "Affected Device", "impactBrackets": "Affected Rack", "impactUsers": "Affected Users"}, "interaction": {"robot": "Robot", "plan": "Plan"}, "material": {"warehouse": {"title": "Warehouse Management", "placeholder": "Please enter key words", "warehouseName": "Warehouse Name", "warehouseType": "Warehouse Type", "location": "Location", "belong": "Belong", "remark": "Remark", "warehouseNameNotEmpty": "Warehouse Name is required"}}, "materialItem": {"title": "Material Item Management", "placeholder": "Please enter key words", "materialItemName": "Material Item Name", "specifications": "Specification", "materialItemCategory": "Material Item Category", "equipmentCode": "Equipment Code", "assetCode": "Asset Code", "unit": "Unit", "materialItemNameNotEmpty": "Material Item Name is required", "specificationsNotEmpty": "Specification is required"}}, "selectResource": {"checkResource": "Select Resources", "resourceType": "Resource Type", "resourceLabel": "Resource Tag", "submit": "Submit", "cancel": "Cancel", "number": "id", "resourceName": "Resource Name", "monitoring": "Monitoring equipment", "upDevice": "Equipment on the shelf", "assetName": "Asset Name", "preserveDevice": "Maintenance equipment"}, "misc": {"logo": {"logoText": "System Name", "logoTextNotNull": "Text cannot be empty", "logoImg": "System Logo", "backgroundImg": "System Background", "websiteTabText": "Site Label Name", "websiteTabIco": "Site Label Icon(16*16.icon)"}, "pageconfig": {"add": "Add", "key": "Key", "value": "Value", "addConfig": "Add New Parameter", "save": "Save All", "addError": "Key or value can't be null!", "addSuccess": "Add success", "removeSuccess": "Delete success", "operate": "Action", "editConfig": "Edit Parameter", "isBoolean": "Boolean", "yes": "yes", "no": "no", "saveSuccess": "Save success", "saveError": "Save fail", "loginParam": "<PERSON><PERSON>", "ssoParam": "SSO", "sysParam": "System", "alarmOpParam": "Alarm", "othersParam": "Others"}}, "collection": {"cameras": {"name": "Name", "cameraIndexCode": "Camera Unique Id", "show": "Check", "ip": "IP Address", "port": "The Port Number", "cameraGroupName": "Group Name", "userName": "User Name", "password": "Password", "deviceName": "device name", "vendorName": "Manufacturer", "model": "Model", "channelNumber": "Channel Number", "back": "playback", "updateTime": "update time", "description": "Description", "nameNotNull": "Name is required!", "ipNotNull": "IP address is required!", "ipNotCorrect": "IP address format is incorrect!", "portNotNull": "port number is required!", "portNotCorrect": "Port number range: 0-65535", "userNameNotNull": "Username is required!", "passwordNotNull": "Password is required!", "channelNumberNotNull": "channel number is required!", "channelNumberNotCorrect": "Channel numbers are numbers!", "cameraIndexCodeNotNull": "Camera Unique Id is required!", "cameraGroupNameNotNull": "Camera group is required!", "vendorNameNotNull": "Manufacturer is required!", "tips": "group ID associated with this camera does not exist!", "excelImportSuccess": "excel import successful", "excelImportError": "excel import error", "templateError": "excel template error", "ImportFromExcel": "Import from Excel", "associateCamera": "Associate a camera", "selectCamera": "Select camera", "cameraTemplate": "CameraTemplate"}, "cameraGroups": {"cameraGroupId": "group number", "cameraGroupName": "Group Name", "parentGroupName": "Superior group", "description": "describe", "cameraGroupNameNotNull": "Camera group name is required!", "cameraGroupNotAllowDelete": " group has sub groups attached and cannot be deleted！"}, "cameraPollGroups": {"cameraPollGroupId": "Patrol group number", "cameraPollGroupName": "Patrol group name", "pollInterval": "Patrol interval (seconds)", "description": "describe", "cameraGroupNameNotNull": "Patrol group name is required!", "ip": "ip address", "channelNumber": "channel number", "PatrolCamera": "Patrol camera", "addCamera": "Add a camera", "cameraName": "Camera Name", "addcamerasToCurrent": "Please add cameras to the current patrol group"}}, "admin": {"common": {"ssoAccount": "SSO Acount", "ssoAccountS6Login": "SSO Acount is allowed to log in directly to this system", "role": "Role", "duty": "Duty", "onDutyResponsibility": "Duty Group", "add": "Add", "edit": "Edit", "name": "Name", "description": "Description", "namePlaceholder": "Please enter name", "selectAll": "select all", "deleteAll": "delete all"}, "organization": {"departmentList": "Department List", "id": "Organization Id", "name": "Name", "caption": "Caption", "description": "Description", "role": "Roles Management", "parentName": "Upper", "addOrganization": "Add Department", "editOrganization": "Edit Department", "addOrganizationSuccess": "Successfully Added", "editOrganizationSuccess": "Successfully Edit!", "deleteOrganizationSuccess": "Successfully Delete!", "notEmpty": "Required", "action": "Action", "del": "Delete", "personName": "Person Name", "print": "Print", "firstDepart": "The Top-level Organization"}, "person": {"aliasCheck": "Enable Status", "mobileTooltip": "contact way two（moile） is not 11 numbers, please confirm?", "alias": "<PERSON><PERSON>", "postAddress": "PostAddr", "mobile": "Contact way two", "account": "Account", "department": "Department", "male": "Male", "female": "Female", "leader": "leader", "staff": "staff", "employeeTitle": "Employee Title", "list": "Person List", "encryption": "Encryption", "closeEncryption": "Cancel Encryption", "personId": "Person Id", "patternStrOrNum": "Please enter numbers, letters or underscores", "organizationId": "Organization Id", "organizationName": "Organization Name", "name": "Name", "genderName": "Gender", "sortIndex": "Sort Index", "title": "Title", "employeeId": "Emp ID", "avatar": "Photo", "phone": "Contact way one", "phoneNumber": "phone Number", "routerConfig": "Create a person successfully. Create an associated account immediately?", "mail": "E-mail", "dutyId": "Duty Id", "dutyName": "Duty Name", "placeholder": "Please enter key to search", "action": "Action", "confirmDelete": "Please confirm whether to delete: ", "confirmDeleteBatch": "Please confirm whether to delete record?", "confirmDeleteAccount": " ,and delete associated account: ", "addPerson": "Add Person", "editPerson": "Edit Person", "nameNotNull": "Required", "genderNotNull": "Required", "organizationNotNull": "Required", "positionNotNull": "Required", "phoneNotNull": "Required", "phoneInvalid": "Invalid Phone Number", "emailInvalid": "Invalid E-mail address", "updatePersonSuccess": "Update person {0} success!", "updatePersonFailed": "Update person {0} fail. Reason: ", "addPersonSuccess": "Add person {0} success!", "addPersonFailed": "Add person {0} fail. Reason: ", "exists": "The person exists", "selectImage": "<PERSON>ose Photo", "uploadImage": "Upload Photo", "invalidFormat": "Invalid Photo Format", "uploadSuccess": "Upload photo success", "uploadFail": "Upload photo fail", "minLength": "Number cannot be less than 2 digits", "acountTitle": "Account Management", "addAccount": "Add Account", "editAccount": "Edit Account", "accountId": "Account Id", "personName": "Person Name", "acountName": "Name", "password": "Password", "confirmPassword": "ConfirmPW", "passwordTip": "Password", "accountFilter": "Enter account name to filter...", "maxError": "MaxErrorNum", "count": "Count(s)", "validTime": "Valid Time", "description": "Description", "passwordUpdateTime": "UPD Time", "roleId": "Role(s)", "roles": "Belong Role(s)", "confirmDeleteUser": "Please confirm whether to delete account: ", "updateAccountSuccess": "Update Account {0} success!", "updateAccountError": "Update Account {0} fail. Reason: ", "addAccountSuccess": "Add Account {0} success!", "addAccountError": "Add Account {0} fail. Reason: ", "nameNotEmpty": "Account name cannnot be empty", "personNotEmpty": "Person cannot be empty", "imgCodeNotEmpty": "imgCodeNotEmpty", "messageCodeNotEmpty": "messageCodeNotEmpty", "getImgCode": "get Img Code", "imgCodeLessThan": "img Code not Less Than 4 digits", "maxErrorNotEmpty": "Max error counts cannot be empty", "validTimeNotEmpty": "Required", "roleNotEmpty": "Required", "notEmpty": "Required", "nameNotLessThan": "Account name cannot be less than 2 character", "passwordNotEmpty": "Required", "passwordNotLessThan": "Password cannot be less than 8 character", "passwordIsInconsistent": "Re-enter password is incorrected", "passwordIsConsistent": "Re-enter pwd is the same as Current pwd", "alreadyCreated": "This account has been created", "accountMaxErrorSection": "Enter interger which less than 5 digits", "show": "ShowA/C", "hidden": "HideA/C", "createAccount": "CreateA/C", "updateAccount": "UpdateA/C", "departmentPerson": "Person", "defaultMenu": "<PERSON><PERSON><PERSON>", "passwordcheck": "Doesn't meet the complexity requirements", "code": "Verification Code", "getMessageCode": "Get Verification Code", "resend": "Resend", "messageCodePrompt": "Message Verification Code", "sendError": "Failed to send the verification code", "codeError": "Incorrected verification code", "low": "low", "middle": "middle", "high": "high", "passwordRules": "The password rules are as follows: Contains at least one capital letter, one lowercase letter, one number and one special character; The password must contain at least 8 characters.", "showPassword": "Show Pwd", "usePwd": "Login with Account Pwd", "useCode": "Login with SMS Code", "selectDepartment": "Select Department", "selectedDepartment": "Selected Department", "validTimeLimited": "Expiration", "validTimeUnlimited": "Unlimited", "export": "Do you want to remove sensitive words before exporting?", "exportType": "Export Type"}, "role": {"title": "Roles Management", "placeholder": "Please enter search keywords", "roleId": "Role ID", "name": "Name", "description": "Description", "action": "Action", "confirmDelete": "Please confirm whether to delete role: ", "confirmDeleteBatch": "Please confirm whether to delete record?", "addRole": "Add Role", "updateRole": "Update Role", "nameNotNull": "Role name cannot be null", "updateRoleSuccess": "Update role {0} success!", "updateRoleFailed": "Update role {0} fail, reason: ", "addRoleSuccess": "Add role {0} success!", "addRoleFailed": "Add role {0} fail, reason: ", "permissionCategory": "Permission Category", "permission": "Permission", "permissionNotEmpty": "Must to select permission", "allSelect": "Select All", "cancelAllSelect": "Cancel Select"}, "permission": {"permissionId": "Permission Id", "permissionName": "Permission Name", "permissionNameNotNull": "Permission name cannot be null", "categoryNameNotNull": "Category name cannot be null", "permissionCategory": "Permission Category", "permissionCategoryName": "Permission Category Name", "returnToPermission": "Return To Permission", "description": "Description", "placeholder": "Please enter search keywords", "caption": "Label", "categoryId": "Category Id", "categoryName": "Category Name", "confirmDelete": "Please confirm whether to delete permission: ", "action": "Action", "noDataMessage": "No data message", "deleteSuccess": "Delete success", "updatePermissionSuccess": "Update permission {item} success", "updatePermissionFailure": "Update permission {item} fail", "insertPermissionSuccess": "Insert permission {item} success", "insertPermissionFailure": "Insert permission {item} fail", "permissionNameValid": "Only Chinese characters, English letters and underscores are allowed;", "permissionCategoryRequired": "Permission Category Name is required;"}, "account": {"remote": "Remote", "lock": "Locked", "enable": "Enable", "pwdrequire": "Length at least 8 bits, upper and lower case letters, Numbers, special characters at least 1 bit per class", "error1": "Parameter verification failure!", "error2": "The account ID does not exist!", "error3": "Old password is incorrect!", "error4": "The new password is the same as the historical password!", "success": "Change success!", "newpassword": "New Pwd", "confirmPassword": "Confirm Pwd", "passwordNotEmpty": "Pwd can't be empty", "passwordNotLessThan": "Pwd can't be less than 8 characters", "passwordIsError": "Input pwd error", "passwordIsInconsistent": "Re-enter password is incorrected", "passwordIsConsistent": "The new pwd is the same as the old one", "password": "password", "passwordcheck": "The pwd doesn't meet the complexity requirements", "meetRequirements": "meet requirements", "phoneFormatError": "Invalid phone number format, please enter a valid 11-digit phone number"}, "areaPermissions": {"operation": "Action", "add": "Add", "edit": "Edit", "name": "Name", "description": "Description", "areaGroup": "Regional Group", "area": "Region", "selectGroupFirst": "Please select group first", "areaGroupAdd": "Area group add", "areaGroupEdit": "Area group edit", "getAreaDataFail": "Get area data failed", "namePlaceholder": "Please enter name", "canNotEditDefault": "Can not edit default", "canNotDeleteDefault": "Can not delete default", "onlyShowSelected": "Only show selected", "nodeParent": "position", "nodeLeaf": "device"}, "operationPermissions": {"operation": "Action", "selectGroupFirst": "Please select group first", "operationGroup": "Operation Group", "operationGroupAdd": "Operation group add", "operationGroupEdit": "Operation group edit", "getOperationDataFail": "Get operation data failed"}, "menuPermissions": {"menu": "<PERSON><PERSON>", "selectGroupFirst": "Please select group first", "menuGroup": "Menu Group", "menuGroupAdd": "Menu group add", "menuGroupEdit": "Menu group edit", "getMenuDataFail": "Get menu data failed"}, "roleAuthorization": {"role": "Role", "description": "Description", "authorization": "Authorization", "canNotEditDefault": "Can not edit default", "canNotDeleteDefault": "Can not delete default", "selectRoleFirst": "Please select role first", "menu": "<PERSON><PERSON>", "canNotEditSysRole": "Can not edit system manager", "canNotDeleteSysRole": "Can not delete system manager", "operate": "Action"}, "accountManagement": {"account": "Account", "logonId": "Account Name", "userName": "User Name", "enableStatus": "Enable Status", "maxError": "Maximum Error", "locked": "Locked", "validTime": "Account <PERSON><PERSON>", "passwordValidTime": "Password Valid Time", "resetPassword": "Reset Password", "unbindPhone": "Unbind Phone", "unbindPhoneConfirm": "Please Confirm Unbind The phone,bind time【{{1}}】bind ID【{{0}}】?", "unbindPhoneNone": "the account【{{0}}】has not binded a phone", "unbindPhoneSuccess": "unbind the phone success", "confirmResetPassword": "Please confirm whether to reset the password：{{0}}", "newPassword": "The reset password is：{{0}}", "success": "Operation Successful!", "canNotDeleteCurrentAccount": "Cannot delete the current account!", "canNotDeleteAdminAccount": "Cannot delete the admin account!", "canNotDeleteAdmin": "Cannot delete the admin!", "canNotDeleteCurrentAccountPerson": "Cannot delete the current person!", "employeeHasCardDeleteConfirm": "The person is bind to a door card, delete the person will delete the card. ", "deleteEmployeeCardSucceed": "Delete Employee Card Succeed!", "deleteEmployeeCardFail": "Delete Employee Card Fail!"}, "navigationTarget": {"placeholder": "Please enter search key", "confirmDelete": "Please confirm whether to delete the navigation:", "nameNotNull": "Navigation name is required", "linkNotNull": "Link address is required", "name": "Navigation name", "link": "link address", "iconPath": "Icon address", "visible": "Is it visible", "params": "Parameter Description", "description": "describe"}}, "ngForm": {"create": {"success": "Create success", "error": "Create error", "timeIntervalError": "Time interval greater than allowed value!", "illegalName": "Illegal Report Name!", "hour": "Hour", "noParamTips": "Tips: No output parameters selected"}, "update": {"success": "Update Success", "error": "Update Error"}, "copy": {"success": "Copy Success", "error": "<PERSON><PERSON>"}, "delete": {"timingReportError": "Please stop this report first!"}}, "equipmentManagement": {"noPermission": "No operation permission!", "cameraManagement": {"treeNode": "zone root node", "title": "Zone", "name": "Camera Name", "byArea": "By Area", "groupName": "grouping method", "station": "Station", "room": "Room", "factory": "Factory", "model": "Equipment Type", "code": "Code", "IPAddress": "IP", "location": "Installation Location", "listname": "Name", "state": "State", "operatingStatus": "Operating status", "belongLabel": "Grouping Method", "labelGroup": "Tab group", "remark": "Remark", "preview": "Preview", "realTimePreview": "live preview", "serialNumber": "serial number", "protoName": "Protocol", "configurationPreview": "Configuration preview", "parameter": "Parameter Configuration", "informationSet": "information settings", "systemSet": "System Settings", "internetSet": "Network Settings", "videoSet": "Video", "imageSet": "OSD Set Up", "unmanaged": "Please select an unmanaged camera", "endProxyName": "Proxy Terminal Name", "endProxyCode": "Proxy Terminal Encoding", "playFailed": "Play Failed!", "connectFailed": "Camera connection failed!", "passwordVerification": "Please enter the correct encoding format!", "pwdRequire": "The code consists of uppercase and lowercase letters and numbers!", "newCamera": "new camera", "delCamera": "Camera deleted", "ipCheck": "Please enter legal ip!", "delCheck": "Empty", "createTime": "Create Time", "createUser": "Create User", "total": "Total", "dataErrorMessage": "Data Error Message", "verifyErrorMessage": "Validation error message", "nvr": "NVR", "cvr": "CVR", "camera": "Camera", "rtspPort": "RTSP Port", "httpPort": "HTTP Port", "serverPort": "SDK Port", "systemSetForm": {"ntp": "NTP Timing", "hand": "Manual Timing", "equiName": "device name", "equiNumber": "equipment number", "equiModel": "Device Model", "equiSerial": "Devise Serial Number", "timeZone": "Time Zone", "serverAddress": "Server Address", "NTPPort": "NTP Port", "timingInterval": "Time Interval", "equimentTime": "Video Server Time", "setTime": "Set Time", "synchronizaComputer": "Synchronize With Computer Time", "minute": "Minute", "test": "Test"}, "internetSetForm": {"networkCardType": "NIC Type", "automatic": "Automatic Acquisition", "iPv4Address": "Device IPv4 Address", "iPv4SubnetMask": "IPv4 Subnet Mask", "iPv4DefaultGateway": "IPv4 Default Gateway", "iPv6Model": "IPv6 Mode", "iPv6Address": "Device IPv6 Address", "iPv6SubnetMask": "IPv6 Subnet Mask", "iPv6DefaultGateway": "IPv6 Default Gateway"}, "videoSetForm": {"streamType": "Stream Type", "videoType": "Video Type", "resolution": "Resolution", "bitRateType": "Bit Rate Type", "imageQuality": "Image Quality", "videoFrameRate": "Video Frame Rate", "maximumBitRate": "Bitrate Limit", "videoEncoding": "Video Encoding", "codingComplexity": "Coding Complexity", "iFrameInterval": "I-<PERSON><PERSON>", "svc": "SVC", "streamSmoothg": "Stream Smooth"}, "imageSetForm": {"showName": "Display Name", "showDate": "Show Date", "showWeek": "Show Week", "channelName": "Channel Name", "timeFormat": "Time Format", "dateFormat": "Date Format", "characterOverlay": "Character Overlay"}, "unknownError": "Unknown Mistake", "finishPlay": "End of play"}, "groupManagement": {"name": "Name", "addPeople": "Create User", "remark": "Remark", "group": "Group", "groupMethod": "grouping method", "groupMethodTree": "group root node", "addGroupMethod": "Grouping Method", "nodeName": "Node Name", "bindCamera": "Bind the Camera", "selectedCamera": "Selected Camera", "unbind": "Unbind", "confirmunbind": "Please confirm whether to unbind:"}, "nvrManagement": {"list": {"factory": "Factory", "model": "Model", "code": "Code", "IPAddress": "IP", "location": "installation location", "listname": "Device Name", "state": "State", "operatingStatus": "Operating Status", "cameraInfo": "Camera Info", "aisleInfo": "Channel Settings", "remark": "Remark", "recordPlan": "Recording Plan", "recordPlanError": "Recording schedule setup failed!", "eventSetting": "Event Settings", "motionDetection": "Motion Detection", "occlusionAlarm": "Occlusion warning", "videoLoss": "Video Loss", "regionalSetting": "Regional Settings", "armingTime": "Arming Time", "startUse": "Enable", "clearAll": "Clear All", "sensitive": "Sensitivity", "timing": "Timing", "event": "Event"}, "add": {"title": "Add NVR device", "unmanaged": "unmanaged device", "owningTerminal": "Owned Terminal", "editNvr": "modify device"}, "camera": {"bindCamera": "Bind the camera", "channelNumber": "channel number", "ipAddress": "IP channel address", "videoUrl": "video stream address", "userName": "Username", "cameraSearch": "Search", "aisle": "Channel Name", "protocol": "Protocol", "deviceIP": "Device IP", "port": "Port", "password": "Password", "equiInspection": "Equipment detection", "detect": "Detection", "testSuccessful": "The test was successful!", "deviceNumber": "device channel number", "searchDevice": "Search Device", "equipmentType": "Equipment type", "chosen": "<PERSON><PERSON>", "copyOther": "Copy To...", "aisleChoose": "Channel Selection", "cameraChoose": "Please select a camera!", "selectCamera": "Select Camera"}}, "realCamera": {"monitoringPoint": "Monitoring Points", "viewList": "View Template", "addViewTitle": "New View Template", "editViewTitle": "edit View Template", "viewName": "View Template Name", "viewNameNotNull": "View template name cannot be empty!", "resourceOccupation": "The camera control resource has been occupied!", "saveTemplate": "Save View Template", "saveTemplateAs": "Save As Template", "currentView": "Current View", "customViewport": "Custom View", "confirmSave": "Please confirm whether to submit the modification?", "standardLayout": "Standard Layout", "customLayout": "Custom Layout", "layoutSetting": "Layout Configuration", "addLayout": "New Layout", "editLayout": "Edit Layout", "layoutName": "Layout Name", "layoutSize": "Size", "layoutSplit": "Window Split", "combine": "Combine", "cancelCombine": "Cancel Combine", "closeAllPreviews": "Close All Previews", "fullVideo": "The playback window is full, please drag the camera to replace the playback window!"}, "PTZcontrol": {"PTZcontrolName": "Video Preview", "PTZ": "PTZ", "speed": "Speed", "focus": "Focus", "distance": "Sistance", "controlFailed": "Control failed!"}, "terminalManagement": {"title": "Name", "code": "Code", "location": "Location", "state": "State", "remark": "Remark", "cameraNumber": "Number of Cameras", "nvrNumber": "Number of NVR Devices", "srsApiPort": "Api port", "srsRtmpPort": "Push port", "srsWebRtcPort": "WebRtc Port", "add": {"titleNotNull": "Name is required!", "codeNotNull": "Code is required!", "numberNotNull": "Number is required!", "locationNotNull": "Location is required!", "ipNotNull": "IP is required!", "srsApiPortNotNull": "Api port is required!", "srsRtmpPortNotNull": "Push port is required!", "srsWebRtcPortNotNull": "WebRtc Port is required!"}}, "pollingGroup": {"group": "Polling Group", "groupName": "Polling Group Name", "groupNameNotNull": "Polling group name cannot be empty", "pollingInterval": "Polling Interval (s)", "pollingIntervalNotNull": "Polling interval cannot be empty", "pollingCamera": "Polling Camera", "savePollingGroupSuccess": "Save the polling group successfully", "savePollingGroupFailed": "Failed to save polling group", "byArea": "By Region", "loadError": "Loading Error", "loadErrorRetry": "Error loading, retrying...", "notFoundSrc": "source not found", "deletePollingGroupSuccess": "Deleting the polling group successfully", "deletePollingGroupFailed": "Deleting the polling group Failed"}, "eventManagement": {"serialNumber": "Serial Number", "eventSource": "Event Source", "eventType": "Event Type", "eventTime": "Event Time", "eventLevel": "Event Level", "eventDetails": "Event Details", "state": "State", "eventCamera": "Alarm Camera", "batchProcess": "Batch Processing", "delete": "Delete", "voiceOpen": "Open", "voiceClose": "Close", "stopRefresh": "Stop", "actionRefresh": "Refresh", "eventDetail": "Detail", "autoPlay": "Auto Play Img", "processrecords": "Process Records", "sendEmail": "Send Email", "process": "Process", "processTime": "Process Time", "processDetails": "Process Result", "video": "Video", "noRecords": "No Records", "chooseRecord": "Please select the alarm record first", "captrueVideo": "Obtaining video address... please wait", "refreshTime": "Set Refresh Time (m)", "rePlay": "RePlay", "noVideo": "No Video", "all": "All"}, "faceApp": {"faceComparision": "Face-Matching", "checkDetail": "check the details", "faceCompareInfo": "Face-Matching Info", "faceSnapInfo": "Face capture Info", "historySnap": "History snapshot", "snapTime": "capture time", "snapEquipment": "capture device", "employeeName": "Name", "departmentName": "organize", "email": "email", "jobNo": "Job number", "employeeTitle": "title", "phone": "phone", "mismatch": "Match failed"}, "faceManagement": {"peopleChoose": "Select Person", "deviceChoose": "Select Device", "faceSend": "Face Send", "deleteSend": "Delete", "send": "Send", "sendStatus": "Send Status", "sendTime": "Send Time", "onSend": "On Send", "offSend": "Off Send", "faceInfo": "Face Info", "errorMessage": "Error Message", "revoke": "Revoke", "revokeAll": "Revoke All", "department": "Department", "history": "History", "confirmRevoke": "Are you sure you want to cancel?", "faceSubmit": "Photo Submission"}, "logManagement": {"operation": {"title": "Operation Log", "objectType": "Operand Type", "object": "Operation Object", "name": "Operation Name", "parameter": "Operating Parameters", "result": "Operation Result", "operations": "Operator", "time": "Operating Time", "type": "Operation Type", "ip": "Operation IP", "mode": "Request Type", "oldValue": "Old Value", "newValue": "New Value"}, "service": {"title": "Review Log", "name": "Review Status", "cameraName": "Camera Name", "parameter": "Review Parameters", "result": "Review Results", "ip": "Review IP", "time": "Review Time", "mode": "Request Type", "requestor": "Reviewer", "type": "Review Type"}}, "versionService": {"versionType": "Version Type", "version": "Version Name", "dateFormat": "Date Format", "time": "Update Time"}, "equipmentLedger": {"assetModelRatio": "Asset Model Ratio", "assetModelRanking": "Asset Model Ranking", "totalEquipment": "Total Equipment", "onlineAlarmEquipment": "Online Alarm Equipment", "onlineEquipment": "Online Equipment", "offlineEquipment": "Offline Equipment", "totalPoints": "Total Points", "equipmentStatus": "Equipment Status", "pointStatistics": "Point Statistics", "index": "Index", "equipmentCategory": "Category", "equipmentCategoryName": "Equipment Type Name", "equipmentCount": "Equipment Count", "equipmentOnlineRate": "Equipment Online Rate", "pointCount": "Signal Point Count", "totalPointCount": "Total Signal Point Count", "sum": "Total", "export": "Export", "exportSummaryTable": "Export Summary Table", "exportFullPointTable": "Export Full Point Table", "manufacturerContact": "Manufacturer Contact", "manufacturerPhone": "Manufacturer Phone", "serialNumber": "Serial Number", "factoryDate": "Factory Date", "purchaseDate": "Purchase Date", "warrantyPeriod": "Warranty Period", "warrantyExpiration": "Warranty Expiration", "lastMaintenanceDate": "Last Maintenance Date", "nextMaintenanceDate": "Next Maintenance Date", "assetDepartmentName": "Asset Department Name", "assetManagerName": "Asset Manager Name", "managerPhone": "Manager Phone", "syncSuccess": "Sync successful", "syncSuccessNoNew": "Synchronization completed, no new data added", "syncSuccessWithNew": "Synchronization successful, {count} new records added", "syncFailed": "Sync failed, please contact the administrator to reset the asset extension field configuration", "confirmSync": "Confirm Sync", "syncLongTimeWarning": "This operation takes a long time. Are you sure you want to start syncing?", "syncLongTimeWarningWithOverride": "This operation takes a long time. Are you sure you want to start syncing? Some modified attributes may be overwritten.", "syncError": "An error occurred. Please try again later.", "syncDataLarge": "Due to the large amount of data, the interface execution time is long. Please refresh the page later to view the latest data.", "signalName": "Signal Name", "parameterNumber": "Parameter Number", "parameterID": "Parameter ID", "loopNo": "Loop Number", "signalPoint": "Signal Points ", "indexCount": "Index Count ", "syncFromEqu": "Sync from equipment", "unCatogory": "Uncategorized", "signalPointDetail": "Signal Point Detail", "selectEquipmentCategory": "Please select equipment category"}}, "himManage": {"template": {"settings": {"indicator-table": {"basic": "basic", "objecttype": "Object Type", "area": "Area"}, "historyCurveVal": {"latestSixHours": "latest 6 Hours", "latestOneHours": "latest 1 Hour", "latestOneDay": "latest 1 Day", "customTime": "Custom Time", "latestWeek": "latest Week", "latestMouth": "latest Mouth", "today": "Today"}}}, "cameraComponentSetForm": {"useTip": "If the page does not respond, please install the video client first!"}, "alarmDurationTimeCountChartSetForm": {"title": "Alarm Duration Analysis", "legend": {"lessThan10Mins": " <10 mins", "10to30Mins": "10-30 mins", "30to60Mins": "30-60 mins", "moreThan60Mins": ">60 mins"}}, "alarmOverviewVal": {"showAlarmStatistical": "show alarm statistical", "dataSource": "data source", "showAll": "show all", "showAccordingToSystemConfig": "according to SystemConfig", "displayDetail": "display detail", "alarmStatiscal": "alarm statiscal", "alarmDetail": "alarm list", "enable": "enable", "showMethod": "show method", "circleChart": "circle chart", "hideAlarmText": "hide alarm text", "hide": "hide"}, "alarmOverview": {"level": "level", "roomName": "room name", "alarmContext": "alarm context"}}, "resource": {"itDevice": {"property": {"itdeviceName": "IT Device Name", "serialNumber": "Serial Number", "customer": "Client", "itDeviceModel": "IT Device Model", "bunsiness": "Business", "launchDate": "Start Date", "remark": "Remark", "uindex": "U Position", "state": "State", "rackUpDown": "Rack Up Down", "DownloadTemplate": "Download template", "itdeviceTemplate": "ITDeviceTemplate", "totalSum": "Total", "IP": "IP Address"}}, "deviceModel": {"property": {"modelId": "Associated Device Model", "modelName": "Device Model Name", "deviceCategory": "Device Model", "categoryName": "Category Name", "modelType": "type", "length": "Length(m)", "width": "Width(m)", "height": "High(m)", "weight": "Weight(kg)", "ratePower": "Rated Power(kW)", "coolingCapacity": "Cooling Capacity(kJ)", "unitHeight": "U High", "faceMaterial": "Front material", "sideMaterial": "side material", "backMaterial": "Back material", "description": "describe", "selectedDeviceModel": "Selected device model", "modelNameNotNull": "Device model name cannot be empty", "deviceCategoryNotNull": "Device model cannot be empty", "ratePowerNotNull": "Rated Power cannot be empty", "rateCoolingNotNull": "Cooling Capacity cannot be empty", "rateWeightNotNull": "Weight cannot be empty", "modelTypeNotNull": "Model type cannot be null", "unitHeightNotNull": "U height cannot be empty", "unitHeightMinValue": "The minimum U height cannot be less than 1", "unitHeightMaxValue": "The maximum U height cannot be greater than 99", "invalidInput": "The input format is incorrect", "doubleOutofRangeValue": "The value entered cannot be greater than 999999.99", "factory": "Manufactor", "brand": "Brand", "capacityParameter": "Capacity parameters", "modelFile": "3D Model", "serialNumber": "serial number", "referenced": "Prompt that the device model is referenced by IT device , cannot be deleted", "select3DModel": "Select 3D Model"}, "list": {"title": "Device Model List", "deleteSuccess": "successfully deleted!", "deleteError": "failed to delete!", "searchPlaceholder": "Please enter search key"}, "add": {"title": "Add device model", "addSuccess": "Added successfully!", "addError": "add failed!"}, "edit": {"title": "edit device model", "editSuccess": "Successfully edit!", "editError": "fail to edit!"}, "common": {"modeTypeNotDelete": "The model type is already in use and cannot be deleted", "nameExist": "Name Exist", "addItem": "Add Item"}}, "rackDevice": {"property": {"selectRackDevice": "Select", "rackDeviceId": "Capacity Device ID", "rackDeviceName": "Capacity Device Name", "modelId": "Associated Device Model", "modelName": "IT Device Model", "rackId": "Associated Cabinets", "assetId": "Associated assets", "assetName": "Asset Name", "assetCode": "asset code", "unitHeight": "U High", "weight": "Weight (kg))", "ratePower": "Electricity (kW)", "coolingCapacity": "Cooling capacity (kJ)", "startRackPosition": "starting point", "launchDate": "Start Date", "purchaseDate": "Purchase Date", "client": "Client", "business": "Business", "length": "Length (m)", "width": "Width(m)", "height": "Height(m)", "state": "State", "power": "Rated power (kW)", "serialNumber": "Serial Number", "capacityParameter": "Capacity parameters", "brand": "Brand", "manufactor": "Manufactor", "model": "Model", "description": "describe", "deviceNameNotNull": "Capacity device name is not allowed to be empty", "modelIdNotNull": "Device model is not allowed to be empty", "rackDeviceNameNotNull": "Capacity device name is not allowed to be empty", "invalidInput": "The input format is incorrect", "doubleOutofRangeValue": "The value entered cannot be greater than 999999.99", "launchDateAfterPurchaseDate": "The activation time cannot be less than the purchase time", "operation": "Action", "offRackSucess": "The device was removed from the shelf successfully", "offRackFailure": "Failed to remove the device", "recommendShelfSucess": "The device is recommended to be put on the shelf successfully", "recommendShelfFailure": "Failed to recommend the device to be put on the shelf", "uindexPoistion": "U Position", "onRackName": "<PERSON>ck <PERSON>", "updateSuccess": "Update Success", "notEnoughSpace": "Selected Poisition Not Enough Space!", "updateFail": "Update Fail", "devicesFromS6": "Associate Devices with S6 Assets", "modelInfo": "Model Info", "ITDeviceInfo": "IT Device Info", "IP": "IP Address"}, "list": {"title": "List of capacity devices", "deleteSuccess": "successfully deleted!", "deleteError": " failed to delete!", "searchPlaceholder": "Please enter search key"}, "add": {"title": "Add capacity device", "addSuccess": "Added successfully!", "addError": "add failed!"}, "edit": {"title": "Modify capacity device", "editSuccess": "Successfully modified!", "editError": "fail to edit!"}}, "rackDeviceOnOffTicket": {"onoff": {"batchOffRack": "Batch Off Rack", "batchOffRackSuccess": "Batch Off Rack Success", "batchOffRackError": "Batch Off Rack Failed", "search": "Search", "controlSend": "Search control send successfully", "controlFail": "Search control send fail", "UNKNOWN_ERROR": "Unknown Error", "PARAM_ERROR": "<PERSON><PERSON>", "COMMAND_EXISTS": "Command Exists", "IN_PROJECT": "In Project", "NO_PERMISSION": "No Permission", "EQUIPMENT_OFFLINE": "Equipment Offline", "IT_DEVICE_NOT_FOUND": "IT Device Not Found", "RACK_INFO_NOT_FOUND": "<PERSON><PERSON>fo <PERSON> Found", "U_POSITION_DEVICE_NOT_FOUND": "U Position Device Not Found", "errorReason": "Device search failed. The following are the reasons for the error."}}}, "selectPoint": {"choosePoint": "Select Point", "deviceChoosePoint": "Select Device", "building": "Building", "floor": "Floor", "room": "Room", "device": "<PERSON><PERSON>", "camera": "camera", "point": "Point", "noChoose": "No Choice", "signal": "Signal", "selectSignal": "Select Signal", "selectDevice": "Select Device", "selectControl": "Select Control", "selectByEquipment": "Select By Equipment", "selectByCurrentEquipment": "Select By Current Equipment"}, "distribution": {"title": "Distribution relationship", "FromName": "from", "toName": "to", "type": "type", "excelFileName": "relationship.xlsx", "delete": "confirm whether to delete it", "addUpstreamDeviceSuccess": "adding an upstream device succeeded", "addDownstreamDeviceSuccess": "adding an downstream device device succeeded", "addRackSuccess": "adding a rack Successfully", "relationship": {"device": "device", "deviceOrRack": "device/rack", "selectDeviceAndRack": "selected device/rack", "selectRack": "select rack", "rootDevices": "root device", "deviceTree": "device tree"}, "exportError": "the rack cannot be used as an upstream device"}, "featuremanage": {"alarmconfig": {"autoconfirm": "Auto Confirm", "alarm": "Alarm", "autoconfirmCount": "Auto Confirm Threshold", "autoconfirmunit": "Count", "autoconfirmTip": "The alarms of each level which exceeds the threshold, will be automatically confirmed", "eventSeverityConfig": "Alarm Level Configuration", "eventReason": "Alarm Reason Category", "name": "Name", "description": "Description"}}, "devices": {"signalList": "Signal List", "alarmList": "Alarm List", "controlList": "Control List", "maskingAlarm": "Masking Alarm", "stateOn": "Masked", "startUpMaskingAlarm": "Mask", "cancelMaskingAlarm": "Cancel Masking", "analog": "Analog", "switch": "Switch", "maskSet": "Mask is set", "BAcontrol": "BA Real-time Control", "endedUnconfirmed": "Ended Unconfirmed", "info": {"number": "Number", "deviceName": "Name", "brand": "Brand", "type": "Type", "model": "Model", "capacity": "Capacity", "location": "Location", "pipeInfo": "Pipeline equipment information", "devicePosition": "Device Location", "deviceType": "Device Type", "monitorInfo": "Monitoring equipment information", "deviceModel": "Device Model", "signalCount": "Signal Count", "connectionStatus": "Connection Status", "alarmStatus": "Alarm Status"}, "excelColumns": {"deviceName": "Device Name", "signalName": "Signal Name", "signalValue": "Signal Value", "sampleTime": "Sample Time", "alarmName": "Alarm Name", "alarmMeaning": "Alarm Meaning", "alarmLevel": "Alarm Level"}}, "idcManage": {"common": {"name": "Name", "nameNotNull": "Name not Null!", "nameNotRepeat": "The name cannot be duplicate", "photo": "Photo", "photoName": "Photo Name", "uploadSuccess": "Upload Success", "uploadError": "Upload Error", "photoNameNotNull": "Photo Name not Null", "remark": "Remark", "enableTime": "System Enable Time", "select": "Select", "clear": "Clear", "download": "Download", "longitudeMinError": "Longitude can't less than degrees -180", "longitudeMaxError": "Longitude can't more than degrees 180", "latitudeMinError": "Latitude can't less than degrees -90", "latitudeMaxError": "Latitude can't more than degrees 90", "info": "Basic Info", "complexIndex": "Indicator", "diagram": "Diagram", "forewarming": "<PERSON>e <PERSON>ing", "weight": "Weight", "maintenanceTeam": "Maintenance Team", "manager": "Manager", "uploadPhotoTips": "Pls upload photo first", "configurationShow": "Show Configuration", "isShow": "Show", "notShow": "Not Show", "asTreeRoot": "Set as <PERSON><PERSON><PERSON>", "fileExits": "File Exits", "cover": "Cover", "uploadCancel": "Cancel", "corePoint": "CorePoint", "confirmDeleteIndicator": "Confirm to delte indicator", "signalColor": "Signal Color Setting", "branchSetting": "Branch Setting", "dynamicCurve": "Realtime Curve", "realtimeChart": "Realtime Chart", "editExpression": "Edit Expression"}, "rackbranch": {"rackName": "<PERSON>ck <PERSON>", "deviceNo": "Device Number", "columnCabinetName": "Column Cabinet Name", "columnCabinetNo": "Column Cabinet Number", "switchName": "Switch Name", "switchAlias": "Switch Alias", "downloadTemplate": "Download Template", "import": "Import", "title": "Rack branch", "RackBranchTemplate": "RackBranchTemplate"}, "namebranch": {"branchName": "branchName", "branchId": "branchId", "equipmentName": "equipmentName", "equipmentId": "equipmentId", "downloadTemplate": "downloadTemplate", "Excelimport": "Excelimport", "Excelexport": "Excelexport", "title": "title", "importSuccess": "importSuccess", "importFailed": "importFailed", "deleteSuccess": "deleteSuccess", "submit": "submit", "cancel": "cancel"}, "capacity": {"resourceList": "Resource List", "device": "<PERSON><PERSON>", "rack": "<PERSON><PERSON>", "currentSelected": "Current：", "noData": "Not Found Data", "itdevice": "IT Device", "baseType": "Base Type", "baseAttribute": "Base Attribute", "attributeName": "Attribute Name", "capacityAttribute": "Capacity Attribute", "logicType": "Logic Type", "passiveUpdate": "Passive Update", "complexIndex": "ComplexIndex", "expression": "Expression", "fromCorePoint": "From Core Point", "fromComplexIndex": "From ComplexIndex", "complexIndexSetting": "Complex Index Setting", "ratedCapacity": "Rated Capacity", "compensateFactor": "Compensate Factor", "minValue": "Min Value", "maxValue": "Max Value", "defaultValue": "Default Value", "unit": "Unit", "precision": "Precision", "description": "Description", "submit": "Submit", "cancel": "Cancel", "reset": "Reset", "operation": "Action", "configureComplexIndex": "Configure ComplexIndex", "attributeOptions": "Attribute Options", "createAttributes": "Create", "batchCreation": "Batch Creation", "remark": "Remark", "current": "Current Object", "sameLevel": "Same type", "childLevel": "Childrens", "currentTip": "Create a capacity attribute for the current resource", "sameLevelTip": "Create capacity attributes in batches for resources of the same type", "childLevelTip": "Create capacity attributes in batches for the current resource and sub-resources", "associateCorePoint": "Associate Core Point", "associateComplexIndex": "Associate Complex Index", "associateCapacity": "Associate Capacity", "modalTip": "Select fields which applied in batch", "recordTip1": "If the capacity attribute already exists in the system, it will return success. The original capacity attribute will not be changed and the creation command will not be re-executed (the existing capacity attribute will not be destroyed).", "recordTip2": "Attribute name: It is determined by the capacity base class attribute and cannot be modified. ", "recordTip3": "Calculation method: It is specified by default by the capacity base class and can be manually selected. When using an indicator, creating the capacity attribute will create the indicator at the same time.", "recordTip4": "Calculation method: It is specified by default by the capacity base class and can be manually selected.", "deviceTip": "Show Device", "rackTip": "Show Computer Racks", "itDeviceTip": "Show It Device and Computer Racks", "creationTip1": "Capacity base class, different base classes define different capacity attributes.", "creationTip2": "Base class property for capacity", "creationTip3": "The field name of the capacity attribute, which cannot be modified, is determined by the capacity base class attribute.", "creationTip4": "Passive calculation can only wait for data to be pushed, and index calculation can calculate index expressions.", "creationTip5": "Rated capacity value for capacity calculation.", "creationTip6": "The original input data will be combined with compensation factors.", "creationTip7": "The minimum input value limit for raw input data, beyond which it will be considered invalid data.", "creationTip8": "The maximum input value limit for raw input data, beyond which it will be considered invalid data.", "creationTip9": "Computational precision for all stages of volume data.", "creationTip10": "The unit of capacity data is: %", "creationTip11": "When the input raw data is invalid, use the default raw data, which is empty by default", "creationTip12": "Ordinary description, record something different", "indexTips": "This attribute contains an indicator formula, deleting the attribute will delete the indicator.", "deleteTips": "Deleting attribute for {{0}}. {{1}} This operation is irreversible. Please proceed with caution。"}, "deviceManagement": {"title": "Device Management", "deviceList": "Room List", "name": "Name", "basicType": "Basic Type", "clientType": "Custom Type", "function": "Function", "searchBox": "Device Name", "searchContent": "Search", "add": "Add <PERSON>", "edit": "<PERSON>", "delete": "Delete Device", "uploadSuccess": "Upload Success", "uploadError": "Upload Error", "illegalFormat": "Illegal file format", "selectBasicTypeTipsNotNull": "Basic type can't be empty", "description": "Description", "devicebook": "<PERSON>ce Book", "allSelect": "Select All", "deleteInBatches": "Delete in Batches", "deleteInBatchesTips": "Comfirm to Delete?", "PrintInBatches": "Print In Batches", "PrintInAll": "Print In All", "customeTypeName": "Type Name", "customeTypeNameNotRepeat": "Type name must bu unique", "customerTypeList": "Custom Type List", "remark": "Remark", "previous": "Previous", "next": "Next", "addFromFSU": "Add From FSU", "addFromExcel": "Add From Excel", "FsuList": "FSU List", "gateWaysTitle": "Collector", "acquisitionUnit": "Acquisition Unit", "fileExits": "File Exits", "cover": "Cover", "uploadCancel": "Cancel Upload", "addDeviceSuccess": "Add Device Success", "branchId": "Branch ID", "branchName": "Branch Name", "deviceId": "Device ID", "corepointId": "Core Point ID", "cabinetNo": "Cabinet No", "branchNo": "Branch No", "deviceName": "Device Name", "deviceType": "Device Type", "roomName": "Room Name", "batchChanging": "Batch Applying", "batchApply": "<PERSON><PERSON> Apply", "lastBatchApplyList": "Device List of the latest batch apply", "selectContent": "Select Content", "selectField": "Select Field", "selectType": "Select Type"}, "rackDevice": {"property": {"selectRackDevice": "Select", "rackDeviceId": "Capacity Device ID", "rackDeviceName": "IT Device Name", "modelId": "Associated Device Model", "modelName": "IT Device Model", "rackId": "Associated Cabinets", "assetId": "Associated assets", "assetName": "Asset Name", "assetCode": "asset code", "unitHeight": "U High", "weight": "Weight (kg))", "ratePower": "Electricity (kW)", "coolingCapacity": "Cooling capacity (kJ)", "startRackPosition": "starting point", "launchDate": "Start Date", "purchaseDate": "Purchase Date", "client": "Client", "business": "Business", "length": "Length (m)", "width": "Width(m)", "height": "Height(m)", "state": "State", "power": "Rated Power (kW)", "serialNumber": "Serial Number", "capacityParameter": "Capacity parameters", "brand": "Brand", "manufactor": "Manufactor", "model": "Model", "description": "describe", "deviceNameNotNull": "Capacity device name is not allowed to be empty", "modelIdNotNull": "Device model is not allowed to be empty", "rackDeviceNameNotNull": "Capacity device name is not allowed to be empty", "invalidInput": "The input format is incorrect", "doubleOutofRangeValue": "The value entered cannot be greater than 999999.99", "launchDateAfterPurchaseDate": "The activation time cannot be less than the purchase time", "operation": "Action", "offRackSucess": "The device was removed from the shelf successfully", "offRackFailure": "Failed to remove the device", "recommendShelfSucess": "The device is recommended to be put on the shelf successfully", "recommendShelfFailure": "Failed to recommend the device to be put on the shelf"}, "list": {"title": "List of capacity devices", "deleteSuccess": "successfully deleted!", "deleteError": " failed to delete!", "searchPlaceholder": "Please enter search key"}, "add": {"title": "Add capacity device", "addSuccess": "Added successfully!", "addError": "add failed!"}, "edit": {"title": "Modify capacity device", "editSuccess": "Successfully modified!", "editError": "fail to edit!"}}, "indexAllocation": {"title": "Title", "businessType": "Business Type", "operation": "Action", "expression": "Configuration or Not", "isExpression": "Yes", "NotExpression": "No"}, "hierarchy": {"title": "Hierarchy Management", "addTips": "Name of new sub-node which under current node must bu unique", "createSuccess": "Create Success", "createFailed": "Create Failed", "deleteSuccess": "Delete Success", "deleteTips": "There are devices in the current hierarchy. Do you want to delete them together?", "indicatorEdit": {"indicatorName": "Name", "businessId": "Type", "globalResourceName": "Global Resource Name", "businessIdNotNull": "Business Id Can't be Null", "countPeriod": "Calc-Cycle", "savePeriod": "Save Cycle", "unit": "Unit", "precision": "Precision", "description": "Description", "formula": "Expression", "fxFormula": "FX Formula", "value": "Value", "addValue": "Add Value", "generateFormula": "Insert Formula", "indicator": "Indicator", "points": "Core Points", "deviceTestpointSelect": "Select Device Point", "indicatorSelect": "Select Indicator", "classification": "Classification", "center": "Monitoring Center", "park": "Park", "building": "Building", "floor": "Floor", "room": "Room", "indicatorId": "Indicator ID", "insetPrompt": "Select the function and refine the calculation items", "incorrect": "Incorrect expression configuration", "formulaPrompt": "Tip: You can change the expression manually here", "formulaOperation": "Operator", "addIndicator": "Add Indicator", "addCorePoint": "Add Core Point", "addMath": "Add Function", "iscalctype": "Use Deviation Calculation", "corePointId": "Core Point ID", "corePointName": "Core Point Name", "indicatorTemplateName": "Indicator Template Name", "indicatorTemplate": "Indicator Te<PERSON><PERSON>", "batchIndicator": "Generation Indicator", "indicatorDefinition": "Definition", "indicatorQuality": "Mass Expression", "indicatorCalcOrder": "Indicator Calc Order", "current": "Current", "sameLevel": "Same Level", "childLevel": "Child Level", "currentTip": "Add indicator to the current resource", "sameLevelTip": "Add indicator to the same level resource in batch", "childLevelTip": "Add indicator to the current resource and it's child resource in batch"}}, "graphicPage": {"templateList": "Template List", "submit": "Submit", "multiple": "Apply in Batch", "graphicPageName": "Graphic Page Name", "defaultGraphic": "Default Graphic", "graphicPageNameRequired": "Graphic page name can't be empty!", "graphicPageTemp": "Graphic Page Template", "deleteConfirm": "Confirm Delete", "createForTemplate": "Create From Template", "createForNull": "Add", "cancel": "Cancel"}, "gateWays": {"title": "Collector", "refresh": "Refresh", "name": "Name", "UUID": "UUID", "collector": "Collector Protocol", "IP": "IP", "accessOrNotAcess": "Access Or Not", "asscesSuccess": "Assces Success", "notAccessSuccess": "Out Success", "access": "Access", "notAccess": "Out", "isJoin": "Yes", "notJoin": "No", "notSelectBasetype": "Havn't select basic type"}, "forewarnManage": {"objectName": "Object Name", "earlyWarnName": "ForeWarn Name", "complexIndex": "Complex Index", "warningThreshold": "<PERSON><PERSON><PERSON><PERSON>", "warningJudgmentWay": "Judgment Way", "earlyWarningCategoryName": "Warning Type", "roomName": "Room Name", "earlyWarnTypeName": "<PERSON><PERSON><PERSON>", "tip": {"earlyWarnNameNotNull": "Warning name can't be empty!", "warningThresholdNotNull": "T<PERSON><PERSON><PERSON> can't be empty!", "warningThresholdFormatError": "Threshold must be a number!"}}, "rackManage": {"noUbPleaseCheck": "This Rack None U Position Please Check", "unbindMsg_1": "Confirm to unbind: [{0}] ", "unbindMsg_2": "and [{0}]", "bindSuccess": "Bind success", "rack Location": "Rack Location", "threeDNotExistRack": "3D Not Exist Rack", "unbindSuccess": "Unbind success", "parkName": "IDC name", "parkNameRequired": "IDC name cannot be empty", "buildingName": "Building name", "buildingNameRequired": "Building name cannot be empty", "floorName": "Floor name", "floorNameRequired": "Floor name cannot be empty", "roomName": "room name", "roomNameRequired": "Room name cannot be empty", "position": "Location", "resourcePosition": "Location", "electricPowerConsumption": "Electricity", "uindexPercent": "U Usage", "positionRequired": "Location cannot be empty", "rackState": "rack status", "unitHeight": "U High", "description": "Describe", "startTime": "Start Time", "Unassigned": "unassigned", "notOpen": "nonactivated", "itEquipment": "Device Name", "state": "State", "changeTime": "Change Time", "ubit": "U Position", "opened": "already opened", "rackList": "<PERSON><PERSON> List", "importSuccess": "excel import successfully", "importFailed": "excel import error", "downloadTempalte": "Download template", "rackUsePrompt": "The rack is used by IT equipment", "rackBindPrompt": "The rack has been bound to a U Position manager", "firstThreshold": "first-level threshold", "secondThreshold": "Secondary threshold", "thirdThreshold": "Level 3 Threshold", "fourthThreshold": "four-level threshold", "thresholdRequired": "Threshold cannot be empty", "rackOpen": "rack open", "unitHeightRequired": "U height cannot be empty", "coolingCapacity": "Cooling capacity", "coolingCapacityRequired": "Cooling cannot be empty", "weight": "weight", "weightRequired": "Weight cannot be empty", "indicatorConfigStatus": "Indicator configuration status", "deleteSuccess": "successfully deleted!", "ubitManagement": "U Position Management", "basicInfo": "Basic Info", "capacityAttribute": "Capacity Attribute", "selectUBit": "Select U Position", "capacityBasetype": "Capacity Basetype", "baseTypeAttributeName": "Basetype Attribute", "selected": "Selected", "unselected": "Unselected", "rackTemplate": "RackTemplate", "importRack": "<PERSON><PERSON><PERSON>", "createRack": "Create Ra<PERSON>", "batchUpdateCustomer": "Batch Setting Customer", "batchUpdatePosition": "Batch Setting Position", "bindDevice": "Bind Device", "unbind": "Unbind", "batchUnbind": "Batch unbinding", "unbindMsg": "Please confirm whether to unbind?", "customer": "customer", "computerRackName": "<PERSON>ck <PERSON>", "computerRackNumber": "<PERSON>ck <PERSON>", "roomNamePosition": "<PERSON><PERSON>", "customerName": "Customer", "business": "Business", "remark": "Remark", "powerSource": "Power Source", "ratedUHeight": "Rack <PERSON>(U)", "ratedPower": "Rack Power(kW)", "ratedCooling": "<PERSON><PERSON>(kJ)", "ratedWeight": "Rack Weight(kg)", "computerRackNameRequired": "Rack Name Is Required", "computerRackNumberReq": "Rack Number Is Required", "roomNamePositionReq": "Rack Position Is Required", "ratedUHeightReq": "Rack <PERSON>(U) Is Required", "ratedPowerReq": "Rack Power(kW) Is Required", "ratedCoolingReq": "<PERSON><PERSON>(kJ) Is Required", "ratedWeightReq": "Rack Weight(kg) Is Required", "powerSourceReq": "Power Source Is Required", "putOnRack": "On", "takeOffRack": "Off", "notOnRack": "Off", "hasOnRack": "On", "deviceAlreadyOnRack": "The device is already on rack", "deviceAlreadyOffRack": "The device is already off rack", "confirmTakeOffRack": "Are you going to take the device off the rack?", "remainingRackSpace": "Remaining Rack <PERSON>", "remainingComPower": "Remaining Power", "remainingRackCooling": "Remaining Cooling", "remainingRackWeight": "Remaining Weight", "emptyUIndex": "Empty UIndex", "ratedRackSpace": "Rated Rack Space", "ratedComPower": "Rated Power", "ratedRackCooling": "Rated Cooling", "ratedRackWeight": "Rated Weight", "exportRecommendRack": "Export Recommend Ra<PERSON>(Poisition)", "recommendRackList": "Recommend <PERSON><PERSON>", "selectedUPoi": "Selected Poisition", "selectedUPoiNoSpace": "Selected Poisition Not Enough Space", "airExchangeOccupied": "Selected Position Occupies The Ventilation U Position，Position: ", "ITDEVICE": "Use the IT device power which is on the rack", "COMPLEXINDEX": "Use the complex index of the config", "exportRecommendRackAll": "Export Recommend <PERSON><PERSON>(All)", "deviceNoUHeight": "The device has no U height, can't put on rack", "pleaseSelectRack": "Please select a rack", "pleaseSelectUPoi": "Please select the position to put on device", "cannotDelteOnRackDevice": "The device is on rack, please put off rack first", "useRate": "Use Rate", "allUbit": "All U Position", "ITDeviceType": "IT Device Type", "ITDeviceServiceAttributes": "IT Device Service Attributes", "onRackInfo": "On Rack <PERSON>ails", "ITDeviceModelTemplate": "IT DeviceModel Template", "rackSpace": "U bit usage capacity", "rackWeight": "Structural load-bearing capacity", "rackCooling": "Cooling Capacity", "comPower": "Power load capacity", "batchPropsTitle": "Set properties in batches", "propTitle": "Property settings"}, "generalObject": {"title": "General Object", "commonObjectName": "Name", "uploadSuccess": "Upload Success", "uploadError": "Upload Error", "illegalFormat": "Illegal file format", "fileExits": "File Exits", "cover": "Cover", "uploadCancel": "Cancel", "addGeneralObject": "Add General Object", "deleteConfirm": "Will delete it's diagram and indicator, Confirm?"}, "deviceMonitor": {"signal": {"copy": "copy"}}, "rackSignal": {"rackName": "Rack name", "activationRate": "Activation rate", "template": "Cabinet signal configuration template", "powerRate": "Power", "activationRateExpression": "activation rate expression", "activationRateDescription": "Activation rate description", "powerExpression": "Power Expression", "powerDescription": "PowerDescription", "complete": "complete", "updateSuccess": "Update successful", "updateFailed": "Update failed"}, "itCabinet": {"searchPlaceholder": "Please enter keywords to search", "powerLoadRate": "Power load rate", "ratedPower": "Rated power", "currentPower": "Current active power", "uPositionRate": "U position utilization rate", "powerStats": "Power statistics", "powerTrend": "Cabinet power change trend", "loadRateTrend": "Load rate trend", "maxPower": "Maximum power", "minPower": "Minimum power", "totalPower": "Total power"}}, "ubitManage": {"common": {"curve": "Total U Position Change Curve", "devicetype": "IT Device Types", "engineRoomUsage": "U Position Utilization Rate of Computer Room", "frame": "<PERSON>ck <PERSON> Usage", "businessAttributes": "IT Device Business", "rackChangeLog": "Recent Rack Change Records"}}, "tagManage": {"common": {"tagValue": "Label Code", "itdeviceName": "IT Device Name", "computerRackName": "<PERSON>ck <PERSON>", "uBit": "U Position", "position": "Resource Location", "itdeviceModelName": "Device Model Name", "bindTime": "Updated", "configTag": "Batch configure label codes", "downTemplate": "Download template", "bindItDevice": "Bind IT equipment", "placeholder": "Please enter a search keyword", "unbind": "unbind", "selectDevice": "Select Device", "tagList": "Tag List", "unBindDevice": "The tag is not bound to a device, please check the configuration", "toUnbind": "Please confirm whether to unbind: tag {{0}} and device {{1}}", "unbindSuccess": "Unbind success", "confirmDelete": "Please confirm whether to delete the tag: ", "tagTemplate": "TagTemplate"}}, "deviceBook": {"common": {"cancel": "Cancel", "ok": "OK", "mobile": "Mobile", "address": "Address", "supervisor": "Supervisor", "deviceConfiguration": "Device Configuration", "indicator": "<PERSON>ce Indicator"}}, "crewscheduling": {"common": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "settings": {"name": "Name", "value": "Value", "version": "Version", "description": "Description", "placeholder": "Please enter search key", "action": "Action", "createSetting": "Add", "editSetting": "Edit", "confirmDeleteSetting": "Confirm to delete", "editSettingSuccess": "Edit{0}success", "editSettingFailure": "Edit{0}fail", "insertSettingSuccess": "Add{0}success", "insertSettingFailure": "Add{0}fail", "deleteSettingSuccess": "Delete success", "deleteSettingFailure": "Delete fail", "alreadyCreated": "Name exists", "nameNotEmpty": "Name can't be empty", "valueNotEmpty": "Value can't be empty"}, "menu": {"menuPlan": "Menu Plan", "menuPlanAdd": "Add Plan", "menuPlanEdit": "Edit Plan", "planName": "Plan Name", "scene": "Scene", "menuPlanPrompt": "Plan name can't be empty", "usePlan": "Use Plan", "nameRepeat": "Name Repeat", "currentMenuPlan": "Current Menu Plan", "addParentMenu": "Select Parent <PERSON>u", "menuLineOptions": "Menu Line Options", "menuLinePrompt": "Hold down the left mouse button and drag to the tree list on the right", "menuTreePrompt": "Click the right mouse button to operate", "treeList": "Tree List (After the configuration is complete, please log in again for the configuration to take effect)", "workbench": "Work Bench", "createMenu": "Create New Menu Link", "addMenuItem": "Add <PERSON>u <PERSON>em", "menuName": "Name", "menuENName": "English Name", "menuIcon": "Icon", "asTabShow": "Show as tab", "showInIfame": "Show In Ifame", "selectedMenu": "Selected Menu", "expandedMenu": "Expanded Menu", "hiddenMenu": "Hidden Menu", "isLink": "Is it an external link", "linkAddress": "Link Address", "maxLength": "The name does not exceed 15 characters", "invalidName": "Contains illegal characters!", "required": "Required", "createFolder": "Create Directory", "rename": "<PERSON><PERSON>", "edit": "Edit", "remove": "Remove", "description": "Description", "confirmDelete": "Confirm to delete: ", "deletePrompt": "Please delete sub-menu first!", "movedInvalid": "Invalid Move"}, "realTime": {"common": {"searchFilter": "Please enter search key", "searchFilter2": "Please enter search key", "title": "Device List", "searchFilter3": "Please enter search key(Case sensitive)", "searchFilter4": "Please enter the server name to quickly locate"}}, "notifyManagement": {"notifyServer": {"meaning": "Meaning", "stationName": "Station Name"}, "button": {"detailhostset": "View or modify notifier settings", "start": "Start", "stop": "Stop", "test": "Test", "close": "Close", "save": "Save", "find": "Search", "delete": "Delete", "execute": "Execute", "modify": "Modify", "confirm": "Confirm", "cancel": "Cancel"}}, "accessControl": {"areaManagement": {"title": "Regional Management", "name": "Area Name", "center": "Access Area Center", "addAreaTitle": "New Area", "nameNotNull": "Region name cannot be empty", "deleteConfirm": "Confirm deletion {0}?", "deleteDoorAreaSuccess": "Delete Area {0} Success!", "deleteDoorAreaFail": "Delete Area {0} Fail!", "mountDevices": "Mount Device", "mountDevicesSuccess": "Area {0} Mount Device Success!", "mountDevicesFail": "Area {0} Mount Device Fail!", "updateDoorAreaSuccess": "Modify Area {0} Success!", "updateDoorAreaFail": "Modify Area {0} Fail!"}, "cardManagement": {"title": "Card Management", "cardName": "Card Name", "cardType": "Card Type", "cardLost": "Card Lost", "cardCopy": "Card Copy", "cardDelay": "Card Delay", "originalCard": "Original Card", "targetCard": "Target Card", "validTime": "Valid Time", "originalCardNoNotNull": "Original card number cannot be empty！", "targetCardNoNotNull": "Target card number cannot be empty！", "dateNoNotNull": "Valid Time cannot be empty！", "deleteLostCard": "Report the loss of the original card", "terminalType": "Terminal Type", "cardNo": "Card No", "cardPassword": "Card Password", "cardHolder": "Cardholder", "cardGroup": "Card Group", "registrationDate": "Registration Time", "inDate": "Lifespan", "inDateStart": "Start Date", "inDateEnd": "End Date", "useState": "Usage Status", "addCardTitle": "New Card", "updateCardTitle": "Modify Card", "addCardGroupTitle": "Add Group", "groupName": "Group Name", "groupNameNotNull": "Group name cannot be empty!", "searchForPersons": "Please enter criteria to query", "cardNameNotNull": "Card name cannot be empty!", "expireDateLimit": "The validity period of Hikvision access control cannot exceed 2037 years", "cardNoNotNull": "Card No. cannot be empty!", "cardHolderNotNull": "Card holder cannot be empty!", "addCardHolder": "Please add card holder!", "cardNoType": "Card Number Type", "decimalism": "Decimal", "hexadecimal": "Hexadecimal", "cardNoPattern": "Card number must conform to decimal format and its length cannot exceed ten digits!", "cardNoPatternForHex": "The card number must conform to the hexadecimal format and the length cannot exceed ten digits!", "cardPasswordNotNull": "Card password cannot be empty!", "cardPasswordLessLength": "Card password cannot be less than 4 digits!", "cardPasswordMustNumber": "Card password must be 4-10 digits!", "deleteGroupSuccess": "Delete Group {0} Success!", "deleteGroupFail": "Delete Group {0} Fail!", "deleteCardsSuccess": "Card deletion succeeded!", "deleteCardsFail": "Failed to delete card!", "deleteAllConfirm": "Confirm Batch Delete?", "deleteConfirm": "confirm deletion", "selectDeleteCard": "Please select the card to be deleted!", "selectDelayCard": "Please select the card to be delayed!", "commandSuccess": "Sending control succeeded!", "commandFail": "Sending control failed!", "cardReader": "Card reader", "feature": "Feature", "needCardReaderApp": "You need to download the card reader service program and install it!", "downloadCardReaderApp": "Download Reader Service", "needFingerprintReaderApp": "You need to download the fingerprint reader service program and install it!", "downloadFingerprintReaderApp": "Download Fingerprint Reader Service", "face": "Face", "fingerprint": "Fingerprint", "createCardSuccess": "Card creation succeeded!", "createCardFail": "Failed to create card!", "bindFaceSuccess": "Face entry succeeded!", "bindFaceFail": "Face entry failed!", "bindFingerSuccess": "Fingerprint input succeeded!", "bindFingerFail": "Fingerprint entry failed!", "submitCardInfo": "Card information will be saved!", "deleteFaceSuccess": "Face information deleted successfully!", "deleteFaceFail": "Failed to delete face information!", "timeNotExpired": "End time cannot be earlier than start time"}, "doorManagement": {"title": "Door Manage", "name": "Equipment Name", "batchOperate": "<PERSON><PERSON>", "filterResult": "Filter", "filterResult2": "Filter", "doorParam": "Door parameters", "lastCardSwipeRecord": "Record of last card swiping", "control": "Control", "event": "Event", "swipeRecord": "Card swiping record", "confirm": "Confirm", "resend": "Resend", "noSelected": "No selected object!", "doorName": "Door Name", "doorPassword": "Door Password", "doorNo": "Door No", "openMode": "Open Mode", "fireSignal": "Fire Signal", "lockErrorCount": "Number of card blocking errors", "slotInterval": "Swipe interval of illegal card (s)", "lockTime": "Card blocking time (s)", "keepTime": "Door open holding time (s)", "openDelay": "Door opening timeout(s)", "keepOpen": "Keep Open", "keepClose": "Keep Close", "auto": "Auto", "swipeTime": "Swipe Time", "inOutFlag": "Access Door", "isValid": "Swiping/Opening Status", "controlName": "Control Name", "controlResult": "Control Results", "controlExecuterId": "Executor", "controlParams": "Control Parameters", "unGroup": "Ungrouped", "default": "<PERSON><PERSON><PERSON>", "unset": "Unset", "doorPasswordConfirm": "Password Confirmation", "doorPasswordNotMatch": "The passwords entered twice are inconsistent!", "editDoorParams": "Edit Door Parameters", "timeGroup": "Permission Time Group", "setDoorNameSuccess": "Successfully modified the door name!", "setDoorNameFailed": "Failed to modify the door name!", "sendControlSuccess": "Succeeded in sending modification control!", "sendControlError": "Error sending modification control!", "openDoor": "Open", "closeDoor": "Close", "byStructure": "Actual", "byArea": "Region", "byDoorGroup": "Group", "byBackups": "Backups", "confirmSuccess": "Confirm control success!", "confirmError": "Confirm control error!", "resendSuccess": "Successful retransmission control!", "resendError": "Error in retransmission control!", "resendSomeError": "There is no control result and cannot be reissued.", "eventName": "Alarm Name", "eventSeverity": "Alarm Level", "setTime": "Access Timing", "clearCard": "Delete All Cards", "infoType": "Event Type", "eventInfo": "Event Info", "infoTime": "Event Time", "addTags": "Add Tags"}, "cardAuthorization": {"title": "Card Authorization", "selectDoor": "Select Door", "selectCard": "Select Card", "authorize": "Authorize", "deleteAuthorize": "Delete Authorization", "byGroup": "Group By", "byPerson": "By Person", "byBackups": "By Backups", "placeholder": "Please enter search keywords", "authorizeSuccess": "Succeeded in sending authorization control!", "authorizeError": "Error sending authorization control!", "deleteSuccess": "Succeeded in sending deletion control!", "deleteError": "Error sending delete control!", "modifySuccess": "Succeeded in sending modification authorization control!", "modifyError": "Error sending modification authorization control!", "invalidDate": "Incorrect date format!", "deleteCheck": "Deleting card authorization will perform the same operation for all time groups on the door. Continue?", "needSelectDoor": "Please set the Permission Time Group for the door first!", "needSelectCard": "Please select the access card!", "card": "Access Card", "fingerprint": "Fingerprint", "face": "Face", "outdate": "Update Available", "isAuthorize": "Comfirm authorize?", "newabelSelectTips": "Must select a Permission Time Group for NewaBel Device!"}, "controlQueue": {"operateTime": "Operation Time", "deleteQueueSuccess": "Queue removed successfully!", "deleteQUeueError": "Error removing queue!", "waiting": "Waiting", "handling": "Handling", "history": "History"}, "doorGroup": {"title": "Grouping Management", "name": "Door Grouping", "nameNotNull": "Door group name cannot be empty", "deleteConfirm": "Confirm Deletion {{0}}?", "deleteDoorAreaSuccess": "Delete Door Group {{0}} Success!", "deleteDoorAreaFail": "Delete Door Group {{0}} Fail!", "mountDevicesSuccess": "Door grouping {{0}} Successfully mounted the device!", "mountDevicesFail": "Door grouping {{0}} Failed to mount the device!", "updateDoorAreaSuccess": "Changing Door Groups {{0}} success!", "updateDoorAreaFail": "Changing Door Groups {{0}} Fail!"}, "errorCode": {"-50101": "Same card number existing already!", "-50102": "Invalid card type!", "-50103": "Invalid card group!", "-50104": "Invalid card number!", "-50105": "Invalid door type!", "-50106": "Invalid card category!", "-50107": "Invalid password!", "-50108": "Invalid cardholder!", "-50109": "Invalid site name!", "-50110": "Invalid time!", "-50301": "Not allowed to delete the authorized Time Group!", "-50401": "Not allowed to delete the authorized Face!", "-50402": "Not allowed to delete the authorized Finger Print!"}}, "linkagestrategy": {"name": "Strategy Name", "description": "Description", "interval": "CRON Expression", "usedstatus": "Status", "group": "Strategy Group", "nameNotNull": "Strategy name not null!", "triggerIntervalNotNull": "Trigger interval not null!", "addGroup": "Add Group", "deleteGroupError": "Group cannot be deleted cause it has strategy(s)", "deviceTree": "<PERSON>ce Tree", "IntegerOperation": "Integer Operation", "FloatOperation": "Float Operation", "RelationalOperation": "Relational Operation", "LogicOperation": "Logic Operation", "AggregationOperation": "Aggregation Operation", "Event": "Event", "LinkageControl": "Linkage Control", "Constant": "Constant", "add": {"groupNotNull": "Group not null", "triggerType": "Trigger Type", "timeTrigger": "Time Trigger", "eventTrigger": "Event Trigger", "triggerTypeNotNull": "Trigger type not null", "startTime": "Start Time", "startTimeNotNull": "Start time not null", "endTime": "End Time", "endTimeNotNull": "End time not null"}, "edit": {"strategyLogic": "Strategy Logic", "beenSet": "Has been set", "set": "Hasn't been set", "check": "Check"}, "list": {"deleteStrategyError": "Actived strategy can't delete!", "strategyList": "Strategy List", "addStrategy": "Add Strategy"}, "groupAdd": {"addGroup": "Add Group", "name": "Name", "nameNotNull": "Name Can't be Null", "description": "Description", "group": "Group", "createSuccess": "create success"}, "groupEdit": {"editGroup": "Edit Group", "editSuccess": "edit success"}}, "alarmFitting": {"convergenceRate": {"title": "Convergence Rate Analysis", "todayConvergenceRate": "Convergence Rate Today", "todayAlarms": "Event Today", "todayConvergenceAlarms": "Convergence Event Today", "eventLineDiagram": "Event Line Diagram", "event": "Event", "convergentEvent": "Convergent Event", "convergenceRate": "Convergence Rate", "eventCount": "Event Count", "convergenceRateCount": "Convergence Rate(%)"}, "filtrationEventQuery": {"title": "Filter Event Query", "filterRule": "Filter Rule", "associatedDevice": "Associated Device", "eventDescription": "Event Description", "eventType": "Event Type", "eventName": "Event Name"}, "convergenceEvent": {"title": "Convergent Event Analysis", "eventTimeLine": "Convergent Event Timeline", "eventLevelMatrix": "Event Level Matrix", "eventDeviceCategory": "Event-Equipment Statistics", "eventList": "Event List", "conditionList": "condition List", "num": "No", "birthTime": "Birth Time", "endTime": "End Time", "rootCauses": "Root Causes", "convergenceEvent": "Convergence Event", "cenvergenceNums": "Convergence Counts", "liveCounts": "Live Counts", "convergenceType": "Convergence Type", "impactRangeAnalysis": "Impact Range Analysis", "hasDealWith": "Processed", "UnDealWith": "Not Processed", "convergenceCountMsg": "Total convergence {{count}} events", "selectedEventToConfirm": "Pls select event to confirm", "batchConfrimEventSuccess": "Batch confrim event success", "eventName": "Event Name", "equipmentName": "Equipment Name", "equipmentType": "Equipment Type", "occurRemark": "Remark"}}, "rack": {"equipment": {"position": "Position", "placeholder": "Placeholder", "advancedSearch": "Advanced Search", "customer": "Customer", "business": "Business", "floor": "Floor", "rome": "Room", "space": "Space", "power": "Power", "cool": "Cool", "weight": "Weight", "reset": "Reset", "search": "Search", "info": "Info", "rack": "<PERSON><PERSON>", "workOrder": "Work Order", "workOrderCount": "Work Order Count", "order": "Order", "selectionRange": "Selection Range", "ratedPower": "Rated Power", "loadRate": "Load Rate", "contractNumber": "Contract Number", "contractStartTime": "Contract Start Time", "contractEndTime": "Contract End Time", "contractStatus": "Contract Status", "businessStatistics": "Business Statistics", "customerStatistics": "Customer Statistics", "rackDue": "<PERSON><PERSON>", "contract": "Contract", "building": "Building", "noWorkOrder": "No Work Order"}}, "asset": {"common": {"assetOverview": "Asset Overview", "row": "row", "column": "column", "fieldRequired": "Cell content cannot be empty", "minlength": "The minimum length is", "maxlength": "The maximum length is", "integerRequired": "Must be an integer", "numericRequired": "Must be a number", "datetimeRequired": "Cells must be of text type, format e.g. 1970-01-01 15:01:01", "uniqueFieldRequired": "The cell content must be unique in this column", "uniqueFieldRequiredInSameGroup": "The cell content must be unique in this colum at the same level", "foreignKeyRequired": "Cell content does not meet foreign key constraints", "excelImportSuccess": "Excel import succeeded", "excelImportError": "Excel import error", "exportexcelError": "Export error message", "templateError": "excel template error", "assetId": "Asset ID", "assetCode": "Asset Code", "assetName": "Asset Name", "description": "Remark"}, "configurationTool": {"stationName": "Station", "houseName": "House", "monitorUnitName": "MonitorUnit", "portName": "Port", "ipAddress": "IP", "portAttribute": "PortAttr", "samplerUnitName": "SamplerUnit", "equipmentName": "Equipment Name", "equipmentTemplateName": "Equipment Template", "address": "Address", "dllPath": "Dll Path", "spUnitInterval": "SpUnit Interval", "isUpload": "IsUpload", "uploadConfig": "IsUploadConfig", "deleteConfig": "<PERSON>", "driveTemplateName": "Driver Template<PERSON>ame", "isReset": "IsNeedReset", "isDeleteConfig": "Are you sure del config", "isUploadConfig": "Are you sure upload config", "isAllUpload": "Upload all", "isAllDelete": "Delete all", "rwAttributes": "RwAttributes"}, "assetDevice": {"title": "Asset List", "assetDeviceId": "Asset ID", "deviceCode": "Device Code", "deviceCodeNotDuplicate": "Code can't be duplicate", "assetCategoryNotRepeat": "Category can't be duplicate", "assetExtendNameNotRepeat": "Extend Name can't duplicate", "deviceName": "Device Name", "assetCategoryId": "Asset Category ID", "deviceType": "Device Type", "brand": "Brand", "model": "Model", "capacityParameter": "Capacity Parameter", "settingPosition": "Setting Position", "serialNumber": "Serial Number", "manufactor": "Manufactor", "extendField": "Extend Field", "exportType": "Export Type", "exportTypeSelect": "Select Export Type", "retainCategory": "Retain Category", "retainTips": "Click 'Yes' and the system will automatically save the asset type that the user has entered and does not exist in the system", "excelFileName": "Asset List", "exportAssetDevice": "Import Asset List", "date": "Date", "monthAsset": "Monthly New Assets", "deviceCodeNotNull": "Code can't be null", "deviceNameNotNull": "Name can't be null", "notNull": "can't be null"}, "assetCategory": {"assetCategoryName": "Category Name", "remarks": "Remarks"}, "extendField": {"extCode": "Code", "extName": "Name", "extDesc": "Description", "extOrder": "Order", "extNecessary": "Necessary", "extDataType": "Data Type", "text": "Text", "number": "Number", "date": "Date", "bindDevice": "Equipment", "bindTable": "bindTable", "tableName": "Table Name", "fieldName": "Field Name", "fieldNameTips": "Use the field in the data table as the display name of the drop-down box", "fieldValue": "Field Value", "fieldValueTips": "Use the field in the data table as the storage value of the drop-down box", "extNecessaryNotNull": "Necessary can't be null", "extDataTypeNotNull": "Data Type can't be null", "extCodeNotNull": "Code can't be null", "extNameNotNull": "Name can't be null"}}, "linkmanagement": {"import": "Import", "export": "Export", "linkId": "Link Id", "linkName": "Link Name", "linkType": "Link Type", "local": "Local Object", "localObjectId": "Local Object Id", "localObject": "Local Object Name", "localObjectType": "Local Object Type", "localPort": "Local Port", "remote": "Remote Object", "remoteObjectId": "Remote Object Id", "remoteObject": "Remote Object Name", "remoteObjectType": "Remote Object Type", "remotePort": "Remote Port", "staticPropertys": "Static Propertys", "remark": "Remark", "addLink": "Add Link", "editLink": "Edit Link", "label": "Label", "value": "Value", "currentEq": "Current Selected: ", "localEmpty": "Local object has missing attribute!", "remoteEmpty": "Remote object has missing attribute!", "linkNameEmpty": "Link Name is empty!", "staticEmpty": "Static Propertys has missing attribute!", "objectIdEqual": "The local object and the remote object cannot be the same!", "updateSuccess": "Update Success", "updateError": "Update Error", "deleteLink": "Confirm to delete link ", "linkTemp": "Link List Template", "linkList": "Link List"}, "focussignal": {"over10000": "The query quantity exceeds 10,000. Please reset the query conditions."}, "videomessage": {"addSuccess": "Added Successfully", "saveSuccess": "Saved Successfully", "deleteSuccess": "Successfully Deleted", "updateSuccess": "Update Completed", "uploadSuccess": "Upload Success", "modifySuccess": "Successfully Modified", "syncSuccess": "Sync Success", "addFail": "Add Failed", "deleteFail": "Failed Delete", "uploadFail": "Upload Fail", "updateFail": "Fail Edit", "operationFiled": "Operation Failed", "pleaseAddViewOperate": "Please add the view before proceeding!", "enumerate": {"0": "Success", "1001": "Request parameter invalid", "1002": "Primary key assign error", "1003": "Create object error", "1004": "Update object error", "1005": "Delete object error", "1006": "Method not implement", "1008": "Name Exist", "1009": "User Authorize Fail", "1010": "Operation In Progress", "1011": "Big Data Exception", "1012": "Exist Dependency", "1014": "Ip and port and channel repeated", "1015": "play rtsp timeout", "2001": "Role not found error", "2002": "User not found error", "2003": "LogonId is repeat", "2004": "Account<PERSON><PERSON><PERSON> is repeat", "4001": "Send control failed", "4002": "Signature is null", "4003": "Timestamp is null", "4004": "<PERSON><PERSON> is null", "4005": "Signature is out of date", "4006": "Signature is invalid", "5001": "Camera channel authentication failed", "5002": "Camera channel failed to open", "5003": "Camera channel parameter error", "5004": "Can not find the available media server", "5005": "Call media server error", "5006": "The camera channel is unreachable", "5007": "The camera channel rtsp url not found", "5008": "The camera channel stream has existed", "5009": "Media response error message not explain", "5010": "Camera channel not found", "-50401": "Not allowed delete authorized face data", "-50402": "Not allowed delete authorized finger data"}, "featureList": {"msePlayback": "Basic player doesn't work on your device", "mseLivePlayback": "HTTP MPEG2-TS/FLV The live stream is not working on your device", "nativeMP4H264Playback": "Your device does not support H.264 MP4 video file natively.", "nativeMP4H265Playback": "Your device does not support H.265 MP4 video file natively.", "nativeWebmVP8Playback": "Your device does not support WebM VP8 video file natively.", "nativeWebmVP9Playback": "Your device does not support WebM VP9 video file natively."}}, "faultRecording": {"tree": {"placeHolder": "Enter keywords to search"}, "charts": {"effectiveValue": "Effective Value", "min": "Minimum", "max": "Maximum", "phaseAngle": "Phase Angle", "preGroup": "Prev Group", "nextGroup": "Next Group", "totalHarmonics": "Total Harmonics", "evenHarmonics": "Even Harmonics", "oddHarmonics": "Odd Harmonics"}, "info": {"title": "Fault Recording", "startTime": "Start Time", "triggerTime": "Trigger Time", "recordingFrequency": "Recording Frequency", "ratedFrequency": "Rated Frequency", "symmetricalComponent": "Symmetrical Component", "positiveSequenceVoltage": "Positive Sequence Voltage", "negativeSequenceVoltage": "Negative Sequence Voltage", "zeroSequenceVoltage": "Zero Sequence Voltage", "voltageNegativeSequenceUnbalance": "Voltage Negative Sequence Unbalance", "voltageZeroSequenceUnbalance": "Voltage Zero Sequence Unbalance", "positiveSequenceCurrent": "Positive Sequence Current", "negativeSequenceCurrent": "Negative Sequence Current", "zeroSequenceCurrent": "Zero Sequence Current", "currentNegativeSequenceUnbalance": "Current Negative Sequence Unbalance", "currentZeroSequenceUnbalance": "Current Zero Sequence Unbalance", "timeRange": "Please set time range", "lastWeek": "Last Week", "lastMonth": "Last Month", "last3Months": "Last 3 Months", "last6Months": "Last 6 Months", "lastYear": "Last Year", "custom": "Custom", "viewList": "View List", "showMode": "Show Mode", "multiAxis": "Multi-Axis", "singleAxis": "Single Axis", "harmonic": "Harmonic", "rms": "RMS", "compSetting": "Comp Setting", "paramSelect": "Param Select", "channelNaming": "Channel Naming", "printSetting": "Print Setting", "vectorGraphicsSetting": "Vector Graphics", "export": "Export", "exportImg": "Export Picture", "exportData": "Export Data", "connectTriangle": "Connect to Triangle", "showVectorLine": "Show Vector Line"}, "table": {"no": "No.", "triggerTime": "Trigger Time", "eqName": "Equipment Name", "eqPosition": "Equipment Position"}, "modal": {"vectorConnection": "Vector Connection", "vectorLine": "Vector Line", "groupSetting": "Group Setting", "showCurrent": "Show Current", "showVoltage": "Show Voltage"}}}