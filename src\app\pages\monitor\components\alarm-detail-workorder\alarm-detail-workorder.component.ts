import { Component, OnDestroy, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AlarmListService } from '../../alarm-list/alarm-list.service';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { Location } from '@angular/common';
import { GlobalState } from '@app/global.state';
import { BaseCRUDService } from '../../../../communal/providers/baseCRUDService/baseCRUDService';

import * as _ from 'lodash';
import { ToastService } from '@app/communal/providers';
import { SessionService } from '@app/core/services/session.service';
import { AlarmCommonCommentComponent } from '@app/core/components/alarm-commoncomment';
import { DeviceShieldConfigComponent } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.component';
import { DeviceShieldEventService } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.service';
import { GraphicPageModalComponent } from '@app/communal/components/graphic-page-modal/graphic-page-modal.component';
import { DeviceInfoModalComponent } from '@app/pages/devices/device-info-modal/device-info-modal.component';
import { AuthenticationService } from '@app/core/services/authentication.service';

@Component({
  selector: 'alarm-detail-workorder',
  templateUrl: './alarm-detail-workorder.component.html',
  styleUrls: ['./alarm-detail-workorder.component.scss'],
})
export class AlarmDetailWorkOrderComponent implements OnInit, OnDestroy {
  @Input() navFromAlarmList: boolean;
  @Input() returnUrl: string;

  @Input('ActiveEvent')
  set ActiveEvent(value: unknown) {
    if (value !== undefined) {
      if (!this.activeEvent.eventId) {
        this.activeEvent = value;
        this.getEventMask();
      } else
        this.activeEvent = value;
    }
  }
  @Input() resourceStructureId: number;

  hasRoomGraphic = true;
  private roomGraphicId: string;
  private lanValues: any;
  maskObject: any;
  activeEvent: any = {};
  isByteDance = false;
  alarmReasonList:any = [];
  // tslint:disable-next-line:member-ordering
  lanKeys = [
    'general.dashboard.alarmCount',
    'general.dashboard.oneLevelAlarm',
    'general.dashboard.secondLevelAlarm',
    'general.dashboard.threeLevelAlarm',
    'general.common.confirm',
    'general.common.end',
    'general.alarm.isEnded',
    'general.dashboard.fourLevelAlarm'];

  constructor(
    private router: Router,
    private serv: BaseCRUDService,
    protected service: AlarmListService,
    private authenticationService: AuthenticationService,
    public sessionService: SessionService,
    private translate: TranslateService,
    private location: Location,
    private ngbModal: NgbModal,
    private toastService: ToastService,
    private shieldService: DeviceShieldEventService,) {

  }

  ngOnDestroy(): void {
    //
  }

  ngOnInit() {
    this.authenticationService.getAuthenticationJson().subscribe(resJson => {
      this.isByteDance = resJson['bytedace.web.enable'];
      if (this.isByteDance) {
        this.service.getAlarmReasonList().subscribe(res => {
          this.alarmReasonList = res;
       });
      }
    });
    this.translate.get(this.lanKeys).subscribe((values) => {
      this.lanValues = values;
    });
    if (!this.resourceStructureId || this.resourceStructureId === 0) {
      this.resourceStructureId = this.activeEvent.resourceStructureId;
    }
    if (this.resourceStructureId) {
      this.serv
        .get(
          null,
          `graphicpages?objectId=${this.resourceStructureId}&pageCategory=5`
        )
        .subscribe((res) => {
          if (res && res.length !== 0) {
            this.sessionService.setRSHMIPages(this.resourceStructureId, res);
            this.hasRoomGraphic = true;
            this.roomGraphicId = res[0]['id'];
          } else {
            this.hasRoomGraphic = false;
          }
        });
    }
  }

  hasLevelMaskPermission(): boolean {
    if (this.isByteDance)
      return this.sessionService.hasPermission(120 + this.activeEvent.eventLevel);
    else
      return true;
  }

  hasLevelConfirmPermission(): boolean {
    if (this.isByteDance)
      return this.sessionService.hasPermission(130 + this.activeEvent.eventLevel);
    else
      return true;
  }

  getEventMask() {
    const ids = this.activeEvent.stationId + ',' +
      this.activeEvent.equipmentId + ',' +
      this.activeEvent.eventId;

    this.shieldService.getEventMask(ids).subscribe(
      res => {
        this.maskObject = res;
      }
    )
  }

  refreshData() {
    //
  }

  back() {
    if (this.navFromAlarmList) {
      this.router.navigateByUrl(
        this.returnUrl
      );
    } else {
      this.location.back();
    }
  }
  gotoMoreAlarmRecord() {
    this.router.navigateByUrl(
      `/entry/alarms/device/alarm?deviceId=${this.activeEvent.equipmentId}`
    );
  }
  gotoDevice() {
    // const toUrl = '/entry/device/' + this.deviceId;
    // this.router.navigate([toUrl]);
    this.serv
      .get(
        null,
        `graphicpages?objectId=${this.activeEvent.equipmentId}&pageCategory=7`
      )
      .subscribe((res) => {
        if (res && res.length !== 0) {
          this.sessionService.setComp(res);
          let comp = res[0];
          res.forEach(s => {
            if (s.isDefault)
              comp = s;
          })
          const id = comp['id'];
          this.router.navigateByUrl(
            `/entry/hmi?pageId=${id}&stationId=${this.activeEvent.stationId}&deviceId=${this.activeEvent.equipmentId}`
          );
          this.serv.get(null, `graphicpages/equipmentbasetypechange?equipmentId=${this.activeEvent.equipmentId}`).subscribe(res => {
            if (res) {
              this.toastService.showToast(
                'warning',
                this.translate.instant('graphic.deviceTemp.changed'),
                null,
                { timeOut: 3000 }
              );
            }
          })
          // const modal = this.ngbModal.open(GraphicPageModalComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
          // modal.componentInstance.passId = id;
          // modal.componentInstance.title = this.activeEvent.equipmentName;
          // modal.componentInstance.width = 1914;
          // modal.componentInstance.height = 980;
          // modal.componentInstance.contentLeft = 0;
        } else {
          const modal = this.ngbModal.open(DeviceInfoModalComponent, {
            container: 'app-entry', size: 'lg', backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy'
          });
          modal.componentInstance.deviceId = this.activeEvent.equipmentId;
          modal.componentInstance.title = this.activeEvent.equipmentName + "_" + this.translate.instant('general.menu.deviceDetail');
          modal.componentInstance.width = 1914;
          modal.componentInstance.height = 930;
          modal.componentInstance.contentLeft = 0;
          // modal.componentInstance.title = 's2.alarm.queryParameterSelection';
          // this.router.navigateByUrl(
          //   `/entry/device/${this.activeEvent.equipmentId}`
          // );
        }
      });
  }
  gotoRoom() {
    if (this.roomGraphicId) {
      this.router.navigateByUrl(
        `/entry/hmi?pageId=${this.roomGraphicId}`
      );
    } else {
      if (!this.resourceStructureId || this.resourceStructureId === 0) {
        this.resourceStructureId = this.activeEvent.resourceStructureId;
      }
      if (this.resourceStructureId) {
        this.serv
          .get(
            null,
            `graphicpages?objectId=${this.resourceStructureId}&pageCategory=5`
          )
          .subscribe((res) => {
            if (res && res.length !== 0) {
              this.sessionService.setRSHMIPages(this.resourceStructureId, res);
              this.hasRoomGraphic = true;
              let comp = res[0];
              res.forEach(s => {
                if (s.isDefault)
                  comp = s;
              })
              this.roomGraphicId = comp['id'];
              this.router.navigateByUrl(
                `/entry/hmi?pageId=${this.roomGraphicId}`
              );
            } else {
              this.hasRoomGraphic = false;
            }
          });
      }
    }
  }

  showMask(): void {
    if (!this.activeEvent.eventId) {
      return;
    }
    const modal = this.ngbModal.open(DeviceShieldConfigComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.equipmentId = this.activeEvent.equipmentId;
    modal.componentInstance.eventId = this.activeEvent.eventId;
    modal.componentInstance.stationId = this.activeEvent.stationId;
    if (this.maskObject) {
      modal.componentInstance.timeGroupCategory = this.maskObject.timeGroupCategory;
      modal.componentInstance.timeGroupSpans = this.maskObject.timeGroupSpans;
      modal.componentInstance.InputstartTime = this.maskObject.startTime;
      modal.componentInstance.InputendTime = this.maskObject.endTime;
      modal.componentInstance.Inputreason = this.maskObject.reason;
    }

    modal.result.then((res) => {
      if (res) {
        const maskUrl = 'eventmasks';
        this.saveMask(res, maskUrl);
      }
    });
  }

  saveMask(param, maskUrl) {
    this.shieldService.saveMask(param, maskUrl).subscribe(
      res => {
        this.toastService.showToast('success', this.translate.instant('s2.shieldevent.saveSuccess'), '');
        this.getEventMask();
      },
      error => {
        this.toastService.showToast('error', this.translate.instant('s2.shieldevent.saveError'), '');
      }
    )
  }

  showOp(opType): void {
    if (!this.activeEvent.sequenceId) {
      return;
    }
    this.service.selectedItems = [this.activeEvent];
    const modal = this.ngbModal.open(AlarmCommonCommentComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.opType = opType;
    if (this.isByteDance) {
      modal.componentInstance.reasonList = this.alarmReasonList;
    }
    if (opType === 'confirm') {
      modal.componentInstance.theTitle = this.lanValues['general.common.confirm'];
    } else if (opType === 'end') {
      modal.componentInstance.theTitle = this.lanValues['general.common.end'];
    }
  }
}
