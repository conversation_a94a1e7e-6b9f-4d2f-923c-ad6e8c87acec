import { <PERSON>mpo<PERSON>, Element<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener, ChangeDetectorRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LocalDataSource } from 'ng2-smart-table';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ToastService } from 'app/communal/providers/toast.service';
import { SessionService } from '@core/services/session.service';
import { AlarmFocusPanelService } from './alarm-focus-panel.service';
import { tableResizeFunc } from 'ng-devui/data-table';
import { HttpClient } from '@angular/common/http';
import { AlarmCommonCommentComponent } from '@app/core/components/alarm-commoncomment';
import { AlarmListService } from '../monitor/alarm-list/alarm-list.service';
import { AuthenticationService } from '@app/core/services/authentication.service';
import { SeverityRendererComponent } from './alarm-focus-panel-severityrenderer.component';
import { CustomServerSourceConf } from '@app/communal/components/custom-smart-table/data-source/custom-server-conf';
import { CustomColumnsComponent } from '@app/pages/site/components/custom-columns/custom-columns.component';
import { FocusPanelServerDataSource } from './alarm-focus-panel-datasource.service';
// 定义警告数据的类型
interface AlarmData {
  sequenceId: string;
  stationId: number;
  eventLevel: number;
  eventSeverity: string;
  equipmentId: number;
  equipmentName: string;
  equipmentPosition: string;
  baseEquipmentId: number;
  baseEquipmentName: string;
  eventId: number;
  eventName: string;
  meanings: string;
  eventValue: number;
  endValue: number | null;
  startTime: string;
  confirmTime: string | null;
  endTime: string | null;
  description: string | null;
  remarkCount: number;
  stationCategoryId: number;
  eventCategoryId: number;
  maintainState: number;
  [key: string]: any;
}
@Component({
  selector: 'app-alarm-focus-panel',
  templateUrl: './alarm-focus-panel.component.html',
  styleUrls: ['./alarm-focus-panel.component.scss'],
})
export class AlarmFocusPanelComponent implements OnInit, OnDestroy {

  settingsOneAndTwo: any;
  settingsThreeAndFour: any;
  baseSettings: any;

  alarmSeverities;
  alarmSeveritiesOneAndTwo;
  alarmSeveritiesThreeAndFour;

  oneandtwoDisplayData: any[] = [];
  oneandtwoSum = 0;
  threeandfourSum = 0;
  threeandfourDisplayData: any[] = [];
  oneandtwoTableWidthConfig: any[] = [];
  threeandfourTableWidthConfig: any[] = [];
  oneandtwoPagerConfig = {
    number: 0,
    size: 15,
    totalElements: 0
  };
  threeandfourPagerConfig = {
    number: 0,
    size: 15,
    totalElements: 0
  };
  allSelectedOneAndTwo = false;
  allSelectedThreeAndFour = false;
  // 数据加载状态
  searchTextOneAndTwo: string;
  searchTextThreeAndFour: string;
  isByteDance = false;
  minWidth = 40;
  tableHeight = '640px';
	resizeObserver: ResizeObserver;
  runAtUICore = false;

  sortEvent = '';
  cols = [];

  columnHeaders: any;
  columnHeaderKeys: string[];
  userId: any;
  showAlarmSeverities: string[] = ['#ff0000', '#ff7f00', '#ffcc00', '#00ff00']; // 示例：事件级别的颜色


  intervalId: any;
  screenHeight!: number;

  selectedItemsOneandTwo: any[] = [];
  selectedItemsThreeandFour: any[] = [];

  allSelectedItems = [];
  alarmReasonList:any = [];
  http: HttpClient;

  // 添加定制列相关属性
  defaultColumns: any[] = []; // 用于modal显示的默认列配置
  defaultColumnsMap: any = {}; // 存储列的完整配置（包含渲染等信息）
  ColumnCustomTypeOneAndTwo = 'OneAndTwoColumnAlarmFocusPanel';
  ColumnCustomTypeThreeAndFour = 'ThreeAndFourColumnAlarmFocusPanel';
  savedCustomColumnIdOneAndTwo: any;
  savedCustomColumnIdThreeAndFour: any;
  savedCustomColumnOneAndTwo: any;
  savedCustomColumnThreeAndFour: any;

  @HostListener('window:resize', ['$event'])
  onWindowResize(event: any) {
    this.screenHeight = event.target.innerHeight;
  }
  constructor(
    private alarmlistService: AlarmListService,
    private translateService: TranslateService,
    private ngbModal: NgbModal,
    private ele: ElementRef,
    private cdr: ChangeDetectorRef,
    private toastService: ToastService,
    public sessionService: SessionService,
    private alarmService: AlarmFocusPanelService,
    private authenticationService: AuthenticationService,
  ) {

  }

  getCellTitle(field: string, item: any): string {
    switch (field) {
      case 'eventSeverity': return item?.severityName || '';
      case 'eventName': return item?.eventName || '';
      case 'startTime': return item?.startTime || '' ;
      case 'equipmentName': return item?.equipmentName || '';
      case 'equipmentPosition': return item?.equipmentPosition || '';
      case 'equipmentCategoryName': return item?.equipmentCategoryName || '';
      case 'confirmTime': return item?.confirmTime || '';
      case 'endTime': return item?.endTime || '';
      case 'meanings': return item?.meanings || '';
      case 'eventValue': return item?.eventValue || '';
      case 'eventReasonType': return item?.eventReasonTypeName || '';
      default: return '';
    }
  }
  
  getData() {
    this.getOneAndTwoData();
    this.getThreeAndFourData();
  }

  // 新增：获取一二级告警数据的方法
  getOneAndTwoData() {
    const selectedIds = this.saveSelectedState(1);
    // 获取新数据
    this.alarmService.getData(1, this.oneandtwoPagerConfig.number, this.oneandtwoPagerConfig.size, this.searchTextOneAndTwo).subscribe(data => {
      if (data.content && Array.isArray(data.content)) {
        this.oneandtwoDisplayData = data.content;
        this.oneandtwoSum = data.totalElements;
        this.oneandtwoPagerConfig = {
          number: this.oneandtwoPagerConfig.number,
          size: this.oneandtwoPagerConfig.size,
          totalElements: data.totalElements
        };

        this.processAlarmData(1);
      }
      else{
        this.oneandtwoDisplayData = [];
      }
      this.restoreSelectedState(1, selectedIds);
      });
    
  }
  getThreeAndFourData() {

    const selectedIds = this.saveSelectedState(2);
    this.alarmService.getData(2, this.threeandfourPagerConfig.number, this.threeandfourPagerConfig.size, this.searchTextThreeAndFour).subscribe(data => {
      if (data.content && Array.isArray(data.content)) {
        this.threeandfourDisplayData = data.content;
        this.threeandfourSum = data.totalElements;
        this.threeandfourPagerConfig = {
          number: this.threeandfourPagerConfig.number,
          size: this.threeandfourPagerConfig.size,
          totalElements: data.totalElements
        };
        this.processAlarmData(2);
      }
      else{
        this.threeandfourDisplayData = [];
      }
      this.restoreSelectedState(2, selectedIds);
    });
  }

  // 新增：处理一二级告警数据
  processAlarmData(type:number) {
    if (type == 1) {
      if (this.oneandtwoDisplayData && this.oneandtwoDisplayData.length) {
        this.oneandtwoDisplayData.forEach(item => {
        item.selected = false;
        // 处理告警等级颜色
        if (this.alarmSeverities) {
          const severity = this.alarmSeverities.find(s => s.eventLevel === item.eventLevel);
          if (severity) {
            item.severityColor = severity.displayColor;
            item.severityName = severity.severityName;
          }
        }

        // 处理告警分类（字节跳动客户端）
        if (this.isByteDance && item.eventReasonType && this.alarmReasonList) {
          const reasonItem = this.alarmReasonList.find(r => r.id === item.eventReasonType);
          if (reasonItem) {
            item.eventReasonTypeName = reasonItem.name;
          }
        }
        });
      }
    }
    else if (type == 2) {
      if (this.threeandfourDisplayData && this.threeandfourDisplayData.length) {
        this.threeandfourDisplayData.forEach(item => {
          item.selected = false;
          if (this.alarmSeverities) {
            const severity = this.alarmSeverities.find(s => s.eventLevel === item.eventLevel);
            if (severity) {
              item.severityColor = severity.displayColor;
              item.severityName = severity.severityName;
            }
          }
          if (this.isByteDance && item.eventReasonType && this.alarmReasonList) {
            const reasonItem = this.alarmReasonList.find(r => r.id === item.eventReasonType);
            if (reasonItem) {
              item.eventReasonTypeName = reasonItem.name;
            }
          }
        });
      }
    }
  }

  // 根据事件级别返回对应的颜色
  batchConfirm(type: number): void {
    this.allSelectedItems = type == 1 ? this.selectedItemsOneandTwo : this.selectedItemsThreeandFour;
    let confirmItems: any[] = [];
    let allLen = 0;
    this.allSelectedItems.forEach(item => {
      if (!item.confirmTime || item.confirmTime === '') {
        allLen += 1;
        confirmItems.push(item);
      }
    });
    if (allLen === 0) {
      this.toastService.showToast('error', this.columnHeaders['general.alarm.allConfirmed'], '');
      return;
    }
    if (this.isByteDance) {
      const canList = this.allSelectedItems.filter(s => this.sessionService.hasPermission(130 + s.eventLevel));
      if (canList.length != confirmItems.length) {
        if (canList.length == 0) {
          this.toastService.showToast('error', this.translateService.instant('general.alarm.hasConfirmed'), '', { timeOut: 3000 });
          return;
        } else {
          confirmItems = canList;
          this.toastService.showToast('warning', this.translateService.instant('general.alarm.hasConfirmed'), '', { timeOut: 3000 });
        }
      }
    } else if (allLen < this.allSelectedItems.length) {
      this.toastService.showToast('warning', this.columnHeaders['general.alarm.hasConfirmed'], '');
    }
    const modal = this.ngbModal.open(AlarmCommonCommentComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.theTitle = this.columnHeaders['general.common.confirm'];
    modal.componentInstance.opType = "confirm";
    modal.componentInstance.isBatch = true;
    modal.componentInstance.reasonList = this.alarmReasonList;
    this.alarmlistService.selectedItems = confirmItems;
    modal.result.then(res => {
      if (res && res.ok) {
        if (type == 1) {
          this.selectedItemsOneandTwo = [];
          this.getOneAndTwoData();
        }
        else if (type == 2) {
          this.selectedItemsThreeandFour = [];
          this.getThreeAndFourData();
        }
      }
    });

  }
  searchChangeType(type: number): void {
    if (type == 1) {
      this.getOneAndTwoData();
    }
    else if (type == 2) {
      this.getThreeAndFourData();
    }
  }

  // 监听页面变化
  onPageChange(type: number, event: any) {
    if(type == 1){
      this.oneandtwoPagerConfig.number = event;
      if (this.oneandtwoPagerConfig.number * this.oneandtwoPagerConfig.size > this.oneandtwoPagerConfig.totalElements) {
        this.oneandtwoPagerConfig.number = 0;
      }
      this.getOneAndTwoData();
    }
    else if(type == 2){
      this.threeandfourPagerConfig.number = event;
      if (this.threeandfourPagerConfig.number * this.threeandfourPagerConfig.size > this.threeandfourPagerConfig.totalElements) {
        this.threeandfourPagerConfig.number = 0;
      }
      this.getThreeAndFourData();
    }
  }
  changeAllSelectedOneAndTwo($event) {
    const checked = $event.target.checked;
    this.oneandtwoDisplayData.forEach(item => {
      item.selected = checked;
    });
    this.updateSelectedItems();
  }
  changeAllSelectedThreeAndFour($event) {
    const checked = $event.target.checked;
    this.threeandfourDisplayData.forEach(item => {
      item.selected = checked;
    });
    this.updateSelectedItems();
  }

  changeSelectedOneAndTwo($event, item) {
    item.selected = $event.target.checked;
    this.updateSelectedItems();
  }
  changeSelectedThreeAndFour($event, item) {
    item.selected = $event.target.checked;
    this.updateSelectedItems();
  }

  private updateSelectedItems() {
    this.selectedItemsOneandTwo = this.oneandtwoDisplayData.filter(item => item.selected);
    this.selectedItemsThreeandFour = this.threeandfourDisplayData.filter(item => item.selected);
    // 更新全选状态
    this.allSelectedOneAndTwo = this.oneandtwoDisplayData.length > 0 && 
      this.oneandtwoDisplayData.every(item => item.selected);
    this.allSelectedThreeAndFour = this.threeandfourDisplayData.length > 0 && 
      this.threeandfourDisplayData.every(item => item.selected);
  }
  setSetting(): void {
    this.columnHeaderKeys = [
      'general.common.noData',
      's2.stationState.message.selectNotNull',
      'general.alarm.severityName',
      'general.alarm.birthTime',
      'general.alarm.confirmTime',
      'componentDisplay.alarmTable.deviceName',
      'general.alarm.devicePosition',
      'general.alarm.deviceCategory',
      'general.alarm.hasConfirmed',
      'general.alarm.allConfirmed',
      'general.common.confirm',
      'general.alarm.coreEventName',
      'general.alarm.clearTime',
      'general.alarm.eventReasonTypeName',
      'general.alarm.meanings',
      'general.alarm.occurValue'
    ];
    this.translateService.get(this.columnHeaderKeys).subscribe((values) => {
      this.columnHeaders = values;
      this.initColumnConfigurations();
    });
  }

  initColumnConfigurations() {
    // 初始化默认列配置（用于modal显示）
    this.defaultColumns = [
      {
        key: 'eventSeverity',
        title: this.columnHeaders['general.alarm.severityName'],
        headerText: this.columnHeaders['general.alarm.severityName'],
        width: '100px',
        canSort: true,
        visible: true
      },
      {
        key: 'eventName',
        title: this.columnHeaders['general.alarm.coreEventName'],
        headerText: this.columnHeaders['general.alarm.coreEventName'],
        width: '150px',
        canSort: true,
        visible: true
      },
      {
        key: 'startTime',
        title: this.columnHeaders['general.alarm.birthTime'],
        headerText: this.columnHeaders['general.alarm.birthTime'],
        width: '150px',
        canSort: true,
        visible: true
      },
      {
        key: 'equipmentName',
        title: this.columnHeaders['componentDisplay.alarmTable.deviceName'],
        headerText: this.columnHeaders['componentDisplay.alarmTable.deviceName'],
        width: '150px',
        canSort: true,
        visible: true
      },
      {
        key: 'equipmentPosition',
        title: this.columnHeaders['general.alarm.devicePosition'],
        headerText: this.columnHeaders['general.alarm.devicePosition'],
        width: '150px',
        canSort: true,
        visible: true
      },
      {
        key: 'equipmentCategoryName',
        title: this.columnHeaders['general.alarm.deviceCategory'],
        headerText: this.columnHeaders['general.alarm.deviceCategory'],
        width: '150px',
        canSort: true,
        visible: true
      },
      {
        key: 'confirmTime',
        title: this.columnHeaders['general.alarm.confirmTime'],
        headerText: this.columnHeaders['general.alarm.confirmTime'],
        width: '150px',
        canSort: true,
        visible: true
      },
      {
        key: 'endTime',
        title: this.columnHeaders['general.alarm.clearTime'],
        headerText: this.columnHeaders['general.alarm.clearTime'],
        width: '150px',
        canSort: true,
        visible: true
      },
      {
        key: 'meanings',
        title: this.columnHeaders['general.alarm.meanings'],
        headerText: this.columnHeaders['general.alarm.meanings'],
        width: '150px',
        canSort: true,
        visible: true

      },
      {
        key: 'eventValue',
        title: this.columnHeaders['general.alarm.occurValue'],
        headerText: this.columnHeaders['general.alarm.occurValue'],
        width: '150px',
        canSort: true,
        visible: true
      }
    ];

    // 如果是字节跳动，添加告警分类字段
    if (this.isByteDance) {
      this.defaultColumns.push({
        key: 'eventReasonType',
        title: this.columnHeaders['general.alarm.eventReasonTypeName'],
        headerText: this.columnHeaders['general.alarm.eventReasonTypeName'],
        width: '150px',
        canSort: true,
        visible: true
      });
    }

    // 初始化列映射（包含完整的列配置）
    this.defaultColumnsMap = {
      eventSeverity: {
        key: 'eventSeverity',
        title: this.columnHeaders['general.alarm.severityName'],
        headerText: this.columnHeaders['general.alarm.severityName'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true,
        type: 'custom',
        valuePrepareFunction: (cell, row) => {
          const severity = row.eventLevel;
          const severityData = this.alarmSeverities.filter(s => s.eventLevel === severity)[0];
          return severityData ? {
            color: severityData.displayColor,
            name: severityData.severityName,
          } : {};
        },
        renderComponent: SeverityRendererComponent,
      },
      eventName: {
        key: 'eventName',
        title: this.columnHeaders['general.alarm.coreEventName'],
        headerText: this.columnHeaders['general.alarm.coreEventName'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      startTime: {
        key: 'startTime',
        title: this.columnHeaders['general.alarm.birthTime'],
        headerText: this.columnHeaders['general.alarm.birthTime'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      equipmentName: {
        key: 'equipmentName',
        title: this.columnHeaders['componentDisplay.alarmTable.deviceName'],
        headerText: this.columnHeaders['componentDisplay.alarmTable.deviceName'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      equipmentPosition: {
        key: 'equipmentPosition',
        title: this.columnHeaders['general.alarm.devicePosition'],
        headerText: this.columnHeaders['general.alarm.devicePosition'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      equipmentCategoryName: {
        key: 'equipmentCategoryName',
        title: this.columnHeaders['general.alarm.deviceCategory'],
        headerText: this.columnHeaders['general.alarm.deviceCategory'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      confirmTime: {
        key: 'confirmTime',
        title: this.columnHeaders['general.alarm.confirmTime'],
        headerText: this.columnHeaders['general.alarm.confirmTime'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      endTime: {
        key: 'endTime',
        title: this.columnHeaders['general.alarm.clearTime'],
        headerText: this.columnHeaders['general.alarm.clearTime'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      meanings: {
        key: 'meanings',
        title: this.columnHeaders['general.alarm.meanings'],
        headerText: this.columnHeaders['general.alarm.meanings'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      },
      eventValue: {
        key: 'eventValue',
        title: this.columnHeaders['general.alarm.occurValue'],
        headerText: this.columnHeaders['general.alarm.occurValue'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true
      }
    };

    if (this.isByteDance) {
      this.defaultColumnsMap.eventReasonType = {
        key: 'eventReasonType',
        title: this.columnHeaders['general.alarm.eventReasonTypeName'],
        headerText: this.columnHeaders['general.alarm.eventReasonTypeName'],
        filter: false,
        sort: false,
        canSort: true,
        visible: true,
        type: 'custom',
        valuePrepareFunction: (cell, row) => {
          if (row.eventReasonType && this.alarmReasonList && this.alarmReasonList.length > 0) {
            const reasonItem = this.alarmReasonList.find(item => item.id === row.eventReasonType);
            return reasonItem ? reasonItem.name : '';
          }
          return '';
        }
      };
    }

    // 创建两个独立的settings配置
    this.baseSettings = {
      mode: 'external',
      selectMode: 'multi',
      pager: {
        display: true,
        perPage: 15
      },
      noDataMessage: this.columnHeaders['general.common.noData'],
      actions: {
        columnTitle: '',
        add: false,
        edit: false,
        delete: false,
      },
      rowClassFunction: this.getRowClass.bind(this)
    };

    // 初始化时使用默认列配置
    this.settingsOneAndTwo = {
      ...this.baseSettings,
      columns: this.defaultColumns.map(col => this.defaultColumnsMap[col.key])
    };

    this.settingsThreeAndFour = {
      ...this.baseSettings,
      columns: this.defaultColumns.map(col => this.defaultColumnsMap[col.key])
    };
    this.initTableWidthConfig();

    // 获取定制列配置
    this.fetchCustomColumn();
  }
  getColDisplayName(field: string): string {
    switch (field) {
      case 'eventSeverity':
        return this.translateService.instant('general.alarm.severityName');
      case 'eventName':
        return this.translateService.instant('general.alarm.coreEventName');
      case 'startTime':
        return this.translateService.instant('general.alarm.birthTime');
      case 'equipmentName':
        return this.translateService.instant('componentDisplay.alarmTable.deviceName');
      case 'equipmentPosition':
        return this.translateService.instant('general.alarm.devicePosition');
      case 'equipmentCategoryName':
        return this.translateService.instant('general.alarm.deviceCategory');
      case 'confirmTime':
        return this.translateService.instant('general.alarm.confirmTime');
      case 'endTime':
        return this.translateService.instant('general.alarm.clearTime');
      case 'meanings':
        return this.translateService.instant('general.alarm.meanings');
      case 'eventValue':
        return this.translateService.instant('general.alarm.occurValue');
      case 'eventReasonType':
        return this.isByteDance ? this.translateService.instant('general.alarm.eventReasonTypeName') : '';
      default:
        return '';
    }
  }
  
  // 新增：初始化一二级告警表格列宽配置
  initTableWidthConfig() {
    this.oneandtwoTableWidthConfig = [
      { field: 'checkBox', width: '60px' },
      { field: 'eventSeverity', width: '100px' },
      { field: 'eventName', width: '150px' },
      { field: 'startTime', width: '150px' },
      { field: 'equipmentName', width: '150px' },
      { field: 'equipmentPosition', width: '150px' },
      { field: 'equipmentCategoryName', width: '150px' },
      { field: 'confirmTime', width: '150px' },
      { field: 'endTime', width: '150px' },
      { field: 'meanings', width: '150px' },
      { field: 'eventValue', width: '150px' }
    ];
    this.threeandfourTableWidthConfig = [
      { field: 'checkBox', width: '60px' },
      { field: 'eventSeverity', width: '100px' },
      { field: 'eventName', width: '150px' },
      { field: 'startTime', width: '150px' },
      { field: 'equipmentName', width: '150px' },
      { field: 'equipmentPosition', width: '150px' },
      { field: 'equipmentCategoryName', width: '150px' },
      { field: 'confirmTime', width: '150px' },
      { field: 'endTime', width: '150px' },
      { field: 'meanings', width: '150px' },
      { field: 'eventValue', width: '150px' }
    ];

    // 如果是字节跳动客户端，添加告警分类列
    if (this.isByteDance) {
      this.oneandtwoTableWidthConfig.push({ field: 'eventReasonType', width: '150px' });
      this.threeandfourTableWidthConfig.push({ field: 'eventReasonType', width: '150px' });
    }
  }

  fetchCustomColumn() {
    // 获取一级二级告警的定制列
    this.alarmlistService
      .getAFilter('activeeventfiltertemplates?filterType=' + this.ColumnCustomTypeOneAndTwo)
      .subscribe((res: any) => {
        if (res && res.length > 0) {
          const findData = res.find(item => item.activeEventFilterTemplateId === this.savedCustomColumnIdOneAndTwo);
          const data = findData ? findData : res[0];
          const savedColumns = JSON.parse(data.content);
          this.savedCustomColumnOneAndTwo = savedColumns;
          // 根据保存的配置重新构建列
          const newColumns = savedColumns
            .filter(col => col.visible)
            .map(col => this.defaultColumnsMap[col.key])
            .filter(col => col); // 过滤掉不存在的列
          this.settingsOneAndTwo = {
            ...this.baseSettings,
            columns: newColumns
          };
          this.savedCustomColumnIdOneAndTwo = data.activeEventFilterTemplateId;

          this.updateTableWidthConfig(1, savedColumns);
          // 刷新数据以应用新的列配置
          setTimeout(() => {
            this.getOneAndTwoData();
          }, 100);
        } else {
          // 如果没有保存的配置，使用默认配置
          this.initTableWidthConfig();
        }
      });

    // 获取三级四级告警的定制列
    this.alarmlistService
      .getAFilter('activeeventfiltertemplates?filterType=' + this.ColumnCustomTypeThreeAndFour)
      .subscribe((res: any) => {
        if (res && res.length > 0) {
          const findData = res.find(item => item.activeEventFilterTemplateId === this.savedCustomColumnIdThreeAndFour);
          const data = findData ? findData : res[0];
          const savedColumns = JSON.parse(data.content);
          this.savedCustomColumnThreeAndFour = savedColumns;
          // 根据保存的配置重新构建列
          const newColumns = savedColumns.filter(col => col)
            .filter(col => col.visible)
            .map(col => this.defaultColumnsMap[col.key]); // 过滤掉不存在的列

          this.settingsThreeAndFour = {
            ...this.baseSettings,
            columns: newColumns
          };
          this.savedCustomColumnIdThreeAndFour = data.activeEventFilterTemplateId;
          this.updateTableWidthConfig(2, savedColumns);
          setTimeout(() => {
            this.getThreeAndFourData();
          }, 100);
        } else {
          // 如果没有保存的配置，使用默认配置
          this.initTableWidthConfig();
        }
      });
  }

  saveCustomColumn(type: number, columns: any[]) {
    // 只保存必要的列信息
    const columnsToSave = columns.map(col => ({
      key: col.key,
      title: col.title,
      visible: col.visible,
      canSort: true,
      headerText: col.headerText,
      width: col.width || '150px' 
    }));

    const para = {
      personId: parseInt(this.sessionService.getPersonId()),
      templateName: type === 1 ? this.ColumnCustomTypeOneAndTwo : this.ColumnCustomTypeThreeAndFour,
      filterType: type === 1 ? this.ColumnCustomTypeOneAndTwo : this.ColumnCustomTypeThreeAndFour,
      content: JSON.stringify(columnsToSave),
      description: type === 1 ? 'Alarm Focus Panel One And Two Event Page Column Custom' : 'Alarm Focus Panel Three And Four Event Page Column Custom',
    };

    if (type === 1) {
      if (this.savedCustomColumnIdOneAndTwo) {
        para['activeEventFilterTemplateId'] = this.savedCustomColumnIdOneAndTwo;
        this.alarmlistService.updateTemplate(para).subscribe(ret => {
          this.fetchCustomColumn();
          // 刷新d-data-table数据
          setTimeout(() => {
            this.getOneAndTwoData();
          }, 200);
        });
      } else {
        this.alarmlistService.saveTemplate(para).subscribe((ret: any) => {
          this.savedCustomColumnIdOneAndTwo = ret && ret.activeEventFilterTemplateId;
          this.fetchCustomColumn();
          // 刷新d-data-table数据
          setTimeout(() => {
            this.getOneAndTwoData();
          }, 200);
        });
      }
    } else {
      if (this.savedCustomColumnIdThreeAndFour) {
        para['activeEventFilterTemplateId'] = this.savedCustomColumnIdThreeAndFour;
        this.alarmlistService.updateTemplate(para).subscribe(ret => {
          this.fetchCustomColumn();
        });
      } else {
        this.alarmlistService.saveTemplate(para).subscribe((ret: any) => {
          this.savedCustomColumnIdThreeAndFour = ret && ret.activeEventFilterTemplateId;
          this.fetchCustomColumn();
        });
      }
    }
  }

  customColumns(type: number) {
    const modal = this.ngbModal.open(CustomColumnsComponent, {
      container: 'app-alarm-focus-panel',
      backdrop: 'static',
    });

    // 获取当前表格的列配置
    let columns;

    if (type === 1) {
      if (this.savedCustomColumnOneAndTwo) {
        const missingColumns = this.defaultColumns.filter(defCol =>
          !this.savedCustomColumnOneAndTwo.some(savedCol => savedCol.key === defCol.key)
        ).map(defCol => ({
          ...defCol,
          visible: false
        }));
        columns = this.savedCustomColumnOneAndTwo.concat(missingColumns);
      } else {
        columns = this.defaultColumns;
      }
    } else {
      if (this.savedCustomColumnThreeAndFour) {
        const missingColumns = this.defaultColumns.filter(defCol =>
          !this.savedCustomColumnThreeAndFour.some(savedCol => savedCol.key === defCol.key)
        ).map(defCol => ({
          ...defCol,
          visible: false
        }));
        columns = this.savedCustomColumnThreeAndFour.concat(missingColumns);
      } else {
        columns = this.defaultColumns;
      }
    }
    

    modal.componentInstance.columns = columns;

    modal.result.then((config) => {
      if (config) {
        if (type === 1) {
          this.saveCustomColumn(1, config);
        } else {
          this.saveCustomColumn(2, config);
        }
      }
    });
  }
  getRowClass(rowData: any): string {
    if (rowData.data.confirmTime) {
      return 'confirmed-row'; // 存在则返回CSS类名
    } else {
      return 'unconfirmed-row'; // 不存在则返回另一个CSS类名
    }
  }

  // 新增：trackBy函数用于优化ngFor性能
  trackByFn(index: number, item: any): any {
    return item ? item.sequenceId || index : index;
  }
  ngOnInit() {
    this.userId = this.sessionService.getUserId();
    this.screenHeight = window.innerHeight;

    // 首先获取是否是字节跳动客户端
    this.authenticationService.getAuthenticationJson().subscribe(resJson => {
      this.isByteDance = resJson['bytedace.web.enable'];

      // 如果是字节跳动客户端，先获取告警分类列表
      if (this.isByteDance) {
        this.alarmService.getAlarmReasonList().subscribe(res => {
          this.alarmReasonList = res;
          // 获取告警分类列表后再设置表格配置
          this.setSetting();
          this.getData();
        });
      } else {
        // 不是字节跳动客户端，直接设置表格配置
        this.setSetting();
        this.getData();
      }
    });

    this.authenticationService.get('', 'coreeventseverities').subscribe(
      res => {
        res.forEach(s => {
          this.showAlarmSeverities[s.eventLevel] = s.displayColor;
        })
        this.alarmSeverities = res;
        this.alarmSeveritiesOneAndTwo = res.filter(s => s.eventLevel == 1 || s.eventLevel == 2);
        this.alarmSeveritiesThreeAndFour = res.filter(s => s.eventLevel == 3 || s.eventLevel == 4);
        this.alarmSeveritiesOneAndTwo.forEach(s => {
          s.count = '0';
        });
        this.alarmSeveritiesThreeAndFour.forEach(s => {
          s.count = '0';
        });

      }
    );

    this.intervalId = setInterval(() => {
      this.getOneAndTwoData();
      this.getThreeAndFourData();
      this.getAlarmCount();
    }, 3000);

  }
  ngAfterViewInit() {
		this.resizeObserver = new ResizeObserver(entries => {
			for (const entry of entries) {
				this.tableHeight = entry.contentRect.height + 'px';
				this.cdr.detectChanges();
			}
		});
		this.resizeObserver.observe(document.querySelector('#tableFrameFocus'));
	}
  getAlarmCount(): void {
    this.alarmlistService.getFilterStaticsEvent(undefined).subscribe((res: any) => {
      if (res && this.alarmSeveritiesOneAndTwo && this.alarmSeveritiesThreeAndFour) {
        this.alarmSeveritiesOneAndTwo.forEach(s => {
          if (res[s.eventLevel.toString()]) {
            s.count = res[s.eventLevel.toString()].toString();
          }
        });
        this.alarmSeveritiesThreeAndFour.forEach(s => {
          if (res[s.eventLevel.toString()]) {
            s.count = res[s.eventLevel.toString()].toString();
          }
        });
      }
    });
  }

  //表格列宽更改
  onResize($event, field) {
    const func = tableResizeFunc(this.cols, this.ele);
    if ($event.width < this.minWidth) {
      $event.width = this.minWidth;
    }
    func($event, field);
  }

  // 新增：一二级告警表格列宽拖拽处理
  onOneAndTwoResize($event, field) {
    const func = tableResizeFunc(this.oneandtwoTableWidthConfig, this.ele);
    if ($event.width < this.minWidth) {
      $event.width = this.minWidth;
    }
    func($event, field);

    // 保存列宽配置
    this.saveColumnWidths(1);
  }
  onThreeAndFourResize($event, field) {
    const func = tableResizeFunc(this.threeandfourTableWidthConfig, this.ele);
    if ($event.width < this.minWidth) {
      $event.width = this.minWidth;
    }
    func($event, field);

    // 保存列宽配置
    this.saveColumnWidths(2);
  }

  // 新增：更新一二级告警表格的tableWidthConfig
  updateTableWidthConfig(type:number, savedColumns: any[]) {
    if (savedColumns && savedColumns.length > 0) {
      if (type == 1) {
      this.oneandtwoTableWidthConfig = savedColumns
        .filter(col => col.visible)
        .map(col => ({
          field: col.key,
          width: col.width || '150px'
        }));
        if (!this.oneandtwoTableWidthConfig.some(col => col.field === 'checkBox')) {
          this.oneandtwoTableWidthConfig.unshift({ field: 'checkBox', width: '60px' });
        }
      }
    else if (type == 2) {
      this.threeandfourTableWidthConfig = savedColumns
        .filter(col => col.visible)
        .map(col => ({
          field: col.key,
          width: col.width || '150px'
        }));
        if (!this.threeandfourTableWidthConfig.some(col => col.field === 'checkBox')) {
          this.threeandfourTableWidthConfig.unshift({ field: 'checkBox', width: '60px' });
        }
      }
    }
  }


  // 新增：保存一二级告警表格列宽配置
  saveColumnWidths(type:number) {
    if (type == 1) {
      if (this.oneandtwoTableWidthConfig) {
        // 更新保存的列配置中的宽度信息
        const originColumns = this.savedCustomColumnOneAndTwo ? this.savedCustomColumnOneAndTwo : this.defaultColumns;
        const updatedColumns = originColumns.map(col => {
        const widthConfig = this.oneandtwoTableWidthConfig.find(w => w.field === col.key);
        return {
          ...col,
          width: widthConfig ? widthConfig.width : col.width
        };
      });
      // 保存更新后的配置
        this.saveCustomColumn(1, updatedColumns);
      }
    }
    else if (type == 2) {
      if (this.threeandfourTableWidthConfig) {
        const originColumns = this.savedCustomColumnThreeAndFour ? this.savedCustomColumnThreeAndFour : this.defaultColumns;
        const updatedColumns = originColumns.map(col => {
          const widthConfig = this.threeandfourTableWidthConfig.find(w => w.field === col.key);
          return {
            ...col,
            width: widthConfig ? widthConfig.width : col.width
          };
        });
        this.saveCustomColumn(2, updatedColumns);
      }
    }
  }

  ngOnDestroy() {
    //清空定时器
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  private saveSelectedState(type: number) {
    if (type == 1) {
      const selectedIds = this.oneandtwoDisplayData
        .filter(item => item.selected)
        .map(item => item.sequenceId);
        return selectedIds;
    }
    else if (type == 2) {
      const selectedIds = this.threeandfourDisplayData
        .filter(item => item.selected)
        .map(item => item.sequenceId);
        return selectedIds;
    }
    
  }

  private restoreSelectedState(type: number, selectedIds: string[]) {
    if (selectedIds && selectedIds.length > 0) {
      if (type == 1) {
          this.oneandtwoDisplayData.forEach(item => {
          item.selected = selectedIds.includes(item.sequenceId);
        });
      }
      else if (type == 2) {
        this.threeandfourDisplayData.forEach(item => {
          item.selected = selectedIds.includes(item.sequenceId);
        });
      }
      this.updateSelectedItems();
    }
  }

}
