import { catchError, mergeMap, tap, timeout } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Observable, of, throwError, TimeoutError } from 'rxjs';
import {
	HttpEvent,
	HttpRequest,
	HttpInterceptor,
	HttpHandler,
	HttpErrorResponse,
	HttpResponseBase,
	HttpResponse,
} from '@angular/common/http';
import { HeaderParamsInterceptorService } from './headerParamsInterceptor.service';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { SessionService } from '@app/core/services/session.service';
import * as CryptoJS from 'crypto-js';

@Injectable()
export class HeaderParamsInterceptor implements HttpInterceptor {
	constructor(
		private httpService: HeaderParamsInterceptorService,
		private translateService: TranslateService,
		private sessionService: SessionService,
		private router: Router
	) { }

	intercept(
		req: HttpRequest<any>,
		next: HttpHand<PERSON>
	): Observable<HttpEvent<any>> {
		const _reqHeader = req.headers;
		const token = this.httpService.getToken();
		const langKey = this.httpService.getLang();
		const baseUrl = this.httpService.getBaseUrl();
		const reqHeaderTypeStr = _reqHeader.get('type')
			? _reqHeader.get('type').toString().toUpperCase()
			: '';
		let setJson;
		req = this.encryptSystemConfigs(req);
		// 判断是否需要添加请求接口前缀 api
		const url = !this.httpService.ignoreFirstUrl(req.urlWithParams)
			? `${baseUrl}/${req.url}`
			: `${req.url}`;
		const antiReplayAttack = this.httpService.getAntiReplayAttack(req);
		const customAuth = this.sessionService.getCustomAuth() ? this.sessionService.getCustomAuth() : null;
		const defaultHeader: any = {
			setHeaders: {
				Accept: _reqHeader.get('Accept') || '*/*',
				'Content-Type': _reqHeader.get('Content-Type') || 'application/json',
				'Accept-Language': _reqHeader.get('Accept-Language') || langKey,
				lan: this.translateService.currentLang === 'en' ? 'en-US' : 'zh-CN',
				TimeStamp: `${antiReplayAttack.timeStamp}`,
				Sign: `${antiReplayAttack.sign}`,
				Nonce: `${antiReplayAttack.nonce}`,
			},
			url: `${url}`,
		};

		if (!customAuth) {
			if (_reqHeader.get('Authorization') || token) {
				defaultHeader.setHeaders = {
					...defaultHeader.setHeaders,
					Authorization: _reqHeader.get('Authorization') || token,
				};
			}
		} else {
			if (_reqHeader.get(customAuth) || token) {
				defaultHeader.setHeaders = {
					...defaultHeader.setHeaders,
				};
				defaultHeader.setHeaders[customAuth] = _reqHeader.get(customAuth) || token;
			}
		}

		const headerJson = req.clone(defaultHeader);

		let headerOnlyAuthHeader;

		if (customAuth) {
			headerOnlyAuthHeader = {
				'Accept-Language': _reqHeader.get('Accept-Language') || langKey,
				TimeStamp: `${antiReplayAttack.timeStamp}`,
				Sign: `${antiReplayAttack.sign}`,
				Nonce: `${antiReplayAttack.nonce}`,
			}
			headerOnlyAuthHeader[customAuth] = _reqHeader.get(customAuth) || token;
		} else {
			headerOnlyAuthHeader = {
				Authorization: _reqHeader.get('Authorization') || token,
				'Accept-Language': _reqHeader.get('Accept-Language') || langKey,
				TimeStamp: `${antiReplayAttack.timeStamp}`,
				Sign: `${antiReplayAttack.sign}`,
				Nonce: `${antiReplayAttack.nonce}`,
			}
		}

		const headerOnlyAuth = req.clone({
			setHeaders: headerOnlyAuthHeader,
			url: `${url}`,
		});

		const headerOntAuth = req.clone({
			setHeaders: {
				'Accept-Language': _reqHeader.get('Accept-Language') || langKey,
				TimeStamp: `${antiReplayAttack.timeStamp}`,
				Sign: `${antiReplayAttack.sign}`,
				Nonce: `${antiReplayAttack.nonce}`,
			},
			url: `${url}`,
		});

		switch (reqHeaderTypeStr) {
			case 'ONLY_AUTH':
				setJson = headerOnlyAuth;
				break;
			case 'NOT_AUTH':
				setJson = headerOntAuth;
				break;
			default:
				setJson = headerJson;
		}

		/*return next.handle(setJson).pipe(
			tap(
				() => undefined,
				(err: any) => {
					if (err instanceof HttpErrorResponse) {
						if (err.status === 404) {
							return throwError('err');
						} else if (err.status === 500) {
							return throwError('err');
						} else if (err.status === 401) {
							//logout when authorize failed
							sessionStorage.clear();
							this.router.navigateByUrl('/signin');
						}
					}
				}
			)
		);*/
		const timeoutFromHeader = parseInt(req.headers.get('Timeout') || '', 10);
		
		let timeoutInterval: number;
		if (req.url.includes('reportdatas')) {
		  timeoutInterval = 90000;
		} else {
		  timeoutInterval = isNaN(timeoutFromHeader) ? 30000 : timeoutFromHeader;
		}
		return next.handle(setJson).pipe(
			timeout(timeoutInterval),
			mergeMap((event: HttpEvent<any>) => {
				return event instanceof HttpResponseBase
					? this.handleData(req, event)
					: of(event);
			}),
			catchError((err) => {
				//   if (err instanceof TimeoutError && !quiet) {
				// 	this.injector.get(MessageService).error(err.message);
				//   } else if (err instanceof HttpResponse) {
				// 	if (err.body?.errcode && !quiet) {
				// 	  this.injector
				// 		.get(MessageService)
				// 		.error(`error.${err.body.errcode}`);
				// 	}
				//   } else 
				if (err instanceof HttpResponseBase) {
					this.handleData(req, err);
				}
				return throwError(err);
			})
		);
	}

	private apiRegexp = /(")[./]*api\/files\//g;
	private assetsRegexp = /(")[./]*\/assets\//g;
	handleResPath(url: string, data) {
		try {
			let dataJson = JSON.stringify(data).replace(this.apiRegexp, '"./api/files/');
			dataJson = dataJson.replace(this.assetsRegexp, '"./assets/');
			return JSON.parse(dataJson);
		} catch (error) {
			console.error('handleResPath function error', 'url:' + url, 'data' + data, 'msg:' + error);
			return data;
		}
	}

	encryptSystemConfigs(req: HttpRequest<any>) {
		if (req.method == 'GET' && req.url.toLocaleLowerCase().includes('systemconfigs')) {
			// 1. 解析原始 URL
			let newUrl = new URL(req.urlWithParams, window.location.origin);

			// 2. 添加新的查询参数
			newUrl.searchParams.set('encrypt', 'true');  // ✅ 你可以根据需要动态拼接

			// 3. 克隆请求并替换 URL
			let modifiedReq = req.clone({
				url: req.url.split('?')[0] + '?' + newUrl.toString().split('?')[1]
			});
			req = modifiedReq;
		}
		return req
	}

	decryptSystemConfigs(req: HttpRequest<any>, event: HttpResponseBase) {
		if (req.method == 'GET' && req.url.toLocaleLowerCase().includes('systemconfigs')) {
			let res = event['body'].data;
			const cfg = {
				mode: CryptoJS.mode.ECB,
				padding: CryptoJS.pad.Pkcs7
			};
			const key = "lmGMlJPZQBpcx3Wf";
			const result = CryptoJS.AES.decrypt(res, CryptoJS.enc.Utf8.parse(key), cfg);
			const str = result.toString(CryptoJS.enc.Utf8);
			// 后端查询单个参数，数据库中不存在时会返回一个编码的空数组，当解码后时空数组则赋值为空字符串避免 !systemconfig 这种判断无效
			let decryptedConfigs = str ? JSON.parse(str) : '';
			decryptedConfigs = Array.isArray(decryptedConfigs) && decryptedConfigs.length === 0 ? '' : decryptedConfigs;
			event['body'].data = decryptedConfigs;
		}
		return event['body'].data
	}

	handleData(req: HttpRequest<any>, event: HttpResponseBase): Observable<any> {
		switch (event.status) {
			case 200: {
				if (event instanceof HttpResponse) {
					const body = event.body;
					if (body && !body.state && body.hasOwnProperty('err_code')) {
						return throwError(event);
					} else if (body && body.state) {
						body.data = this.decryptSystemConfigs(req, event);
						body.data = this.handleResPath(event.url || '', body.data);
						return of(
							new HttpResponse<any>({
								body: body.data,
								headers: event.headers,
								status: event.status,
								statusText: event.statusText,
								url: event.url || undefined,
							})
						);
					}
				}
				break;
			}
			case 401: {
				if (this.sessionService.needLogoutWhenNoAuth()) {
					if (window.location.href.indexOf('/signin') == -1) {
						this.httpService.webClientLog('请求:' + event.url + ' 出现401错误。', '在页面:' + window.location.href + ' 已退出');
						sessionStorage.clear();
						window.location.reload();
					}
					// this.router.navigateByUrl('/signin').then(
					// 	s => {
					// if (window.location.href.indexOf('/signin') == -1)
					// 	window.location.reload();
					// }
					// );
				} else {
					if (window.location.href.indexOf('/signin') == -1) {
						this.httpService.webClientLog('请求:' + event.url + ' 出现401错误。', '在页面:' + window.location.href + ' 前端设置为该用户不退出');
					}
				}
				break;
			}
			default:
				if (event instanceof HttpErrorResponse) {
					// this.injector.get(MessageService).error(event.message);
					// this.injector.get(MessageService).error('error.999999');
					return throwError(event);
				}
				break;
		}
		return of(event);
	}
}
