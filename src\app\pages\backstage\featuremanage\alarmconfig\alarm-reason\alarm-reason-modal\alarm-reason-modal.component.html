<div class="modal-header">
    <h4 class="modal-title" id="modal-basic-title">
        {{ 'featuremanage.alarmconfig.eventReason' | translate }}
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="close()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
    <div class="arm_list">
        <div class="arm_line">
            <div class="arm_title">
                {{ 'featuremanage.alarmconfig.name' | translate }}
            </div>
            <div class="arm_value">
                <input type="text" class="form-control" [(ngModel)]="data.name">
            </div>
        </div>
        <div class="arm_line">
            <div class="arm_title">
                {{ 'featuremanage.alarmconfig.description' | translate }}
            </div>
            <div class="arm_value">
                <input type="text" class="form-control" [(ngModel)]="data.description">
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="close()">
        {{ 's2.devicemonitor.cancel' | translate }}
    </button>
    <button type="button" class="btn btn-primary" (click)="close(data)">
        {{ 'general.common.confirm' | translate }}
    </button>
</div>