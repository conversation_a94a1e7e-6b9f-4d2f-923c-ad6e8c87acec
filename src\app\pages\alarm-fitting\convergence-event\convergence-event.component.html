<div class="convergence-event-container">
    <div class="convergence-event-content">
        <form class="
        form-inline-header
        d-flex
        justify-content-between
        align-items-baseline
      ">
            <div class="text-left">
                <button class="btn btn-primary" type="button" (click)="batchConfirm()">
                    <span>{{ 'general.common.batchProcessing' | translate }}</span>
                </button>
            </div>
            <div class="right-content">
                <ng-container *ngTemplateOutlet="rangePicker"></ng-container>
                <button class="btn btn-primary ml-1 mr-1" (click)="searchData()">
                    {{ 'general.common.find' | translate }}
                </button>
            </div>
        </form>

        <ngx-datatable #table [selected]="selected" [rowIdentity]="setRowIdentity" [selectionType]="'checkbox'"
            [selectAllRowsOnPage]="false" [rows]="rows" [columns]="columns" [columnMode]="'force'" [headerHeight]="38"
            [footerHeight]="50" [rowHeight]="'36'" [externalPaging]="true" [messages]="messages"
            [count]="page.totalElements" [offset]="page.pageNumber" [limit]="page.size" (page)="setPage($event)"
            (activate)="onActivate($event)" (select)="onSelect($event)">
        </ngx-datatable>
    </div>
    <!-- 收敛事件时间线 -->
    <div class="event-timeline-container">
        <app-phoenix-panel [panelClass]="'event-timeline-content'"
            [title]="(eventTitle || 'alarmFitting.convergenceEvent.eventTimeLine')">
            <app-phoenix-panel [panelClass]="'content-type'" [title]="'alarmFitting.convergenceEvent.eventLevelMatrix'">
                <div class="alarm-level-matrix-content" echarts [initOpts]="{ renderer: 'svg' }" [options]="chartOption" [autoResize]="true"></div>
            </app-phoenix-panel>
            <app-phoenix-panel [panelClass]="'content-type'"
                [title]="'alarmFitting.convergenceEvent.eventDeviceCategory'">
                <div class="alarm-level-matrix-content" echarts [initOpts]="{ renderer: 'svg' }" [options]="pieChartOption" [autoResize]="true"></div>
            </app-phoenix-panel>
            <app-phoenix-panel [panelClass]="'content-type'" [title]="'alarmFitting.convergenceEvent.eventList'">
                <ul class="event-list-content phoenix-ul-list">
                    <li class="phoenix-list-line event-list-line" *ngFor="let event of eventList">
                        <span class="line-title">{{ event.birthTime }}</span>
                        <span class="line-value">{{ event.equipmentName }}:{{ event.eventName }}--{{ event.occurRemark }}</span>
                    </li>
                </ul>
            </app-phoenix-panel>
        </app-phoenix-panel>
    </div>
</div>

<ng-template #rangePicker>
    <div class="input-group bs-daterangepicker">
        <label class="control-label">{{'s2.shieldevent.starttime' | translate}}</label>
        <div class="input-group">
          <input
            class="form-control"
            name="startTime"
            type="text"
            readonly
            [(ngModel)]="startTime"
            placeholder="{{ 'general.mask.startTime' | translate }}"
            (applyDaterangepicker)="selectTime($event, 'startTime')"
            daterangepicker
            [options]="options"
            [disabled]="dateDisabled"
          />
        </div>
        <label class="control-label">{{'s2.shieldevent.endtime' | translate}}</label>
        <div class="input-group">
          <input
            class="form-control"
            name="endTime"
            type="text"
            readonly
            [(ngModel)]="endTime"
            placeholder="{{ 'general.mask.endTime' | translate }}"
            (applyDaterangepicker)="selectTime($event, 'endTime')"
            daterangepicker
            [options]="options"
            [disabled]="dateDisabled"
          />
        </div>
    </div>
</ng-template>

<ng-template #eventName let-row="row" let-value="value">
    <span
        title="{{value}},{{ 'alarmFitting.convergenceEvent.convergenceCountMsg' | translate:{ count: row.convergenceCount } }}">
        {{value}},{{ 'alarmFitting.convergenceEvent.convergenceCountMsg' | translate:{ count: row.convergenceCount } }}
    </span>
</ng-template>

<ng-template #status let-value="value">
    <span [ngClass]="'status_' + value"
        title="{{ 'alarmFitting.convergenceEvent.' + statusMap.get(value)  | translate }}">
        {{ 'alarmFitting.convergenceEvent.' + statusMap.get(value) | translate }}
    </span>
</ng-template>

<ng-template #impactRange let-row="row">
    <app-impact-range-analysis [rowData]="row" (open)="openTopo(row)"></app-impact-range-analysis>
</ng-template>

<ng-template #export let-row="row">
    <span title="{{'general.common.export' | translate}}" class="iconfont icon-export"
        (click)="exportExcel(row)"></span>
</ng-template>