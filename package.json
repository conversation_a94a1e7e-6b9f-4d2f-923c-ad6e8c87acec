{"name": "phoenix", "version": "0.0.1", "license": "MIT", "scripts": {"postinstall": "ngcc", "ng": "ng", "rimraf": "<PERSON><PERSON><PERSON>", "start": "npm-run-all --parallel mockServer serv ", "build-with-dll": "webpack --config webpack.vendor.config", "build-with-stats": "npm run clean:dist && ng build --stats-json", "postbuild-with-stats": "webpack-bundle-analyzer ./build/static/stats.json", "prebuild:prod": "npm run clean:dist", "build:prod": "node --max_old_space_size=8096 ./node_modules/@angular/cli/bin/ng build --configuration='production' --build-optimizer=false > build_log.txt 2>&1", "postbuild:prod": "gzipper compress --threshold 307200 ./build/static", "test": "ng test", "lint": "eslint src --ext .js,.ts -f table > ./lint/lint.txt", "lint-fix": "eslint src --fix --ext .js,.ts", "e2e": "ng e2e", "clean:dist": "npm run rimraf -- ./build/static", "serv": "ng serve --port 4200 --proxy-config proxy.conf.json --open --watch", "dev": "ng cache clean && node --max_old_space_size=8096 ./node_modules/@angular/cli/bin/ng serve --host 0.0.0.0 --port 4201 --proxy-config proxy.conf.json --open --watch", "fix-memory-limit": "cross-env LIMIT=8096 increase-memory-limit"}, "browser": {"fs": false, "path": false, "os": false}, "private": true, "dependencies": {"@angular-devkit/core": "^14.2.10", "@angular/animations": "14.3.0", "@angular/cdk": "^13.3.9", "@angular/common": "14.3.0", "@angular/compiler": "14.3.0", "@angular/core": "14.3.0", "@angular/forms": "14.3.0", "@angular/localize": "^16.2.12", "@angular/platform-browser": "14.3.0", "@angular/platform-browser-dynamic": "14.3.0", "@angular/router": "14.3.0", "@circlon/angular-tree-component": "^11.0.4", "@ng-bootstrap/ng-bootstrap": "^5.0.0", "@ng-select/ng-select": "^9.1.0", "@ngx-loading-bar/http-client": "^2.2.0", "@ngx-translate/core": "^9.0.0", "@ngx-translate/http-loader": "^3.0.1", "@stomp/ng2-stompjs": "^8.0.0", "@sweetalert2/ngx-sweetalert2": "^7.3.0", "@swimlane/ngx-datatable": "^20.1.0", "@techiediaries/ngx-qrcode": "^9.1.0", "@types/crypto-js": "^4.0.2", "@types/d3-sankey": "^0.11.0", "@types/filesaver": "0.0.30", "@types/lodash": "4.14.186", "@types/sockjs-client": "^1.5.1", "angular-datepicker": "^1.0.5", "angular-uuid": "0.0.4", "ap-angular-fullcalendar": "^1.7.10", "apollo-boost": "^0.4.4", "bootstrap": "4.0.0-alpha.6", "browserify": "^17.0.1", "codemirror": "^5.58.2", "core-js": "^2.4.1", "crypto-js": "^4.1.1", "d3": "^5.11.0", "d3-hexbin": "^0.2.2", "d3-ng2-service": "^2.2.0", "d3-sankey": "^0.12.3", "d3-sankey-circular": "^0.34.0", "d3-tip": "^0.7.1", "default-passive-events": "^2.0.0", "devextreme": "^17.2.5", "devextreme-angular": "^17.2.5", "e-ngx-print": "^1.2.0", "easy-pie-chart": "2.1.7", "echarts": "^5.5.0", "echarts-gl": "^2.0.9", "echarts-liquidfill": "^3.1.0", "es6-promise": "^4.2.4", "exceljs": "^4.4.0", "face-api.js": "^0.10.0", "file-saver": "^1.3.8", "font-awesome": "^4.7.0", "heatmap.js": "^2.0.5", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "ionicons": "2.0.1", "jquery": "3.6.1", "jquery-slimscroll": "^1.3.8", "jqueryui": "^1.11.1", "jspdf": "^3.0.1", "jspdf-customfonts": "0.0.3-rc.7", "jszip": "^3.2.0", "keycloak-angular": "^6.1.0", "keycloak-js": "^19.0.1", "kjua": "0.1.2", "linqts": "^1.8.3", "lodash": "^4.17.4", "merge-stream": "^1.0.1", "moment": "^2.18.1", "mpegts.js": "^1.8.0", "ng-canvas-gauges": "^6.0.4", "ng-devui": "^14.1.0", "ng-multiselect-dropdown": "^0.2.10", "ng-pick-datetime": "~5.0.0-beta.11", "ng-select": "^1.0.0-rc.3", "ng-zorro-antd": "^14.3.0", "ng2-daterangepicker": "^2.0.12", "ng2-file-upload": "^1.3.0", "ng2-ion-range-slider": "^1.0.3", "ng2-select": "^2.0.0", "ng2-smart-table": "^1.7.2", "ng2-tree": "^2.0.0-rc.4", "ngx-bootstrap": "^7.1.2", "ngx-color": "^4.1.1", "ngx-color-picker": "^14.0.0", "ngx-echarts": "^5.2.2", "ngx-feature-toggle": "5.2.5", "ngx-kjua": "1.3.2", "ngx-pagination": "^3.2.1", "ngx-select-ex": "^3.5.2-ng4", "ngx-toastr": "16.1.1", "ngx-tree-select": "^0.15.0", "node-schedule": "^2.1.1", "orgchart.js": "0.0.4", "os": "^0.1.1", "pdfmake": "^0.1.36", "popper.js": "^1.12.3", "remove-drag-ghosting": "^1.0.3", "rxjs": "6.6.6", "rxjs-tslint": "^0.1.8", "sha1": "^1.1.1", "signals": "^1.0.0", "sockjs-client": "^1.6.1", "stream": "0.0.2", "sweetalert2": "^9.7.0", "tether": "^1.4.0", "three": "^0.119.1", "three.meshline": "^1.3.0", "tslib": "^2.0.0", "typedoc-plugin-external-module-name": "^1.1.3", "uglify-es": "^3.3.4", "uglify-js": "^3.4.9", "uuid": "^3.1.0", "vinyl-buffer": "^1.0.1", "vis-timeline": "^7.7.2", "visibility-polygon": "^1.1.0", "weboot-plugin": "^1.1.5", "xlsx": "^0.17.0", "xml2js": "^0.4.19", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/custom-webpack": "^8.4.1", "@angular-devkit/architect": "^0.1402.10", "@angular-devkit/build-angular": "^14.2.13", "@angular/cli": "^14.2.10", "@angular/compiler-cli": "^14.2.12", "@aspnet/signalr": "^1.1.4", "@babel/core": "^7.13.10", "@types/heatmap.js": "^2.0.36", "@types/jasmine": "~3.6.0", "@types/jquery": "2.0.41", "@types/jquery.slimscroll": "1.3.30", "@types/lodash": "4.14.186", "@types/node": "^12.11.1", "@types/signals": "^1.0.1", "@types/webpack": "^5.28.0", "@types/webpack-bundle-analyzer": "^4.4.0", "@typescript-eslint/eslint-plugin": "^4.29.0", "@typescript-eslint/parser": "^4.29.0", "codelyzer": "^6.0.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "guid-typescript": "^1.0.9", "gzipper": "^4.5.0", "increase-memory-limit": "^1.0.6", "install": "^0.12.2", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.1", "karma-chrome-launcher": "~3.1.0", "karma-cli": "~1.0.1", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "ng2-completer": "^9.0.1", "sass": "^1.87.0", "npm-run-all": "^4.0.2", "prettier": "^2.3.2", "protractor": "~7.0.0", "resize-observer-polyfill": "^1.5.1", "rimraf": "2.6.1", "rxjs-compat": "^6.6.7", "sass-loader": "^7.3.1", "ts-node": "~8.10.2", "typedoc": "^0.14.2", "typedoc-plugin-sourcefile-url": "^1.0.3", "typescript": "~4.6.4", "webpack-bundle-analyzer": "^3.9.0", "webpack-cli": "^4.7.2"}, "description": "phoenix web 项目", "main": ".eslintrc.js", "repository": {"type": "git", "url": "git@*************:phoenix/phoenix-web.git"}, "keywords": [], "author": ""}