import { forkJoin, Observable } from 'rxjs';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CustomBasicComponent } from '../../../../basic/custom-basic/custom-basic.component';
import { CompEmitService } from '@app/@hmi/providers/comp-emit.service';
import { TranslateService } from '@ngx-translate/core';
import { BaseCRUDService } from '@communal/providers/baseCRUDService/baseCRUDService';
import { DateUtil } from '@communal/utils/DateUtil';

@Component({
    selector: 'app-history-indicator-aggregate',
    templateUrl: './history-indicator-aggregate.component.html',
    styleUrls: ['./history-indicator-aggregate.component.scss']
})
export class HistoryIndicatorAggregateComponent extends CustomBasicComponent implements OnInit {
    data: any;
    theme: string;
    baseUrl = 'historycomplexindexs?';
    paramsUrl: any;
    aggregateType: string;
    stratDate: any;
    endDate: any;
    currentProgress: number;
    backgroundColor: any;
    indicatorValHeight: any;
    indicatorWidth: any;
    sumData: any;
    timeID: number;
    _isActive: boolean;

    constructor(
        private activatedRoute: ActivatedRoute,
        private emitService: CompEmitService,
        private baseService: BaseCRUDService,
        private translate: TranslateService,
        protected router: Router
    ) {
        super(activatedRoute, emitService);
    }

    ngOnInit() {
        this.initBasicData();
        if (this.data['complexIndexId'] !== undefined && this.data['complexIndexId'] !== '') {
            this.initData();
        }
        this.backgroundColor = '#FFFFFF';
        this.currentProgress = 0;
        this.sumData = 0;

        //设置进度条宽度和缩进
        this.indicatorValHeight = (parseInt(this.expandUnit('height')) - this.data['indicatorSize'] - this.data['progressbarHeight']) / 2;
        this.indicatorWidth = parseInt(this.expandUnit('width')) - this.data['iconTypeFontsize'] - 10;
    }

    initData() {
        this._isActive = true;
        this.theme = localStorage.getItem("theme");

        switch (this.data.aggregateType) {
            case "max": {
                this.aggregateType = this.translate.instant('component.historyIndicatorAggregate.max');
                break;
            }
            case "min": {
                this.aggregateType = this.translate.instant('component.historyIndicatorAggregate.min');
                break;
            }
            case "avg": {
                this.aggregateType = this.translate.instant('component.historyIndicatorAggregate.avg');
                break;
            }
            case "sum": {
                this.aggregateType = this.translate.instant('component.historyIndicatorAggregate.sum');
                break;
            }
        }

        //拼装日期
        this.stratDate = this.getStartDate();
        this.endDate = this.getEndDate();

        //拼装url
        this.paramsUrl =
            'complexIndexIds=' +
            this.data['complexIndexId'] +
            '&startTime=' +
            this.stratDate +
            '&endTime=' +
            this.endDate;

        //请求数据
        if(this.data['dataSource'] == 1) {
            this.getEnergyIndicatorData();
        } else {
            this.getIndicatorData();
        }
    }

    getStartDate() {
        if (this.data['queryType'] == 0) {
            switch (this.data['queryUnit']) {
                case "y": {
                    let year = new Date().getFullYear();

                    return year + "-01-01 00:00:00";
                }
                case "M": {
                    let today = new Date();
                    let firstDay = DateUtil.firstDayOfMonth(today);
                    let date = DateUtil.dateToDayLongString(firstDay);

                    return date;
                }
                case "d": {
                    let today = new Date();
                    let date = DateUtil.shortDateToString(today);
                    date = date + " 00:00:00"

                    return date;
                }
                case "h": {
                    let today = new Date();
                    let date = DateUtil.shortDateToString(today);
                    let h = today.getHours();
                    let hnow = '';
                    if (h < 10) {
                        hnow = '0' + h.toString();
                    } else {
                        hnow = h.toString();
                    }
                    return date + " " + hnow + ":00:00";
                }
                default: {
                    let today = new Date();
                    let date = DateUtil.dateToDayLongString(today);
                    return date;
                }
            }
        } else {
            let sum = 0;
            let date: any;
            switch (this.data['queryUnit']) {
                case "M": {
                    sum = 30 * this.data['queryRange'];
                    date = DateUtil.latestSomeDays(sum);
                    return date[0].startTime + " " + "00:00:00";
                }
                case "d": {
                    sum = this.data['queryRange'];
                    date = DateUtil.latestSomeDays(sum);
                    return date[0].startTime + " " + "00:00:00";
                }
                case "h": {
                    sum = this.data['queryRange'];
                    date = DateUtil.latestSomeHours(sum);
                    return date[0].startTime;
                }
                default: {
                    let today = new Date();
                    let date = DateUtil.dateToDayLongString(today);
                    return date;
                }
            }
        }
    }

    getEndDate() {
        let currentDate = DateUtil.currentDateToString();
        return currentDate;
    }

    expandCustomUnit(param, other?: any) {
        let margin = 42;
        if (other) {
            margin = Number(other);
        }
        let val = 100;
        val = this.style[param] - margin || 100;
        return val + 'px';
    }

    expandCustomHeight() {
        return (parseInt(this.expandUnit('height')) - this.data['indicatorSize'] - this.data['progressbarHeight'] - 10) / 2;
    }

    getIndicatorData() {
        let newSum = 0;
        forkJoin([
            this.baseService.getAll(this.baseUrl + this.paramsUrl),
        ]).subscribe(
            (res) => {
                if (res && res[0]) {
                    if (res[0][this.data.complexIndexId].length === 0) {
                        this.currentProgress = 0;
                    } else {
                        res[0][this.data.complexIndexId].forEach((item) => {
                            let data = parseFloat(Number(item.indexValue).toFixed(1));
                            newSum += data;
                        });
                        this.sumData = parseFloat(Number(newSum).toFixed(1));
                        this.currentProgress = parseFloat((this.sumData / parseInt(this.data['ratedValue']) * 100).toFixed(2));
                    }
                    if (this._isActive) {
                        if (this.timeID) {
                            window.clearTimeout(this.timeID);
                        }
                        this.timeID = window.setTimeout(() => {
                            this.initData();
                        }, this.data['refresh'] ? this.data['refresh'] * 1000 : 30 * 1000);
                    }
                }
            },
            (error) => {
                if (this._isActive) {
                    if (this.timeID) {
                        window.clearTimeout(this.timeID);
                        this.timeID = null;
                    }
                    this.timeID = window.setTimeout(() => {
                        this.initData();
                    }, 30 * 1000);
                }
            }
        );
    }

    getEnergyIndicatorData() {
        const options = {
            complexIndexIds: this.data['complexIndexList'],
            tableType: this.data['queryUnit'],
            startTime: this.stratDate,
            endTime: this.endDate
        }

        this.baseService.get(options, 'api/energy/energyapi/historycomplexindexs').subscribe(
            res => {
                if (res) {
                    let newSum = 0;
                    if (res[this.data.complexIndexId].length === 0) {
                        this.currentProgress = 0;
                    } else {
                        res[this.data.complexIndexId].forEach((item) => {
                            let data = parseFloat(Number(item.indexValue).toFixed(1));
                            newSum += data;
                        });
                        this.sumData = parseFloat(Number(newSum).toFixed(1));
                        this.currentProgress = parseFloat((this.sumData / parseInt(this.data['ratedValue']) * 100).toFixed(2));
                    }
                    if (this._isActive) {
                        if (this.timeID) {
                            window.clearTimeout(this.timeID);
                        }
                        this.timeID = window.setTimeout(() => {
                            this.initData();
                        }, 30 * 1000);
                    }
                }
                if (this._isActive) {
                    if (this.timeID) {
                        window.clearTimeout(this.timeID);
                    }
                    this.timeID = window.setTimeout(() => {
                        this.initData();
                    }, 30 * 1000);
                }
            },
            error => {
                if (this._isActive) {
                    if (this.timeID) {
                        window.clearTimeout(this.timeID);
                        this.timeID = null;
                    }
                    this.timeID = window.setTimeout(() => {
                        this.initData();
                    }, 30 * 1000);
                }
            }
        )
    }

    ngOnDestroy() {
        if (this.sub) {
            this.sub.unsubscribe();
        }
        if (this.timeID) {
            window.clearTimeout(this.timeID);
            this.timeID = null;
        }
        this._isActive = false;
        super.ngOnDestroy();
    }
}