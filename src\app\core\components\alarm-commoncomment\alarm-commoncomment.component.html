<div class="modal-content">
  <div class="modal-header">
    <h4 class="modal-title">{{extTitle}}</h4>
    <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeOrg()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <div class="form-group row" *ngIf="opType === 'confirm'">
      <label class="col-sm-4 form-control-label showLabel">{{'general.alarm.eventReasonTypeName' | translate}}</label>
      <select class="col-sm-7 form-control" name="chatTypeVal" [(ngModel)]="reasonType">
        <option *ngFor="let obj of reasonList" [value]="obj.id">
            {{ obj.name | translate }}
        </option>
    </select>
      <em class="star"></em>
    </div>
    <div class="form-group row">
      <label class="col-sm-4 form-control-label showLabel">{{theTitle}}</label>
      <textarea class="col-sm-7 form-control" style="height: 222px;" maxlength="255" name="reason" [(ngModel)]="reason"></textarea>
      <em class="star"></em>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn phoenix-form-btn__default" data-dismiss="modal"
      (click)="closeOrg()">{{'general.common.cancel' | translate}}</button>
    <button type="button" class="btn btn-primary" (click)="submit()">{{'general.common.ok' | translate}}</button>
  </div>
</div>