// button 按钮
.ant-btn-primary {
  border: var(--portal-button-primary-border);
  color: var(--portal-button-primary-text-color);
  background-color: var(--portal-button-primary-bg-color);

  &:hover {
    border: var(--portal-button-primary-hover-border);
    color: var(--portal-button-primary-hover-text-color);
    background-color: var(--portal-button-primary-hover-bg-color);
  }

  &:active {
    border: var(--portal-button-primary-active-border);
    color: var(--portal-button-primary-active-text-color);
    background-color: var(--portal-button-primary-active-bg-color);
  }

  &:disabled {
    border: var(--portal-button-primary-disabled-border);
    color: var(--portal-button-primary-disabled-text-color);
    background-color: var(--portal-button-primary-disabled-bg-color);
  }
}

// input 输入框
.ant-input {
  background-color: var(--portal-input-bg-color);
  border-color: var(--portal-input-border-color);
  color: var(--portal-input-text-color);

  &:hover,
  &:focus {
    color: var(--portal-input-hover-text-color);
    background-color: var(--portal-input-hover-bg-color);
    border-color: var(--portal-input-hover-border-color);
  }

  @include placeholderStyle(var(--portal-input-placeholder-color), 1);
}

// select 下拉框
.ant-select {
  .ant-select-selection-placeholder {
    color: var(--portal-input-placeholder-color);
  }

  &:hover {
    color: var(--portal-input-hover-text-color);
    background-color: var(--portal-input-hover-bg-color);
    border-color: var(--portal-input-hover-border-color) !important;
  }

  &:focus {
    color: var(--portal-input-hover-text-color);
    background-color: var(--portal-input-hover-bg-color);
    border-color: var(--portal-input-hover-border-color) !important;
  }
}

.ant-select-dropdown {
  color: var(--portal-text-color);
  .ant-select-item {
    color: var(--portal-input-text-color);
    background-color: var(--portal-select-dropdown-bg-color);
    border-color: var(--portal-select-dropdown-border-color);

    &:hover,
    &:focus {
      color: var(--portal-select-option-hover-text-color);
      background-color: var(--portal-select-option-hover-bg-color);
    }
  }

  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    background-color: var(--portal-select-option-active-bg-color) !important;

    .ant-select-item-option-content {
      color: var(--portal-select-option-active-text-color);
    }

    .ant-select-item-option-state {
      color: var(--portal-select-option-active-text-color);
    }
  }
}

// form 表单
.ant-form {
  .ant-form-item {
    .ant-form-item-label {
      label {
        color: var(--portal-text-color);
      }
    }
  }
}


// DatePicker 日期选择框
.ant-picker {
  border-color: var(--portal-border-color);

  &:hover {
    color: var(--portal-input-hover-text-color);
    background-color: var(--portal-input-hover-bg-color);
    border-color: var(--portal-input-hover-border-color);
  }

  &:focus {
    color: var(--portal-input-hover-text-color);
    background-color: var(--portal-input-hover-bg-color);
    border-color: var(--portal-input-hover-border-color);
  }

  .ant-picker-input {
    input {
      color: var(--portal-text-color);

      &::placeholder {
        color: var(--portal-input-placeholder-color);
      }
    }
  }

  .ant-picker-separator {
    color: var(--portal-text-color);
  }

  .ant-picker-suffix {
    color: var(--portal-text-color);
  }

  .ant-picker-clear {
    color: var(--portal-text-color);
    background: var(--portal-cbg-color);

    &:hover {
      color: var(--portal-hover-color);
    }
  }
}

.ant-picker-dropdown {

  .ant-picker-date-range-wrapper,
  .ant-picker-range-wrapper {
    .ant-picker-range-arrow {
      background: linear-gradient(135deg, transparent 40%, var(--portal-cbg-color) 40%);

      &::before {
        background: var(--portal-cbg-color);
      }
    }

    .ant-picker-panel-container {
      background-color: var(--portal-cbg-color);
      box-shadow:
        0 3px 6px -4px var(--portal-picker-box-shadow-1-color),
        0 6px 16px var(--portal-picker-box-shadow-2-color),
        0 9px 28px 8px var(--portal-picker-box-shadow-3-color);

      .ant-picker-panels {
        background-color: var(--portal-cbg-color);

        .ant-picker-panel {
          border-color: var(--portal-border-color);
          background-color: var(--portal-cbg-color);

          > :not(.ant-picker-date-panel) .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start::before {
            background-color: var(--portal-picker-dropdown-cell-range-modify-bg-color);
          }

          .ant-picker-date-panel,
          .ant-picker-year-panel,
          .ant-picker-month-panel,
          .ant-picker-decade-panel {
            background-color: var(--portal-cbg-color);

            .ant-picker-header {
              color: var(--portal-text-color);
              border-color: var(--portal-border-color);

              .ant-picker-header-super-prev-btn {
                color: var(--portal-text-color);
              }

              .ant-picker-header-prev-btn {
                color: var(--portal-text-color);
              }

              .ant-picker-header-next-btn {
                color: var(--portal-text-color);
              }

              .ant-picker-header-super-next-btn {
                color: var(--portal-text-color);
              }
            }

            .ant-picker-body {
              .ant-picker-content {
                th {
                  color: var(--portal-text-color);
                }

                .ant-picker-cell {
                  color: var(--portal-text-color_25);

                  &:hover {
                    .ant-picker-cell-inner {
                      color: var(--portal-picker-dropdown-cell-inner-text-color);
                      background-color: var(--portal-picker-dropdown-cell-inner-bg-color);
                    }
                  }
                }

                .ant-picker-cell-in-view {
                  color: var(--portal-text-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-in-range::before {
                  background-color: var(--portal-picker-dropdown-cell-range-bg-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single)::before {
                  background-color: var(--portal-picker-dropdown-cell-range-bg-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single)::before {
                  background-color: var(--portal-picker-dropdown-cell-range-bg-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover::before {
                  background-color: var(--portal-picker-dropdown-cell-range-modify-bg-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single).ant-picker-cell-range-hover-start::before {
                  background-color: var(--portal-picker-dropdown-cell-range-modify-bg-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-start .ant-picker-cell-inner::after {
                  background-color: var(--portal-picker-dropdown-cell-range-modify-bg-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single).ant-picker-cell-range-hover-end::before {
                  background-color: var(--portal-picker-dropdown-cell-range-modify-bg-color);
                }

                .ant-picker-cell-in-view.ant-picker-cell-in-range.ant-picker-cell-range-hover-end .ant-picker-cell-inner::after {
                  background-color: var(--portal-picker-dropdown-cell-range-modify-bg-color);
                }
              }
            }
          }

          .ant-picker-time-panel {
            border-color: var(--portal-border-color);

            .ant-picker-header {
              border-color: var(--portal-border-color);
            }

            .ant-picker-content {
              .ant-picker-time-panel-column {
                border-color: var(--portal-border-color);

                .ant-picker-time-panel-cell {
                  .ant-picker-time-panel-cell-inner {
                    color: var(--portal-text-color);
                  }
                }

                .ant-picker-time-panel-cell-selected {
                  .ant-picker-time-panel-cell-inner {
                    color: #fff;
                    background: var(--portal-active-color);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}