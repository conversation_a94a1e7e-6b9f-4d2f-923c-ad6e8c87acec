@import "../../../../core/scss/ng2-admin-scss/auth";
@import "../../../../core/scss/ng2-admin-scss/form";

$margin-size: 4px;
$page-top-height: 60px;
$tab-size: 33px;

:host ::ng-deep {
  .auth-main {
    background-size: 100% 100%;
  }

  .auth-block {
    width: 610px;

    .form-group {
      margin-bottom: 0 !important;
    }
  }

  .forget-title {
    display: flex;
    justify-content: center;
    font-size: 18px;
  }

  .forget-placeholder {
    height: 18px
  }

  .phoenix-form .form-group {
    padding: 5px 0 !important;
  }

  .card {
    &.viewport100 {
      height: calc(100vh - #{$page-top-height} - #{$margin-size} * 8 - #{$tab-size}) !important;
    }

    .card-body {
      border: none;
    }
  }

  .grade {
    padding-left: 12px;
    align-self: center;
    font-size: 0;

    span {
      font-size: 14px;
      height: 16px;

      &.color {
        display: inline-block;
        width: 20px;
        height: 8px;
        margin-right: 4px;
      }
    }

    &.low {
      color: var(--error-color);

      .color-a {
        background-color: #ffbfbf;
      }

      .color-b {
        background-color: #ff7373;
      }

      .color-c {
        background-color: #ff0000;
      }
    }

    &.middle {
      color: #006dd9;

      .color-a {
        background-color: #73b9ff;
      }

      .color-b {
        background-color: #2693ff;
      }

      .color-c {
        background-color: #006dd9;
      }
    }

    &.high {
      color: #00b200;

      .color-a {
        background-color: #99ff99;
      }

      .color-b {
        background-color: #4dff4d;
      }

      .color-c {
        background-color: #00b200;
      }
    }
  }

  .div-code {
    height: 35px;
    width: 100%;
  }

  .modal-footer {
    justify-content: flex-start;
    border-top: none !important;
    .btn  {
        font-size: 16px !important;
    }
  }

  .form-control:hover {
    border-color: rgba(var(--primary-color), 1);
  }

  .phoenix-form label {
    padding-right: 0 !important;
  }

  // 手机号验证消息样式
  .phone-validation-message {
    padding-left: 12px;
    font-size: 14px;

    .success {
      color: #00b200;
    }

    .error {
      color: var(--error-color);
    }
  }

  // 附件上传样式
  .attachment-upload-container {
    .file-list {
      margin-top: 10px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #eee;

        .file-name {
          flex: 1;
          font-size: 14px;
          color: #333;
        }

        .file-size {
          margin-left: 10px;
          font-size: 12px;
          color: #666;
        }

        .btn {
          margin-left: 10px;
          padding: 2px 6px;
          font-size: 12px;
          line-height: 1;
        }
      }
    }

    .file-validation-info {
      margin-top: 5px;

      small {
        font-size: 12px;
        color: #666;
      }
    }

    // 上传状态样式
    .upload-status {
      margin-top: 10px;
      padding: 8px 12px;
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;

      .spinner-border-sm {
        width: 1rem;
        height: 1rem;
      }

      .text-primary {
        font-size: 14px;
        color: #007bff;
      }
    }
  }

  .file-validation-message {
    padding-left: 12px;
    font-size: 14px;

    .error {
      color: var(--error-color);
    }
  }
}