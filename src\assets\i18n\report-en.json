{"reportSchema": {"title": {"tit1": "Current Alarm Report", "tit2": "History Alarm Reports", "tit3": "History Data Reports", "tit4": "tit1", "tit5": "tit1", "tit6": "tit1", "tit7": "Alarm Operation Log Report (Device)", "tit8": "Alarm Operation Log Report (Alarm Events)", "tit9": "History Discharge Reports", "tit10": "History Metrics Statement", "tit11": "tit1", "tit12": "tit1", "tit13": "Control Record Report", "tit14": "Instant Reports", "tit15": "Statistical Report On The Number Of Alarms", "tit16": "Electricity Consumption Statistics Report", "tit17": "tit1", "tit18": "Generic Customized Reports", "tit19": "tit1", "tit20": "tit1", "tit21": "tit1", "tit22": "tit1", "tit23": "Alarm Masking Report", "tit24": "User Operation Log Report", "tit25": "Access Control Card Swiping Record Log", "tit26": "Alarm Notification Delivery Record Report", "tit27": "Historical Warning Data Query Report", "tit28": "Historical Data Report(5 Minutes Storage)", "tit29": "Rack Change Record Report", "tit30": "Audit Record Report", "tit31": "Security Log Report", "tit32": "Equip state report", "tit33": "Batch Control Group Change Log Report", "tit34": "Batch Issue Control Command History Report", "tit35": "Virtual Management Equipment History Change Report", "tit36": "BA Control Command Report", "tit37": "All Alarm Report", "tit38": "Equipment Report", "tit39": "Historical Data Report(Raw data)", "tit40": "Alarm Classification Statistical Report", "tit41": "Serial Port Information Report"}, "description": {"des1": "Display the results of the current alarm report data for the desired query", "des2": "Display the results of the required query history alarm report data", "des3": "Display the data results of the required query history data report", "des4": "des1", "des5": "des1", "des6": "des1", "des7": "Display the results of the required query alarm operation log report data", "des8": "Display the results of the required query alarm operation log report data", "des9": "Display the historical discharge structure of the desired query device", "des10": "Display the results of the required query history index report data", "des11": "des1", "des12": "des1", "des13": "Display the control records for the desired query", "des14": "Display real-time data for the desired query", "des15": "Statistical report on the number of alarms", "des16": "Display the electricity consumption statistics for the desired query", "des17": "des1", "des18": "Display the daily electricity consumption statistics for the desired query", "des19": "des1", "des20": "des1", "des21": "des1", "des22": "des1", "des23": "Display the results of the required query alarm masking report data", "des24": "The user's operation log contains operations such as login and configuration changes", "des25": "Display the required query access control card swiping record report data results", "des26": "Query alarm notification delivery records", "des27": "Display the results of the desired query history alert data", "des28": "Display the data results of the required query history data report", "des29": "Display the results of the desired rack change record", "des30": "Display the results of the desired audit record", "des31": "Display the security log records to be queried", "des32": "Display the historical operation status of the subordinate equipment of the air-conditioning group control equipment that needs to be queried", "des33": "Display change log records of batch control groups", "des34": "Display the history of batch issued control commands", "des35": "Display the virtual management equipment and associated data change log records", "des36": "Display BA issues instructions and synchronizes to the control record of s 6", "des37": "Display the results of all the alarm tables required to be queried", "des38": "Display the list of equipment to be queried", "des39": "Display the data results of the required query history data report", "des40": "Displays the number of abnormal alarms and construction alarms to be queried", "des41": "Display the required serial port information data"}, "categoryName": {"category1": "Alarm", "category2": "History", "category3": "Complex Index", "category4": "Log", "category5": "Others", "category6": "Battery", "category7": "Security", "category8": "Air Conditioner"}, "queryParameter": {"param1": "Start Time", "param2": "End Time", "param3": "Equipment Base Class", "param4": "Equipment", "param7": "Start Time", "param8": "End Time", "param10": "Time Granularity", "param12": "Start Time", "param13": "End Time", "param14": "Equipment", "param16": "Operators", "param17": "Operation", "param18": "Start Time", "param19": "End Time", "param21": "Events", "param22": "Operators", "param23": "Operation", "param25": "Equipment", "param26": "Start Time", "param27": "End Time", "param28": "Indicators", "param29": "Time Granularity", "param38": "Start Time", "param39": "End Time", "param41": "Start Time", "param42": "End Time", "param43": "Equipment Base Class", "param44": "Equipment", "param45": "Start Time", "param46": "End Time", "param47": "Start Time", "param48": "End Time", "param49": "Signal", "param50": "Time Granularity", "param55": "Start Time", "param56": "End Time", "param57": "Template File", "param58": "Configuration Files", "param59": "Time Granularity", "param78": "Shield Start Time", "param79": "Shield End Time", "param80": "Start Time", "param81": "End Time", "param82": "Operation Type", "param83": "Events", "param84": "Operators", "param86": "Start Time", "param87": "End Time", "param88": "Operators", "param89": "Area", "param90": "Equipment", "param91": "Card Code", "param92": "Card Name", "param93": "Card Holder", "param94": "Card Group", "param95": "Card Status", "param96": "Swipe Start Date", "param97": "Swipe End Date", "param98": "Access Door Flag", "param99": "Door Status", "param85": "Equipment Signal", "param101": "Alarm Time Start", "param102": "Alarm Time End", "param103": "Send Time From", "param104": "Send Time TO", "param105": "Send Type", "param106": "Send Result", "param109": "Alarm Level", "param110": "Event Name", "param111": "Confirmed By", "param112": "Keyword", "param113": "Alarm Level", "param114": "Event Name", "param115": "Keyword", "param116": "Confirmed By", "param117": "Events", "param118": "Events", "param119": "Start Time", "param120": "End Time", "param121": "Early Warning Category", "param122": "Early Warning Level", "param123": "Resource Level", "param124": "Resource Name", "param125": "Start Time", "param126": "End Time", "param127": "Time Granularity", "param128": "Equipment Signal", "param129": "Unit", "param130": "Value Retrieval Method", "param150": "Base Type", "param151": "Signal Type", "param152": "Unit", "param153": "Equipment", "param154": "Value Retrieval Method", "param200": "Start Time", "param201": "End Time", "param203": "Resource Location", "param204": "<PERSON>ck <PERSON>", "param205": "IT Equipment Name", "param206": "Status", "param210": "Start Time", "param211": "End Time", "param212": "Operation Account", "param213": "Details", "param214": "Client IP", "param215": "Audit Level", "param216": "Start Time", "param217": "End Time", "param218": "Operation Account", "param219": "Type", "param220": "Details", "param221": "Client Ip", "param222": "Start Time", "param223": "End Time", "param224": "Virtual Group Control Device", "param225": "Start Time", "param226": "End Time", "param227": "Change Type", "param228": "Start Time", "param229": "End Time", "param230": "Equipment", "param231": "Command Type", "param232": "Start Time", "param233": "End Time", "param234": "Station", "param235": "Change Type", "param236": "Equipment", "param237": "Control Name", "param238": "Start Time", "param239": "End Time", "param240": "Equipment Base Class", "param241": "Equipment", "param242": "Alarm Level", "param243": "Event Name", "param244": "Keyword", "param245": "Confirmed By", "param246": "Events", "param247": "Resource Structure", "param248": "Equipment Base Class", "param260": "Start Time", "param261": "End Time", "param262": "Equipment Signal", "param263": "Base Type", "param264": "Signal Type", "param265": "Equipment", "param280": "Statistic Type", "param281": "Room", "param282": "Equipment Category", "param283": "Equipment", "param284": "Start Time", "param285": "End Time", "param286": "Equipment Base Class", "param287": "Equipment", "param288": "EquipmentState", "param292": "Note", "param293": "Note", "param300": "Station", "param301": "Monitor Unit Name", "param302": "Serial Port Name", "param303": "Display device location when export", "param400": "Equipment Category", "param401": "Alarm Reason", "param500": "Equipment Category", "param501": "Alarm Reason", "param600": "Equipment Category", "param601": "Alarm Reason", "param700": "Equipment Category", "param310": "ResourceStructure Name", "param311": "Equipment Name", "param312": "Equipment Type", "param313": "IP Address", "param314": "Collection Unit Name", "param315": "Equipment Status", "param316": "Equipment Template Name", "param317": "Display System ID", "param290": "Alarm level"}, "selectOption": {"option10": "[{'name': '1 Minutes','value': '1m-start'},{'name': '5 Minutes','value': '5m-start'},{'name': '10 Minutes','value': '10m-start'},{'name': '30 Minutes','value': '30m-start'},{'name': '1 Hour','value': '1h-start'},{'name': '3 Hour','value': '3h-start'},{'name': '6 Hour','value': '6h-start'},{'name': '12 Hour','value': '12h-start'},{'name': '1 Day','value': '1d-start'},{'name': 'Week','value': '1w-start'},{'name': 'Month','value': 'month-start'},{'name': 'Year','value': 'year-start'}]", "option17": "[{'name':'All', 'value':'0'},{'name':'Alarm Forced End', 'value':'Alarm Forced End'}, {'name':'Alarm Confirm', 'value':'Alarm Confirm'}, {'name':'Alarm Remark', 'value':'Alarm Remark'}]", "option23": "[{'name':'All', 'value':'0'},{'name':'Alarm Forced End', 'value':'Alarm Forced End'}, {'name':'Alarm Confirm', 'value':'Alarm Confirm'}, {'name':'Alarm Remark', 'value':'Alarm Remark'}]", "option29": "[{'name': 'Raw Data','value': '0'},{'name': '10 Minutes','value': '10m'},{'name': '30 Minutes','value': '30m'},{'name': '1 Hour','value': '1h'},{'name': '3 Hour','value': '3h'},{'name': '6 Hour','value': '6h'},{'name': '12 Hour','value': '12h'},{'name': '1 Day','value': '1d'},{'name': 'Week','value': '1w'},{'name': 'Month','value': 'month'}]", "option50": "[{'name': 'Day-Start','value': 'start'},{'name': 'Day-End','value': 'end'},{'name': 'Day-Difference','value': 'difference'}]", "option59": "[{'name': 'Min Value','value': 'min'},{'name': 'Max Value','value': 'max'},{'name': 'Initial Value','value': 'start'},{'name': 'End Value','value': 'end'},{'name': 'Average Value','value': 'avg'},{'name': 'Difference Value','value': 'difference'},{'name': 'Max Difference Value','value': 'maxdifference'},{'name': 'Month Difference Value','value': 'monthdifference'}]", "option98": "[{'name': 'Entrance','value': 'Entrance'},{'name': 'Go Out','value': 'Go Out'}]", "option105": "[{'name':'All', 'value':'0'},{'name':'Short Message', 'value':'Short Message'}, {'name':'Mail', 'value':'Mail'}, {'name':'Telephone Voice(SMS)', 'value':'Telephone Voice(SMS)'},{'name':'WeCom Apply Notify', 'value':'WeCom Apply Notify'}]", "option106": "[{'name':'All', 'value':'0'},{'name':'Send Success', 'value':'Send Success'}, {'name':'Send Fail', 'value':'Send Fail'}]", "option127": "[{'name': 'Raw Data','value': '0'},{'name': '10 Minutes','value': '10m-start'},{'name': '30 Minutes','value': '30m-start'},{'name': '1 Hour','value': '1h-start'},{'name': '3 Hour','value': '3h-start'},{'name': '6 Hour','value': '6h-start'},{'name': '12 Hour','value': '12h-start'},{'name': '1 Day','value': '1d-start'},{'name': 'Week','value': '1w-start'},{'name': 'Month','value': 'month-start'}]", "option129": "[{'name':'No','value':'0'},{'name':'Yes','value':'1'}]", "option130": "[{'name': '<PERSON>','value': 'max'},{'name': 'Min','value': 'min'},{'name': 'Avg','value': 'mean'},{'name': 'First','value': 'first'},{'name': 'Last','value': 'last'}]", "option151": "[{'name':'Statistical Data－Max','value':'0'},{'name':'Statistical Data－Min','value':'1'},{'name':'Statistical Data－Avg','value':'2'},{'name':'Event','value':'3'},{'name':'Variation Amplitude','value':'4'},{'name':'Storage Cycle','value':'5'},{'name':'Meter Reading Data','value':'6'},{'name':'Scheduled Storage','value':'7'}]", "option152": "[{'name':'No','value':'0'},{'name':'Yes','value':'1'}]", "option154": "[{'name': '<PERSON>','value': 'max'},{'name': 'Min','value': 'min'},{'name': 'Avg','value': 'mean'},{'name': 'First','value': 'first'},{'name': 'Last','value': 'last'}]", "option206": "[{'name': 'All','value': '0'},{'name': 'Put In Shelf','value': '1'},{'name': 'Take Form Shelf','value': '2'}]", "option215": "[{'name': 'Min Level','value': '1'},{'name': 'Basic Level','value': '2'},{'name': 'Detail Level','value': '3'},{'name': 'Undefined Level','value': '4'}]", "option219": "[{'name': 'Identity Authentication','value': '1'},{'name': 'Attack Detection','value': '2'},{'name': 'Brute Force','value': '3'},{'name': 'Integrity Test','value': '4'}]", "option227": "[{'name':'All', 'value':'-1'},{'name':'New', 'value':'1'},{'name':'Update', 'value':'2'},{'name':'Delete', 'value':'3'}]", "option231": "[{'name':'All', 'value':'-1'},{'name':'Remote boot', 'value':'1'},{'name':'Remote shutdown', 'value':'2'},{'name':'Temperature setting', 'value':'3'},{'name':'Working mode switching', 'value':'4'}]", "option235": "[{'name':'All', 'value':'-1'},{'name':'New', 'value':'1'},{'name':'Update', 'value':'2'},{'name':'Delete', 'value':'3'}]", "option264": "[{'name':'Statistical Data－Max','value':'0'},{'name':'Statistical Data－Min','value':'1'},{'name':'Statistical Data－Avg','value':'2'},{'name':'Event','value':'3'},{'name':'Variation Amplitude','value':'4'},{'name':'Storage Cycle','value':'5'},{'name':'Meter Reading Data','value':'6'},{'name':'Scheduled Storage','value':'7'}]", "option280": "[{'name': 'Room','value': '1'},{'name': 'Device','value': '2'},{'name': 'Device Type','value': '3'}]", "option288": "[{'name': 'OffLine','value': '0'},{'name': 'OnLine','value': '1'},{'name': 'UnRegistered','value': '2'}]", "option303": "[{'name':'No', 'value':'-1'},{'name':'Yes', 'value':'1'}]", "option315": "[{'name':'Offline', 'value':'0'},{'name':'Online', 'value':'1'},{'name':'Unregistered', 'value':'2'}]", "option317": "[{'name':'No','value':'0'},{'name':'Yes','value':'1'}]"}, "timeType": {"time1": "Storage Time", "time2": "An hour ago", "time3": "A dag ago", "time4": "A week ago", "time5": "A month ago"}, "storageCycle": {"cycle15": "every hour", "cycle16": "every 12 hours", "cycle17": "every day", "cycle18": "every week", "cycle19": "every month", "cycle21": "8am every day", "cycle22": "20pm every day", "cycle35": "02 minutes every day", "cycle36": "02 minutes per week", "cycle37": "02 minutes per month", "cycle39": "every year"}, "exportParameter": {"param1": "Max", "param2": "Min", "param3": "Sum", "param4": "Avg", "param5": "Max", "param6": "Min", "param7": "Sum", "param8": "Avg", "param9": "First", "param10": "Second", "param11": "Third", "param12": "Fourth", "param13": "Max", "param14": "Min", "param15": "Sum", "param16": "Avg", "param17": "Max", "param18": "Min", "param19": "Sum", "param20": "Avg", "param21": "Max", "param22": "Min", "param23": "Sum", "param24": "Avg", "param25": "First", "param26": "Last"}}, "reportStatus": {"generatReportFailed": "Generate Report Failed!", "dowloadFailed": "Download Report File Failed!", "dowloadSucced": "Download Report File Succesed!"}}