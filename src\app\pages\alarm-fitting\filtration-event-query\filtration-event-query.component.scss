:host {
  .filtration-event-query-container {
    height: 100%;
    background: var(--background-color-widget);
    overflow-y: auto;
  }

  .event-query-content {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  // 日期控件
  .bs-daterangepicker {
    width: auto;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-right: 16px;

    label.control-label {
      margin-left: 12px;
      margin-right: 8px;
      margin-bottom: 0;
      color: var(--text-color);
    }
    label.custom-control {
        margin-left: 32px;
    }

    .input-group {
      width: 24%;
      height: 32px;
      display: flex;
      align-items: center;

      input {
          height: 26px;
      }
    }
  }

  .form-group,
  .input-group {
    margin-bottom: 0;
  }
  .form-group.hidden {
    width: 0;
    margin: 0;
    border: none;
    padding: 0;
  }
  .custom-day {
    text-align: center;
    padding: 0.185rem 0.25rem;
    display: inline-block;
    height: 2rem;
    width: 2rem;
  }
  .custom-day.focused {
    background-color: #e6e6e6;
  }
  .custom-day.range,
  .custom-day:hover {
    background-color: rgb(2, 117, 216);
    color: white;
  }
  .custom-day.faded {
    background-color: rgba(2, 117, 216, 0.5);
  }

  .search-container {
    margin-left: 14px;

    input {
      line-height: 1;
    }
  }
}
