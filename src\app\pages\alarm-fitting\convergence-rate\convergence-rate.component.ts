import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { ConvergenceRateService } from '../services/convergence-rate.service';
import { CurrentStatistics } from '../alarm-fitting.model';
import { EchartsOptionsService } from '../services/echarts-options.service';
import {
	NgbDate,
	NgbDateParserFormatter,
	NgbCalendar,
} from '@ng-bootstrap/ng-bootstrap';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { merge } from 'lodash';
import {
	debounceTime,
	takeWhile,
	distinctUntilChanged,
	switchMap,
} from 'rxjs/operators';
import { interval } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import * as moment from 'moment';

@Component({
	selector: 'app-convergence-rate',
	templateUrl: './convergence-rate.component.html',
	styleUrls: ['./convergence-rate.component.scss'],
})
export class ConvergenceRateComponent implements OnInit, OnDestroy {
	COLOR_LIST = ['#F6B10D', '#00B200', '#00FFFF'];
	DEBOUNCE_TIME = 800;
	INTERVAL_TIME = 5000;
	currentStatistics: CurrentStatistics = null;
	pieChartOptions: any;
	curveOptions: any;
	curveChartDatas: any = [];
	startTime: any;
	endTime: any;
	hoveredDate: NgbDate | null = null;
	alive = true;
	subId: any;

	options: any = {
		singleDatePicker: true,
		timePicker: true,
		timePicker24Hour: true,
		timePickerSeconds: true,
		autoUpdateInput: false,
		showDropdowns: true,
		locale: {
			format: 'YYYY-MM-DD HH:mm:ss',
		},
	};


	constructor(
		private service: ConvergenceRateService,
		private optionsService: EchartsOptionsService,
		private calendar: NgbCalendar,
		private translate: TranslateService,
		public formatter: NgbDateParserFormatter
	) {
		this.startTime = moment().subtract(7, 'days').format('YYYY-MM-DD 00:00:00');
		this.endTime = DateUtil.dateFormat(new Date(), 'YYYY-MM-DD') + ' 00:00:00';
		this.getConvergenceStatistisc();
	}

	ngOnInit(): void {
		this.getCurrentStatistics();
		let lang = localStorage.getItem('lang');
		if (lang == 'zh') {
			this.options = DateUtil.getDatePickerOptions();
		} else {
			this.options = DateUtil.getTranslateDatePickerOptions(this.translate);
		}
	}

	ngOnDestroy(): void {
		this.alive = false;
		if (this.subId) {
			this.subId.unsubscribe();
		}
	}

	selectTime(value: any, state: string) {
		const date = state === 'startTime' ? 'startDate' : 'endDate';
		const val = value.picker[date]['_d'];
		const time = DateUtil.shortDateToString(val);
		if (state === 'startTime') {
			this.startTime = time;
		} else {
			this.endTime = time;
		}
	}

	/**
	 * 实时获取今日数据
	 */
	getCurrentStatistics(): void {
		this.subId = interval(this.INTERVAL_TIME)
			.pipe(switchMap(() => this.service.getCurrentEventsStatistics()))
			.pipe(takeWhile(() => this.alive))
			.subscribe((res) => {
				if (!res) {
					return;
				}
				this.currentStatistics = res;
				this.initPieChartOptions();
			});
	}

	searchCurveDatas(): void {
		this.getConvergenceStatistisc();
	}

	getConvergenceStatistisc(): void {
		const now = new Date();
		const timeStr = `${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;
		const startTime = DateUtil.formatNgbDate(this.startTime).substr(0, 10);
		const endTime = DateUtil.formatNgbDateToEndTime(this.endTime).substr(0, 10) + ` ${timeStr}`;
		this.service
			.getConvergenceStatistics(startTime, endTime)
			.pipe(debounceTime(this.DEBOUNCE_TIME), distinctUntilChanged())
			.subscribe((res) => {
				if (!res || !Array.isArray(res)) {
					return;
				}
				this.curveChartDatas = res;
				this.initCurveChartOptions();
			});
	}

	initPieChartOptions() {
		let options = {
			xAxis: null,
			yAxis: null,
			series: [],
		};
		options = this.optionsService.initOptions(options);
		options.series.push(
			this.optionsService.setSeries(
				'pie',
				[
					[this.currentStatistics.convergenceCount],
					[
						this.currentStatistics.totalCount -
						this.currentStatistics.convergenceCount,
					],
				],
				{ label: { show: false } }
			)
		);
		const colorList = [
			['linear', '#00ffff', '#5900b2'],
			'rgba(70, 114, 179, 0.2)',
		];
		options['color'] = this.optionsService.setMultiColors(colorList);
		this.pieChartOptions = options;
	}

	initCurveChartOptions() {
		let options = {
			legend: {
				data: [this.translate.instant('alarmFitting.convergenceRate.event'), this.translate.instant('alarmFitting.convergenceRate.convergentEvent'), this.translate.instant('alarmFitting.convergenceRate.convergenceRate')],
			},
			tooltip: {},
			xAxis: {
				type: 'category',
				splitLine: {
					show: true,
				},
				data:
					this.curveChartDatas.map((item) =>
						this.formatDate(item.statisticsDate, 2, '/')
					) || [],
			},
			yAxis: [
				{
					name: this.translate.instant('alarmFitting.convergenceRate.eventCount'),
					type: 'value',
				},
				{
					name: this.translate.instant('alarmFitting.convergenceRate.convergenceRateCount'),
					type: 'value',
					max: 100,
				},
			],
			series: [],
		};
		options = this.optionsService.initOptions(options);
		options.yAxis.forEach((item) => {
			item = merge(item, this.optionsService.setDefaultYaxis());
		});
		const totalCounts =
			this.curveChartDatas.map((item) => item.totalCount) || [];
		const convergenceCounts =
			this.curveChartDatas.map((item) => item.convergenceCount) || [];
		const rates =
			this.curveChartDatas.map((item) => item.convergenceRate) || [];
		options.series.push(
			this.optionsService.setSeries('line', totalCounts, {
				name: this.translate.instant('alarmFitting.convergenceRate.event'),
				smooth: true,
			})
		);
		options.series.push(
			this.optionsService.setSeries('line', convergenceCounts, {
				name: this.translate.instant('alarmFitting.convergenceRate.convergentEvent'),
				smooth: true,
			})
		);
		options.series.push(
			this.optionsService.setSeries('line', rates, {
				yAxisIndex: 1,
				name: this.translate.instant('alarmFitting.convergenceRate.convergenceRate'),
				smooth: true,
			})
		);
		options['color'] = this.optionsService.setMultiColors(this.COLOR_LIST);

		this.curveOptions = options;
	}

	formatDate(str: string, index: number, symbolMark: string): string {
		return str.slice(0, index) + symbolMark + str.substr(index);
	}

	showNumCount(value: number | null): string {
		return typeof value === 'number' ? value.toString() : '--';
	}
}
