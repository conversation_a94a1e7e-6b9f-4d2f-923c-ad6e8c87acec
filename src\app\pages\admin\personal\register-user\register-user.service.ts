import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

@Injectable()
export class RegisterUserService {
  language: any;

  constructor(private http: HttpClient) {
 
  }

  registerUser( param: any){
      const header = new HttpHeaders().set('content-Type', 'application/json');
      return this.http.post('siteweb-flow/api/processInstance/create', param);
  }

  // 验证手机号接口（预留）
  validatePhone(phone: string) {
      const header = new HttpHeaders().set('content-Type', 'application/json');
      // TODO: 替换为实际的后端验证接口
      return this.http.post('api/validate-phone', { phone: phone }, { headers: header });
  }

  // 批量上传文件接口
  uploadFiles(files: File[]) {
      const formData = new FormData();

      // 将所有文件添加到FormData中
      files.forEach(file => {
          formData.append('files', file);
      });

      return this.http.post('siteweb-flow/api/attachment/upload/batch', formData);
  }
}
