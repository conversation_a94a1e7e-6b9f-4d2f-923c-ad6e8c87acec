﻿{
	"general": {
		"home": "首页",
		"connectError": "服务器未连接！",
		"reconnectSever": "正在重新连接服务器......",
		"noPermission": "您暂时没有该页的访问权限",
		"sessionTimeout": "会话超时,请重新登录",
		"backPage": "返回上一页",
		"count": "数量",
		"toplogy": {
			"online": "在线",
			"offline": "离线(1.当前节点离线 2.连接关系树下级存在离线节点)",
			"masked": "屏蔽中",
			"expandAll": "展开全部",
			"collapseAll": "折叠全部"
		},
		"file": {
			"extNameNoSame": "文件内容与扩展名不一致"
		},
		"status": {
			"alarm": "告警",
			"normal": "正常"
		},
		"switchpage": {
			"add": "自定义",
			"addfromhmi": "从组态",
			"addfrommenu": "从菜单",
			"setting": "设置轮巡页面及间隔",
			"span": "页面切换间隔 (秒)",
			"starting": "页面轮巡中...",
			"stoping": "页面轮巡已暂停",
			"prev": "上一页面",
			"next": "下一页面",
			"start": "开启页面轮巡",
			"stop": "暂停页面轮巡",
			"quad": "设置页面",
			"topLeft": "左上",
			"topRight": "右上",
			"bottomLeft": "左下",
			"bottomRight": "右下"
		},
		"time": {
			"hour": "时",
			"minute": "分",
			"second": "秒",
			"day": "号",
			"date": "日",
			"month": "月",
			"week": "周",
			"year": "年",
			"everyday": "每日",
			"everyweek": "每周",
			"everymonth": "每月",
			"everyyear": "每年",
			"everyhour": "每小时",
			"everymin": "每分钟",
			"everySec": "每秒",
			"Monday": "周一",
			"Tuesday": "周二",
			"Wednesday": "周三",
			"Thursday": "周四",
			"Friday": "周五",
			"Saturday": "周六",
			"Sunday": "周日"
		},
		"cron": {
			"cronExpressionSetting": "CRON表达式配置",
			"custom": "自定义",
			"timeExpression": "时间表达式",
			"crontabFullExpression": "crontab完整表达式",
			"secondTips": "谨慎配置秒级触发策略，对系统会有影响",
			"dateStart": "日开始，每",
			"numDays": "日执行一次",
			"hourStart": "小时开始，每",
			"numHours": "小时执行一次",
			"minStart": "分钟开始，每",
			"numMins": "分钟执行一次",
			"monthStart": "月开始，每",
			"numMonths": "月执行一次",
			"secondStart": "秒开始，每",
			"numSeconds": "秒执行一次",
			"yearStart": "年开始，每",
			"numYears": "年执行一次",
			"lastDayOfTheMonth": "本月最后一天",
			"lastWeekOfTheMonth": "本月最后一个星期",
			"nearestWorking": "每月",
			"eachMonth": "号最近的那个工作日",
			"multipleSelect": "可多选",
			"expression": "表达式",
			"include": "指定",
			"exclude": "不指定",
			"cycleFromWeek": "周期从星期",
			"cycleFrom": "周期从",
			"ofEveryMonth": "日",
			"ofEveryDay": "时",
			"ofEveryHour": "分钟",
			"ofEveryMinute": "秒",
			"ofEveryYear": "月",
			"ofEveryWeek": "",
			"year": "年",
			"week": "",
			"ofTheWeek": "周的星期",
			"th": "第",
			"ofTheMonth": "",
			"leaveBlank": "不填",
			"from": "从",
			"confirm": "确定",
			"reset": "重置",
			"cancel": "取消",
			"last5Runs": "最近5次运行时间",
			"calculatingResult": "计算结果中...",
			"noResult": "没有达到条件的结果!",
			"only": "最近100年内只有上面",
			"results": "条结果！",
			"second": "秒",
			"minute": "分钟",
			"hour": "小时",
			"day": "日",
			"week2": "周",
			"month": "月"
		},
		"dashboard": {
			"alarmCount": "告警总数",
			"alarmPosition": "告警分布 ",
			"alarmLevelSatic": "等级数量",
			"oneLevelAlarm": "一级告警",
			"secondLevelAlarm": "二级告警",
			"threeLevelAlarm": "三级告警",
			"fourLevelAlarm": "四级告警",
			"allAlarm": "所有告警",
			"noAlarm": "无告警",
			"maskedAlarm": "屏蔽告警",
			"onlineRate": "在线率",
			"onLine": "在线",
			"offLine": "离线",
			"unregister": "未注册",
			"alarmTrend": "告警趋势",
			"realTime": "实时",
			"usersByCountry": "用户列表",
			"revenue": "收益",
			"feed": "反馈",
			"toDoList": "代办列表",
			"calendar": "日历",
			"newVisits": "访问量",
			"purchases": "购物",
			"activeUsers": "活动用户",
			"returned": "已归还"
		},
		"forms": {
			"option1": "选项 1",
			"shiftchange": "交接班"
		},
		"menu": {
			"syncMenuItem": "同步菜单显示权限点",
			"alarmOverview": "告警总览",
			"activeAlarm": "活动告警",
			"realTimeData": "实时数据",
			"dailyInspection": "日常巡视",
			"running": "运行",
			"organizationManagement": "组织管理",
			"taskManagement": "任务管理",
			"airConditioningGroupControl": "空调群控",
			"ttsconfig": "TTS配置",
			"analysis": "分析",
			"monitoringData": "监控数据",
			"permissions": "权限管理",
			"roles": "角色",
			"permission": "权限",
			"permissionCategories": "权限分类",
			"persons": "人员管理",
			"register": "注册用户",
			"groups": "组管理",
			"resource": "资源",
			"systemInternalConfig": "内部配置",
			"themeAndLogo": "主题及Logo",
			"userTheme": "用户主题",
			"customMenu": "自定义菜单",
			"alarm": "告警列表",
			"real-timeAlarm": "实时告警",
			"historyAlarm": "历史告警",
			"workbench": "工作台",
			"report-entry": "运维分析",
			"cmdb": "配置管理",
			"misc": "其他",
			"monitor": "运维工具",
			"device": "设备查找",
			"deviceDetail": "设备详情",
			"deviceBook": "机历簿",
			"crewScheduling": "排班管理",
			"weekSchedule": "本周排班",
			"time": "时间",
			"flight": "班次设置",
			"scheduing": "排班日历",
			"labelPrinting": "设备二维码",
			"onduty": "今日值班",
			"myscheduing": "我的排班",
			"taskSchedule": "任务安排",
			"scheduingPerson": "排班人员",
			"scheduingCalendar": "组织日历",
			"duty": "岗位",
			"teamhuman": "人员组织",
			"team": "团队",
			"maintenanceoperation": "运维管理",
			"taskoverview": "任务总览",
			"personaltask": "个人任务",
			"report": "运维分析",
			"backstage": "后台配置",
			"profile": "个人设置",
			"sysconfig": "系统参数",
			"shiftchange": "交接班",
			"registration": "签到",
			"shiftchangeReport": "交接班报表",
			"alarmHistoryReport": "历史告警报表",
			"verificationReport": "设备维护报表",
			"dailyinspectanalysis": "日常巡检分析",
			"routineinspectanalysis": "月度维护分析",
			"historydatareport": "历史数据曲线报表",
			"controlRecord": "控制记录报表",
			"loginlogreport": "登录日志报表",
			"batteryAlarmHistoryReport": "蓄电池告警报表",
			"dailyInspectDayReport": "日常巡检日报表",
			"safe": "安全",
			"maintenance": "维护",
			"verification": "月度维护",
			"baseInformation": "基本信息",
			"informations": "工作日志",
			"emergency": "应急保障",
			"fault": "故障管理",
			"defect": "缺陷管理",
			"customerDetail": "客户信息",
			"business": "基础业务",
			"customer": "客户管理",
			"contract": "合同管理",
			"contractDetail": "合同详情",
			"contractTerm": "合同条款管理",
			"contractTermDetail": "合同条款详情",
			"announcement": "公告",
			"announcementDetail": "公告详情",
			"logoSetting": "Logo设置",
			"license": "License",
			"updatePassword": "修改密码",
			"forgetPassword": "忘记密码",
			"park": "园区",
			"building": "大楼",
			"floor": "楼层",
			"mdc": "MDC",
			"rack": "机架",
			"deviceOfRack": "上架设备",
			"assetGeneral": "通用资产",
			"room": "房间",
			"basePointdictionary": "基类规范",
			"human": "组织架构",
			"humanList": "组织列表",
			"extends": "扩展",
			"servicesRegister": "注册服务",
			"navigationDock": "导航坞",
			"navigationTarget": "导航目标",
			"document": "文档管理",
			"docTypeManagement": "分类管理",
			"notification": "通知",
			"readed": "已读",
			"readedAll": "已读所有",
			"setting": "模块",
			"settings": "模式视图",
			"entry": "项目入口",
			"map": "地图",
			"collection": "采集",
			"camera": "摄像机",
			"rackEquipment": "机柜设备",
			"material": "物资管理",
			"personGroup": "人员组",
			"dashboards": "显示面板",
			"twod": "2D模态视图",
			"dev2d": "组态设计",
			"3deditor": "3D设计",
			"2dTempl": "设备组态",
			"2dComp": "组件管理",
			"2dCompAdd": "新增",
			"2dCompEdit": "编辑",
			"idcManage": "IDC管理",
			"hierarchy": "层级",
			"indicatorEedit": "指标编辑",
			"menuDefine": "自定义菜单",
			"ticket": "工单",
			"themesetting": "设置主题",
			"aliassetting": "别名设置",
			"scene": "场景切换",
			"currentPwd": "当前密码"
		},
		"alarm": {
			"width": "宽度",
			"alarmDetails": "告警详情",
			"alarmDevice3dLocation": "告警设备3D定位",
			"createAlarmWorkOrder": "创建告警工单",
			"alarmStartEndSnapshot": "查看告警抓图",
			"cameraName": "摄像头",
			"evnetType": "事件类型",
			"pageNumber": "图片序号",
			"expertAdvice": "专家建议",
			"first": "首页",
			"end": "尾页",
			"prePage": "前一页",
			"nextPage": "后一页",
			"perPageCount": "每页",
			"filterResult": "满足页面过滤条件的共",
			"lock": "锁定当前页",
			"unlock": "解锁当前页",
			"filterSetting": "过滤设置",
			"customFilter": "自定义条件",
			"title": "告警列表",
			"coreEventName": "告警名称",
			"severityName": "告警等级",
			"comment": "备注",
			"coreSourceName": "告警来源",
			"corePointName": "告警名称",
			"occurValue": "告警触发值",
			"endValue": "告警结束值",
			"eventReasonTypeName": "告警分类",
			"occureRemark": "告警备注",
			"startTimeFrom": "开始时间晚于",
			"startTimeTo": "开始时间早于",
			"birthTime": "开始时间",
			"clearTime": "结束时间",
			"meanings": "告警含义",
			"execResult": "状态",
			"execTime": "创建时间",
			"execTimes": "控制时间",
			"execPerson": "操作员",
			"responseTime": "响应时间",
			"clearedByName": "结束人",
			"confirmTime": "确认时间",
			"confirmerName": "确认人",
			"confirmComment": "确认备注",
			"selectAlarm": "已选告警",
			"deviceName": "设备名称",
			"devicePosition": "设备位置",
			"deviceCategory": "设备类型",
			"remarkCount": "备注数",
			"convergeCount": "收敛数",
			"reason": "原因",
			"mask": "屏蔽",
			"baseLogicCategoryName": "告警类型",
			"desc": "内容",
			"batch": "批量",
			"opUser": "操作人",
			"alarm": "告警",
			"opLog": "操作日志",
			"last7Reord": "最近7天告警记录",
			"isEnded": "告警已结束且已确认",
			"allConfirmed": "所选告警都已被确认",
			"hasConfirmed": "所选告警有部分已被确认或者当前用户无权限确认",
			"hasNoPermissionMask": "所选告警有部分当前用户无权限屏蔽",
			"hasEnded": "所选告警有部分已经结束",
			"alarmCount": "条告警",
			"roomDetail": "房间详情",
			"moreAlarmRecord": "更多告警记录",
			"noPopInThisLogin": "不再提醒",
			"PopInThisLogin": "恢复提醒",
			"unconfirmedAlarm": "所有未确认告警",
			"unfinishedAlarm": "所有未结束告警",
			"close": "关闭",
			"remark": "注释",
			"moreAlarm": "更多告警",
			"alarmLevel": "告警等级",
			"alarmBaseTypeName": "告警基类",
			"theSelected": "选中",
			"confirmAll": "确认所有",
			"inAll1": "共",
			"inAll2": "条告警",
			"inAllPage": "页",
			"inAll3": "条记录",
			"alarmOccurValue": "触发值",
			"alarmSource": "告警位置",
			"setLockSpan": "设置锁定时长",
			"lblLockSpan": "单位(秒)",
			"unlockPage": "解锁页面",
			"lockPage": "锁定页面",
			"noImg": "暂无图片",
			"oneAndTwoAlarm": "一级二级告警",
			"threeAndFourAlarm": "三级四级告警",
			"enableSuccess": "启用成功",
			"disabledSuccess": "禁用成功"
		},
		"airConditionerOverview": {
			"title": "空调组总览",
			"airConditionerGroupList": "空调组列表",
			"airConditionerList": "空调列表",
			"airConditionerDeviceId": "空调设备ID",
			"baseInfo": "基础信息",
			"backupPlan": "备用计划",
			"communicationOutage": "通讯中断",
			"closed": "关闭",
			"running": "运行中",
			"starting": "启动中",
			"stopping": "停用中",
			"backupInfo": "间隔{{date}}天更新，其中{{size}}台备选",
			"interval": "间隔",
			"intervalUpdate": "天更新，其中",
			"alternateQuantity": "台备选",
			"airConditionGroupOnSuccess": "空调组启动成功!",
			"airConditionGroupOnFail": "空调组启动失败!",
			"airConditionGroupOffSuccsee": "空调组关闭成功!",
			"airConditionGroupOffFail": "空调组关闭失败!",
			"backupStatusOnSuccess": "备用计划启动成功!",
			"backupStatusOffSuccess": "备用计划停止成功!",
			"backupStatusOnFail": "备用计划启动失败!",
			"backupStatusOffFail": "备用计划停止失败!",
			"temperatureTitle": "近30天平均温度曲线",
			"humidityTitle": "近30天平均湿度曲线",
			"cooling": "制冷中",
			"heating": "制热中"
		},
		"airConditionerShadow": {
			"name": "名称",
			"temperature": "温度(℃)",
			"humidity": "湿度(%)",
			"switchingStatus": "开关机状态",
			"refrigerationStatus": "制冷状态",
			"heatingStatus": "制热状态",
			"airConditionGroup": "空调组",
			"isBackup": "是否备用",
			"isStandby": "备用",
			"notStandby": "未备用",
			"isBackupException": "是否例外",
			"isException": "是",
			"notException": "否",
			"nameNotNull": "设备名称不能为空",
			"temperatureNotNull": "温度不能为空",
			"humidityNotNull": "湿度不能为空",
			"airConditionGroupNotNull": "空调组名称不能为空",
			"airConditionGroupIsActive": "不能选择处于启动状态空调组",
			"device": "关联设备",
			"deviceNotNull": "关联设备不能为空",
			"title": "空调模型",
			"relationShadow": "关联空调",
			"selectedShadow": "已选空调",
			"deviceName": "设备名称",
			"deviceType": "设备类型",
			"relationShadowNotNull": "关联shadow不能为空"
		},
		"airConditionerGroup": {
			"title": "空调组配置",
			"name": "名称",
			"defaultTemperature": "预设温度(℃)",
			"maxTemperature": "温度上限(℃)",
			"maxTemperatureTips": "温度上限必须大于预设温度",
			"minTemperature": "温度下限(℃)",
			"minTemperatureTips": "温度下限必须小于预设温度",
			"defaultHumidity": "预设湿度(%)",
			"maxHumidity": "湿度上限(%)",
			"maxHumidityTips": "湿度上限必须大于预设湿度",
			"minHumidity": "湿度下限(%)",
			"minHumidityTips": "湿度下限必须小于预设湿度",
			"backupStatus": "备用计划状态",
			"updateInterval": "轮询间隔(天)",
			"alternateQuantity": "备用数量",
			"status": "状态",
			"description": "描述",
			"nameNotNull": "空调组名称不能为空",
			"defaultTemperatureNotNull": "预设温度不能为空",
			"defaultHumidityNotNull": "预设湿度不能为空",
			"updateIntervalNotNull": "轮询间隔天数不能为空",
			"alternateQuantityNotNull": "备用数量不能为空",
			"formatInvalid": "输入数字只能在0-100之间",
			"enableValue": "启用",
			"unEnableValue": "未启用",
			"standByPlan": "空调组备用计划",
			"minRunningQuantity": "最小运行台数",
			"minRunningQuantityNotNull": "最小运行台数不能为空"
		},
		"battery": {
			"batteryModel": "蓄电池模型",
			"batteryString": "蓄电池组",
			"batteryStringOverview": "蓄电池总览",
			"factry": "厂家",
			"type": "型号",
			"batteryCellModelId": "电池模型ID",
			"deviceId": "设备ID",
			"deviceName": "设备名称",
			"batteryStringId": "蓄电池ID",
			"vendor": "电池厂商",
			"model": "电池型号",
			"voltageType": "单体电压类型",
			"ratedVoltage": "额定电压(V)",
			"ratedCapacity": "额定容量(Ah)",
			"initialIR": "初始内阻(μΩ)",
			"floatChargeVoltage": "浮充电压(V)",
			"evenChargeVoltage": "均充电压(V)",
			"tempCompensationFactor": "温度补偿系数(mV)",
			"terminationVoltage": "终止电压(V)",
			"chargingCurrentLimitFactor": "充电限流系数(CA)",
			"dischargeCurrentLimitFactor": "放电限流系数(CA)",
			"length": "长度(mm)",
			"width": "宽度(mm)",
			"height": "总高(mm)",
			"weight": "重量(Kg)",
			"vendorNotNull": "电池厂商不能为空",
			"modelNotNull": "电池型号不能为空",
			"ratedVoltageNotNull": "电压类型不能为空",
			"ratedCapacityNotNull": "额定容量不能为空",
			"initialIRNotNull": "初始内阻不能为空",
			"floatChargeVoltageNotNull": "浮充电压不能为空",
			"evenChargeVoltageNotNull": "均充电压不能为空",
			"tempCompensationFactorNotNull": "温度补偿系数不能为空",
			"terminationVoltageNotNull": "终止电压不能为空",
			"chargingCurrentLimitFactorNotNull": "充电限流系数不能为空",
			"dischargeCurrentLimitFactorNotNull": "放电限流系数不能为空",
			"standbyPowerNotNull": "备用时间不能为空",
			"startUsingTimeNotNull": "启用时间不能为空",
			"cellCountNotNull": "电池节数量不能为空",
			"maxChargingCurrentNotNull": "最大均充充电电流不能为空",
			"currentTransformerTypeNotNull": "电流互感器类型不能为空",
			"batteryStringNameNotNull": "设备名称不能为空",
			"deviceNotNull": "关联的设备不能为空",
			"batteryModelNotNull": "关联的设备模型不能为空",
			"lengthNotNull": "长度不能为空",
			"widthNotNull": "宽度不能为空",
			"heightNotNull": "总高不能为空",
			"weightNotNull": "重量不能为空",
			"ratedCapacityInvalid": "输入的格式不正确",
			"batteryStringName": "设备名称",
			"standbyPower": "备用时间(min)",
			"timeLimitNotEmpty": "备用时间不能为空",
			"timeLimitType": "备用时间只能为数字",
			"timeLimitMax": "备用时间不允许超过9999分钟",
			"timeLimitMin": "备用时间不允许小于1分钟",
			"cellCountLimitMax": "电池组串联单体总数至多999节",
			"cellCountLimitMin": "电池组串联单体总数至少1节",
			"startUsingTime": "启用时间",
			"cellCount": "电池节数量",
			"currentTransformerType": "电流互感器类型",
			"maxChargingCurrent": "最大均充充电电流(C)",
			"maxFloatChargeVoltage": "最大浮充电压(V)",
			"device": "关联设备",
			"alarm": "告警",
			"battery": "蓄电池组",
			"lessThanOneYear": "1年以下",
			"one2ThreeYear": "1~3年",
			"three2FiveYear": "3~5年",
			"moreThanFiveYear": "5年以上",
			"usagePeriod": "使用年限",
			"noAlarm": "无告警",
			"hasAlarm": "有告警",
			"communicationOutage": "通讯中断",
			"doubleOutofRangeValue": "输入的数值不能大于999999.99",
			"invalidInput": "输入的格式不正确"
		},
		"batteryStringDash": {
			"alarmcategory": "告警分类统计",
			"batteryList": "电池列表",
			"vendor": "厂家分类统计",
			"usedYear": "使用年限",
			"maxRelatedDeviceNum": "最大关联设备数不能超过："
		},
		"batteryCell": {
			"realData": "实时数据",
			"performCompare": "性能对比",
			"realDischarge": "实时放电曲线",
			"hisdischarge": "历史放电曲线",
			"alarms": "当前告警",
			"controls": "控制列表",
			"batteryCellOverview": "电池详情",
			"vendorModel": "厂商/型号",
			"totalVoltage": "电池总电压(V)",
			"totalCurrent": "电池总电流(A)",
			"temperature": "环境温度(℃)",
			"maxMinTemp": "电池温度 最高/最低(℃)",
			"humidity": "环境湿度",
			"averageVoltage": "平均电压(V)",
			"maxMinVoltage": "电压 最高/最低(V)",
			"averageIR": "平均内阻(μΩ)",
			"maxMinIR": "内阻 最高/最低(μΩ)",
			"batteryCellCount": "节单体",
			"realTimeSignal": {
				"title": "实时信号",
				"batteryCellId": "编号",
				"batteryCellName": "名称",
				"cellTemp": "单体温度",
				"cellTempStatus": "温度状态",
				"cellVoltage": "单体电压",
				"cellVoltageStatus": "电压状态",
				"cellIR": "单体内阻",
				"stopVoltage": "放电截止电压(V):",
				"stopCapacity": "放电截止容量(AH):",
				"dischargeSpan": "放电时长(Min):",
				"cellIRStatus": "内阻状态"
			},
			"performanceContrast": {
				"title": "性能对比",
				"voltage": "电压对比",
				"temperature": "温度对比",
				"IR": "内阻对比",
				"cell": "单体",
				"emptyTips": "当前模式下没有符合的数据！"
			},
			"dischargesupervision": {
				"title": "放电测试",
				"curve": "放电曲线",
				"triggerconditions": "配置触发条件",
				"startdischarge": "开始放电",
				"enddischarge": "结束放电",
				"dischargetime": "已放时长",
				"dischargevoltage": "放电电压",
				"dischargeCurrent": "放电电流",
				"dischargecapacity": "已放容量",
				"cellTemperature": "单体温度",
				"cellVoltage": "单体电压",
				"dischargevoltageexceed": "放电电压超限",
				"dischargecapacityexceed": "已放容量超限",
				"dischargetimeexceed": "放电时长超限"
			},
			"historycurve": {
				"title": "历史曲线",
				"totalvoltagecurve": "总体放电曲线",
				"cellvoltagecurve": "单体放电曲线",
				"startDischargeTime": "开始时间",
				"endDischargeTime": "结束时间",
				"quickSelect": "快速选择",
				"cellvoltage": "单体电压",
				"dischargehistoryreport": "历史数据报表.xlsx",
				"dischargestatus": "充/放电状态：",
				"discharge": "放电",
				"constrasttime": "对比时间段：",
				"totalVoltage": "电池组总电压(V)",
				"totalCurrent": "电池组充放电电流(A)",
				"remainCapacityRate": "电池组剩余容量百分比",
				"cellName": "#单体",
				"voltage": "电压",
				"temp": "温度",
				"excelName": "蓄电池历史放电数据",
				"sheetName": "放电数据",
				"reportName": "放电报表.xlsx"
			}
		},
		"deviceSearch": {
			"deviceName": "设备名称",
			"devicePosition": "设备位置",
			"deviceSearch": "搜索",
			"alarmSeverity": "告警等级",
			"deviceSearchResult": "查询结果",
			"deviceResultName": "名称",
			"deviceResultBuilding": "楼栋",
			"deviceResultFloor": "楼层",
			"deviceResultRoom": "机房"
		},
		"devicedetail": {
			"deviceMask": "屏蔽设备",
			"cancelDeviceMask": "取消屏蔽设备",
			"deviceDiseases": "疾病指数",
			"triggerValue": "检测值",
			"diagnoseTime": "诊断时间",
			"itemName": "检测项目",
			"realTimeData": "实时数据",
			"assetInformation": "资产信息",
			"deviceBook": "机历簿",
			"healthIndex": "健康指数",
			"subhealthIndex": "亚健康指数",
			"description": "参考值",
			"idx": "编号",
			"exportName": "信号告警数据"
		},
		"assetInformation": {
			"assetBasicInfo": "基本信息",
			"assetId": "资产ID",
			"assetName": "资产名称",
			"assetCode": "资产编码",
			"quantity": "数量",
			"validity": "有效期",
			"startUsingTime": "启用时间",
			"description": "描述",
			"assetEntityName": "资产实体名称",
			"vendorName": "供应商",
			"unitName": "单位名称",
			"endUserName": "使用人",
			"assetsPhysicalInfo": "物理信息",
			"rotation": "旋转角度",
			"heightToFloor": "离地高度",
			"modelName": "外观模板",
			"assetTagInfo": "标签信息",
			"tagName": "标签名称",
			"tagType": "标签类别",
			"codeType": "编码类型",
			"isPasted": "是否标贴",
			"tagStyleName": "标签样式",
			"assetExtendProperty": "拓展属性",
			"propertyName": "属性名称",
			"propertyValue": "属性值"
		},
		"pointsList": {
			"alarmSeverity": "等级",
			"currentValue": "值",
			"corePointName": "名称",
			"alarmState": "状态",
			"alarmLevel": "等级",
			"pointName": "名称",
			"pointValue": "值",
			"pointStatus": "状态",
			"pointOperation": "操作",
			"statusStartedTip": "有告警",
			"statusNoStartTip": "未告警",
			"statusEndedTip": "告警已结束",
			"statusNoEndTip": "告警未结束",
			"statusConfirmdTip": "告警已确认",
			"statysNoConfirmTip": "告警未确认",
			"opSetValueTip": "设置值",
			"opMaskTip": "屏蔽告警",
			"opCancelMaskTip": "取消屏蔽告警",
			"opForceEndTip": "强制结束告警",
			"opConfirmTip": "确认告警",
			"points": "测点",
			"commandSet": "设置",
			"commandValue": "值",
			"commandCancel": "取消",
			"commandSend": "确定",
			"pleaseInput": "请输入设定值"
		},
		"login": {
			"noMenu": "该用户未配置菜单权限，无法进入系统，请联系管理员进行正确配置",
			"title": "用户登录",
			"usrname": "用户名",
			"personName": "申请人",
			"describe": "注册说明",
			"registerDescribe": "请输入申请人信息！",
			"pwd": "密码",
			"toggle": "看不清?",
			"register": "注册新用户",
			"forgotpwd": "忘记密码？",
			"userInvalid": "登录用户失效,请重新登录",
			"btn_login": "登录",
			"zh": "中文",
			"en": "EN",
			"welcome": "欢迎",
			"errInfo": "用户名或者密码错误或者验证码错误",
			"accountExpired": "登录用户已过期,请切换用户重新登录.",
			"accountDisabled": "登录用户不可用,请切换用户重新登录",
			"accountLocked": "登录用户被锁定,请切换用户重新登录",
			"accountOverError": "当前用户密码输入错误次数过多，请3分钟后再尝试",
			"overMaxOnlineUsers": "当前允许登陆用户数已满，请稍后再尝试",
			"passwordExpired": "密码已过期，请修改密码后重新登陆",
			"freezeAccount_1": "该用户已被冻结，",
			"freezeAccount_2": "秒后解除",
			"userNotFound": "用户名不存在",
			"wrongPassword": "用户名或密码错误。",
			"wrongPassword2": "总共可尝试次数：",
			"wrongAccessTime": "该账户当前时段禁止访问！",
			"wrongIPAccessTime": "该IP当前时段禁止访问！",
			"telephone": "手机号",
			"telephonePlaceholder": "手机号",
			"picCode": "图形码",
			"picCodePlaceholder": "图片验证码",
			"messageCode": "验证码",
			"messageCodePlaceholder": "短信验证码",
			"touristLogin": "访客登录",
			"forgetPassword": "忘记密码",
			"passwordNotInValidTime": "密码超过有效期，请联系管理员重置密码",
			"accountIvalidTimeoneMonth":"账号有效期不足一个月，如需继续使用，请尽快联系管理员延长有效期",
			"pwdLogin": "密码登录",
			"PhoneLogin": "免密登录",
			"getImgCode": "获取验证码",
			"resend": "重新发送",
			"imgCodeError": "图片验证码错误",
			"sendError": "验证码发送失败",
			"phoneCodeError": "短信验证码错误",
			"accountExpiringWarn": "帐户即将到期",
			"resetPassword": "忘记密码？",
			"passwordExpiring": "密码即将到期",
			"passwordExpiringPrompt": "请尽快修改密码，密码到期帐户将被锁定",
			"firstLogin": "首次登陆需要更改密码！",
			"noMenusPrompt": "帐号无授权！请联系系统管理员进行帐号授权！",
			"closeWindow": "返回",
			"usePwdLogin": "切换到使用帐户密码登录",
			"useSMScode": "切换到使用短信验证码登录",
			"second": "秒",
			"title2": "验证码登录",
			"usePwd": "用户登录",
			"useCode": "验证码登录",
			"securityFileIntegrityFail": "系统完整性失效，请联系管理员！",
			"noPhoneError": "该用户没有设置手机号！",
			"forcehack": "请勿暴力破解",
			"imageCodeError": "图形验证码错误！",
			"attachment": "附件上传",
			"attachmentPlaceholder": "请选择附件文件",
			"attachmentInfo": "支持图片、Word、Excel文件，单个文件不超过1MB"
		},
		"resetPassword": {
			"title": "重置密码",
			"continue": "下一步",
			"newPassword": "新密码",
			"confirmPassword": "确认密码",
			"passwordLess": "密码不能少于8位",
			"passwordInconsistent": "密码不一致",
			"resetFail": "更新失败",
			"resetSuccess": "更新成功",
			"low": "弱",
			"middle": "一般",
			"high": "强",
			"rule": "请使用不少于8位且包含数字、字母、特殊字符其中两种的组合密码"
		},
		"logout": {
			"title": "退出登录",
			"changeLang": "中英切换"
		},
		"profile": {
			"title": "个人中心"
		},
		"system": "系统",
		"systemAdmin": "系统管理员",
		"dateOptions": {
			"January": "一月",
			"February": "二月",
			"March": "三月",
			"April": "四月",
			"May": "五月",
			"June": "六月",
			"July": "七月",
			"August": "八月",
			"September": "九月",
			"October": "十月",
			"November": "十一月",
			"December": "十二月",
			"Su": "周日",
			"Mo": "周一",
			"Tu": "周二",
			"We": "周三",
			"Th": "周四",
			"Fr": "周五",
			"Sa": "周六"
		},
		"common": {
			"yes": "是",
			"no": "否",
			"all": "全部",
			"fontsize": "字号",
			"noData": "没有符合条件的数据",
			"notData": "无数据",
			"aboutAccountName": "  ，并删除相关帐号: ",
			"action": "操作",
			"tip": "提示",
			"commit": "提交",
			"reject": "作废",
			"commitSuccess": "提交成功",
			"startTime": "开始时间",
			"startTimeNotNull": "开始时间不可为空",
			"endTime": "结束时间",
			"endTimeNotNull": "结束时间不可为空",
			"endTimeNotValid": "结束时间必须大于开始时间",
			"select": "选择",
			"clear": "清空",
			"upload": "上传",
			"uploadImage": "上传图片",
			"uploadAll": "上传全部",
			"uploadFailure": "上传附件失败",
			"download": "下载",
			"progress": "进度",
			"downloadAll": "下载全部",
			"downloadSuccess": "下载附件成功",
			"downloadFailure": "下载附件失败",
			"reason": "原因",
			"time": "时间",
			"comments": "意见",
			"maxLength128": "最大长度为128",
			"ok": "确定",
			"cancel": "取消",
			"modify": "修改",
			"goBack": "返回",
			"add": "新增",
			"edit": "编辑",
			"editCantNot": "无法修改",
			"detail": "详情",
			"rank": "机架设备",
			"error": "错误",
			"confirmDelete": "请确认是否删除: ",
			"confirmDelete2": "请确认是否删除？",
			"delete": "删除",
			"deleteSuccess": "删除成功",
			"deleteFailed": "删除失败",
			"deleteCantNot": "无法删除",
			"deleted": "已删除",
			"succeed": "成功",
			"failed": "失败",
			"find": "查询",
			"findAll": "显示全部",
			"resetQuery": "重置查询",
			"reset": "重置",
			"sort": "排序",
			"close": "关闭",
			"day": "天",
			"hour": "小时",
			"minute": "分",
			"second": "秒",
			"exitEdit": "退出编辑",
			"lineNumber": "序号",
			"comingSoon": "即将上线，敬请期待",
			"noInformation": "暂时没有收录基本信息",
			"searchFilter": "请输入名称查询",
			"ascendingText": "升序",
			"descendingText": "降序",
			"photograph": "拍照",
			"openCameraFailed": "打开摄像头失败, 请检查摄像头是否接好！",
			"save": "保存",
			"saveSuccess": "保存成功",
			"saveFailed": "保存失败",
			"export": "导出",
			"exportAll": "导出全部",
			"addTemplate": "制作模板",
			"templateList": "模板管理",
			"cardCopy": "卡复制",
			"cardLost": "卡丢失",
			"exportIng": "导出中......",
			"exportFailed": "导出失败",
			"exportZipSuccess": "导出成功",
			"import": "导入",
			"status": "状态",
			"selectIcon": "选择图标",
			"confirm": "确认",
			"end": "结束",
			"refresh": "刷新",
			"switchTo": "切换",
			"horizontal": "横向",
			"vertical": "纵向",
			"batchProcessing": "批量确认",
			"lockToOperation": "锁定操作",
			"unlockOperation": "解锁操作",
			"description": "描述",
			"backList": "返回列表",
			"itEquipmentList": "IT设备列表",
			"buildConfiguration": "生成配置",
			"uploadCnfig": "上传配置",
			"initSampler": "初始化采集配置",
			"allUpload": "上传所有设备",
			"oneUpload": "上传当前设备",
			"allDelete": "删除所有设备",
			"oneDelete": "删除当前设备",
			"deleteConfig": "删除配置",
			"bind": "绑定",
			"unbind": "解除绑定",
			"filterError": "过滤错误行",
			"columnExportTips": "列数超过Excel限制，请使用横向导出！",
			"notLicense3D": "3D可视化未授权",
			"exporting": "正在导出，请稍候",
			"alarmfittingTips1": "在 ",
			"alarmfittingTips2": " 发生告警收敛！收敛事件为 ",
			"alarmfittingTips3": " ,是否前往告警收敛页面?",
			"clearfilter": "清除过滤条件",
			"pleaseInput": "请输入",
			"pleaseSelect": "请选择",
			"keywords": "关键字",
			"total": "共",
			"items": "条",
			"noMore": "没有更多了",
			"forward": "前进",
			"backward": "后退"
		},
		"selfprocotocol": {
			"ipnotempty": "ip地址不能为空",
			"ipformarterror": "ip地址格式错误",
			"portnotempty": "端口号不能为空",
			"portformarterror": "端口号格式错误",
			"addrnotempty": "地址不能为空",
			"addrformarterror": "地址格式错误",
			"dllnotempty": "动态库名称不能为空",
			"intervalnotempty": "采集周期不能为空",
			"intervalformarterror": "采集周期格式错误",
			"cannotupload": "您已上传配置，不能重复上传",
			"noerrorrow": "当前无错误行",
			"uploadsuccess": "上传成功",
			"buildconfigerror1": "生成配置当前选中行里存在错误，不执行生成配置操作，请检查基础配置",
			"buildconfigerror2": "生成配置当前选中行里存在未上传过配置，不执行生成配置操作，请检查基础配置",
			"sametemplate": "引用相同模板设备的配置",
			"configexsit": "已生成配置",
			"notchecked": "您未勾选任何配置选项",
			"initsuccess": "初始化成功",
			"noupload": "您未上传过配置",
			"deletesuccess": "删除成功",
			"defaulttemplatecannotdelete": "默认模板不能删除",
			"readpropertynotempty": "读写属性不能为空",
			"readpropertyformaterror": "读写属性格式错误",
			"edit": "编辑",
			"deleteedit": "删除配置"
		},
		"drivertemplate": {
			"name": "模板名称",
			"description": "描述",
			"templatedrivertype": "模板类型",
			"isDefaultTemplate": "是否默认模板",
			"placeholder": "请输入检索关键字",
			"intputName": "输入名称",
			"intputDescription": "输入描述",
			"editTree": "编辑树结构",
			"isDisk": "是否是目录",
			"addString": "添加占位符",
			"path": "结构路径",
			"inputpath": "输入结构路径",
			"NotNull": "值不能为空",
			"templateName": "结构名称",
			"inputtemplateName": "输入结构名称",
			"isUpload": "是否需要上传",
			"Upload": "上传文件",
			"UploadCondition": "上传条件",
			"UploadWhenCreate": "创建时上传",
			"UploadWhenGenrate": "生成时上传",
			"isFill": "是否需要填充文件内容",
			"treeStruct": "模板结构",
			"addRoot": "新增根节点",
			"toBack": "返回上一层",
			"folder": "上级文件夹"
		},
		"mask": {
			"batchMaskAlarm": "批量屏蔽告警",
			"maskManagement": "屏蔽管理",
			"deviceMaskList": "已屏蔽设备",
			"alarmMaskList": "已屏蔽告警",
			"deviceBulkblock": "批量屏蔽",
			"blockAll": "全部屏蔽",
			"maskMorethan": "所选屏蔽超过1000条，请减少范围",
			"inputStartTime": "请输入开始时间",
			"inputEndTime": "请输入结束时间",
			"deviceId": "设备ID",
			"deviceName": "设备名称",
			"deviceCategory": "设备类型",
			"deviceLocation": "设备位置",
			"userId": "屏蔽操作人ID",
			"userName": "屏蔽操作人",
			"comment": "屏蔽原因",
			"operationTime": "操作时间",
			"startTime": "屏蔽开始时间",
			"endTime": "屏蔽结束时间",
			"confirmDeleteDeviceMask": "请确认是否删除设备屏蔽: ",
			"confirmDeleteAllDeviceMask": "请确认是否删除全部设备屏蔽",
			"commentNotEmpty": "屏蔽原因不能为空",
			"corePointName": "告警名称",
			"confirmDeleteAlarmMask": "请确认是否删除告警屏蔽: ",
			"confirmDeleteAllAlarmMask": "请确认是否删除全部告警屏蔽",
			"masking": "屏蔽设置状态",
			"actived": "屏蔽启用状态",
			"confirmBulkblock": "确定批量屏蔽告警",
			"confirmBulkblock2": "确定批量屏蔽设备",
			"confirmUnblock": "确认解除屏蔽",
			"confirmUnblockBatch": "确认批量解除",
			"confirmUnblockAll": "确认解除所有屏蔽",
			"confirmDeleteAll_1": "确认解除",
			"confirmDeleteAll_2": "下的所有屏蔽",
			"confirmDeleteAll_3": "下的选中屏蔽",
			"confirmDeleteAll_2_1": "确认解除所有屏蔽",
			"confirmDeleteAll_3_1": "确认解除选中屏蔽",
			"mask": "屏蔽",
			"unblock": "解除屏蔽",
			"UnblockBatch": "批量解除屏蔽",
			"UnblockAll": "解除所有屏蔽",
			"blockByConfig": "配置筛选条件",
			"timeJudge": "开始时间不能高于结束时间",
			"timeNotEmpty": "请填写屏蔽时间",
			"ismasked": "已设置",
			"isnotmasked": "未设置",
			"isActived": "已生效",
			"isnotActived": "未生效",
			"operation": "操作",
			"resName": "层级名称",
			"eqName": "设备名称",
			"eventID": "告警ID",
			"eventName": "告警名称",
			"maskTimePeriod": "屏蔽时段",
			"set": "设置",
			"notSelectMask": "请先选择屏蔽对象！",
			"editParams": "编辑查询参数",
			"jumpTo": "跳转到"
		},
		"alarmVideoLink": {
			"linkName": "告警联动视频实例名称",
			"linkDescription": "告警联动视频实例简介",
			"linkType": "联动类型",
			"snapshotCount": "抓图张数",
			"snapshotInterval": "抓图间隔（毫秒）",
			"videoPopupWindow": "视频弹窗",
			"snapshot": "抓图",
			"alarmStatus": "抓图关联告警状态",
			"alarmStart": "告警开始",
			"alarmEnd": "告警结束",
			"notificationStatus": "启用状态",
			"nameNotNull": "不能为空",
			"linkRule": "告警联动视频规则",
			"addAlarmTriggerRule": "新增告警触发规则",
			"setCamera": "配置告警和摄像头",
			"deviceBaseTypeName": "设备基类",
			"close": "关闭",
			"noMoreReminder": "不再提醒",
			"resumeReminder": "恢复提醒",
			"closeAllPreviews": "关闭所有预览",
			"equimentList": "设备名称列表",
			"alarmList": "告警列表",
			"cameraList": "摄像头列表",
			"alarmVideoLinkConfigTemplate": "配置告警和摄像头模板",
			"importConfig": "导入配置",
			"exportConfig": "导出配置",
			"alarmBaseTypeName": "告警基类"
		},
		"shiftManagement": {
			"shiftGroupId": "班组id",
			"shiftName": "班次名称",
			"shiftStartTime": "上班时间",
			"shiftEndTime": "下班时间",
			"shiftPeriod": "班次时长",
			"shiftColor": "班次颜色",
			"shiftGroupName": "班组名称",
			"shiftGroupDescrip": "描述",
			"addShiftGroup": "新增班组",
			"addShiftGroupPerson": "新增班组成员",
			"groupNumberEmpty": "班组成员不能为空",
			"editShiftGroup": "编辑班组",
			"shiftGroupList": "班组列表",
			"shiftPersonlist": "班组人员列表",
			"num": "序号",
			"shiftGroupPerson": "班组人员",
			"shiftGroupNameShort": "班组名称",
			"shiftGroupPersonShort": "班组人员",
			"shiftNameShort": "班次名称",
			"scheduleDate": "排班日期",
			"chooseShift": "选择班次",
			"editSchedule": "编辑排班",
			"addSchedule": "新增排班",
			"delSchedule": "您确定要删除当前排班吗？",
			"department": "部门",
			"role": "角色",
			"phone": "电话",
			"mobile": "手机号",
			"sequence": "排序",
			"moveUp": "上移",
			"moveDown": "下移",
			"chooseSevenDays": "只能选择7天",
			"choose7Days": "选择7天",
			"previousWeek": "上一周",
			"startTime": "选择7天开始时间",
			"nextWeek": "下一周"
		},
		"chartDataSeparateConfig": {
			"chartStyleName": "样式名称",
			"chartStyleOptionJson": "样式代码",
			"chartStyleThumbnail": "样式缩略图",
			"chartType": "适用图表类型",
			"codeEdit": "代码编辑",
			"nameNotNull": "不能为空",
			"chartCustomThemeName": "自定义主题名称",
			"echartsThemeName": "echarts主题名称",
			"chartDefaultTheme": "默认主题",
			"chartApiName": "接口名称",
			"chartApiCategory": "接口分类",
			"chartApiUrl": "接口地址",
			"chartApiMethod": "请求方法",
			"chartApiParamSchema": "接口参数",
			"chartApiParamIn": "接口参数位置",
			"chartApiTransformFunction": "接口结果变换函数",
			"addParam": "添加参数",
			"fieldName": "参数字段名",
			"fieldMeaning": "参数字段名含义",
			"paramArray": "参数格式数组",
			"paramRequired": "参数必须",
			"defaultValue": "参数默认值",
			"paramSource": "参数值来源",
			"input": "输入",
			"router": "路由",
			"dataDictionary": "数据字典",
			"component": "组件",
			"systemConfig": "系统参数",
			"sessionName": "sessionStorage名称",
			"paramComponent": "参数值来源组件",
			"paramDataDictionary": "参数值来源数据字典",
			"basicSignalSelectorComponent": "基类选择器",
			"treeSelectorComponent": "信号选择器",
			"indicatorSelectorComponent": "指标选择器",
			"timeRangeComponent": "数据时间选择器",
			"hierarchySelectorComponent": "层级选择器",
			"aiHierarchySelectorComponent": "AI层级选择器",
			"aiGroupSelectorComponent": "AI分组选择器",
			"selectHierarchy": "选择层级",
			"paramSetting": "参数设置",
			"addChildParam": "增加子参数",
			"routerName": "路由名称",
			"chartThemeJson": "主题代码"
		},
		"tts": {
			"commonConfig": "通用配置",
			"alarmConfig": "告警配置",
			"preAlarmConfig": "预警配置",
			"broadcastConfirmedPreAlarm": "已确认预警是否播报",
			"ttsPreAlarmEnable": "是否启用TTS预警语音通知",
			"strategyName": "TTS语音策略名称",
			"strategyType": "TTS语音类型",
			"alarm": "告警",
			"preAlarm": "预警",
			"internalMessage": "站内信",
			"strategyNameNotNull": "TTS语音策略名称不能为空",
			"description": "TTS语音策略简介",
			"enable": "启用状态",
			"effectiveStartTime": "生效开始时间",
			"positive": "过滤",
			"negative": "取反",
			"effectiveEndTime": "生效结束时间"
		},
		"alarmNotification": {
			"tTSSeverity": "TTS语音通知关联告警等级",
			"playbackOrder": "TTS语音通知播放顺序",
			"playbackOrder1": "1告警,2预警,3站内信",
			"playbackOrder2": "1告警,3站内信,2预警",
			"playbackOrder3": "2预警,1告警,3站内信",
			"playbackOrder4": "2预警,3站内信,1告警",
			"playbackOrder5": "3站内信,1告警,2预警",
			"playbackOrder6": "3站内信,2预警,1告警",
			"tTSAlarmStatus": "TTS语音通知关联告警状态",
			"tTSMsgURL": "TTS语音通知服务新消息URL",
			"ttsSpeakTimes": "TTS语音通知重复播报次数",
			"ttsSpeakRate": "TTS语音通知播报语速",
			"ttsSpeakRateRange": "最大值10，最小值0.1，步长0.1",
			"sortByEventLevel": "TTS按照告警等级优先播报",
			"ttsSpeakVolume": "TTS语音通知播报音量",
			"ttsSpeakVolumeRange": "最大值1，最小值0，步长0.1",
			"firstPushAlarm": "TTS不播报登陆时的活动告警",
			"tTSCycleMsgURL": "TTS语音通知服务循环播放消息URL",
			"alarmField": "告警字段",
			"alarmContentTemplate": "TTS语音通知告警开始内容模板",
			"alarmEndContentTemplate": "TTS语音通知告警结束内容模板",
			"alarmContentTemplateRemark": "备注：可在此编辑通知内容模板，{}内的属性会实际替换成相应的具体告警字段，请勿随意修改",
			"alarmPolicyName": "告警通知策略名称",
			"alarmPolicyNode": "告警策略流程节点",
			"selectIndicator": "选择指标",
			"selectSignal": "选择信号",
			"safeMsgName": "平安消息实例名称",
			"safeMsgDescription": "平安消息实例简介",
			"safeMsgTemplate": "平安消息模板",
			"safeMsgField": "平安消息模板字段",
			"alarmPolicyNameNotNull": "告警通知策略名称不能为空",
			"safeMsgTemplateNotNull": "平安消息模板不能为空",
			"safeMsgNameNotNull": "平安消息实例名称不能为空",
			"sendTime": "平安消息发送时刻",
			"receiver": "平安消息接收人",
			"sendCycle": "平安消息发送周期",
			"receiveMode": "平安消息接收方式",
			"sms": "短信",
			"voice": "语音",
			"email": "邮件",
			"indicatorAliasName": "指标别名",
			"bindMax": "最多绑定5条信号或指标",
			"templateLimit": "备注：因受短信猫长度限制，模板超出60个汉字将分成两条短信发送",
			"description": "告警通知策略简介",
			"notificationTemplate": "告警通知模板",
			"notificationTemplateNotNull": "告警通知模板不能为空",
			"usedStatusNotDelete": "该策略当前是使用状态不允许删除",
			"safeMsgUsedStatusNotDelete": "该实例当前是使用状态不允许删除",
			"notificationStatus": "启用状态",
			"eventStart": "告警开始",
			"eventEnd": "告警结束",
			"eventDelay": "告警延时(秒)",
			"filterConditions": "过滤条件",
			"hasSet": "已设置",
			"set": "设置",
			"view": "查看",
			"alarmNotificationPolicyFilter": "告警通知策略过滤器",
			"ttsFilter": "TTS语音过滤条件",
			"notificationProcess": "通知流程",
			"gateWayList": "网关列表",
			"sNotificationPolicyProcess": "的告警策略流程",
			"save": "保存",
			"back": "返回",
			"edit": "编辑",
			"closeEditMode": "关闭编辑状态",
			"callandmessage": "同时拨打电话和发短信",
			"onlyCallUp": "仅拨打电话",
			"onlySentMessage": "仅发短信",
			"name": "姓名",
			"title": "头衔",
			"emailAddress": "邮箱地址",
			"phone": "电话",
			"jobNumber": "工号",
			"mustSelect": "必须选择一个或多个发送的人员",
			"mustSelectPersonOrShiftGroup": "必须选择一个或多个发送的人员/班组",
			"selectSentPerson": "选择发送的人员",
			"selectSentPersonOrShiftGroup": "选择发送的人员/班组",
			"onlyShowSelected": "只显示已选用户",
			"onlyShowSelectedShiftGroup": "只显示已选班组",
			"sendPolicy": "发送策略",
			"timeoutSetting": "设置超时时间",
			"Signal": "信号",
			"ComplexIndex": "指标",
			"AlarmCount": "告警总数",
			"timeout": "超时时间（秒）",
			"filterFirst": "请先选择过滤条件",
			"selectPerson": "选择人员",
			"selectShiftGroup": "选择班组",
			"cyclicBroadcastActiveAlarm": "循环播报活动告警",
			"notBroadcastConfirmedAlarm": "已确认告警不播报",
			"notBroadcastEndAlarm": "已结束告警不播报",
			"alarmEnable": "是否启用TTS网页语音通知",
			"alarmMsg": "告警消息",
			"prealarmMsg": "预警消息",
			"systemMsg": "系统消息",
			"id": "序号",
			"content": "内容",
			"emptyQueue": "清空播报队列",
			"emptyQueueConfirm": "确认清空播报队列?",
			"emptyQueueSuccess": "清空播报队列成功",
			"ttsQueue": "语音播报队列"
		},
		"gatewayList": {
			"title": "网关服务列表",
			"save": "保存",
			"cancel": "取消",
			"delete": "删除",
			"gatewayType": "网关类型",
			"gatewayURL": "网关URL",
			"description": "备注",
			"name": "名称",
			"add": "新增",
			"useAdd": "使用新增网关",
			"useEdit": "使用已有网关",
			"confirmDelete": "确认删除?",
			"saveError": "请先完善参数！"
		},
		"scene": {
			"sceneName": "名称",
			"status": "状态",
			"subSceneName": "名称",
			"operation": "操作",
			"currentScene": "当前主场景",
			"useSubScene": "使用该场景"
		},
		"userGraphicPage": {
			"userName": "用户名",
			"graphicPageName": "组态名",
			"departmentName": "部门",
			"editUserGraphicPage": "编辑用户组态",
			"delUserGraphicPage": "您确定要删除当前用户组态吗？"
		},
		"roleGraphicPage": {
			"roleName": "角色",
			"graphicPageName": "组态名",
			"editRoleGraphicPage": "编辑角色组态",
			"delRoleGraphicPage": "您确定要删除当前角色组态吗？"
		},
		"airConditionerAiGroup": {
			"aiGroupTitle": "AI空调分组",
			"aiOverheatingTitle": "AI热点区域",
			"aiGroupName": "AI分组名称",
			"aiGroupId": "AI分组ID",
			"description": "分组描述",
			"aiOverheatingAreaName": "AI热点区域名称",
			"aiOverheatingAreaId": "AI热点区域ID",
			"overheatingDescription": "区域描述",
			"inputEmpty": "未填写",
			"alreadyExist": "该名称已存在",
			"selectEmpty": "未选择",
			"deleteCode412": "分组下有关联热点区域,不允许删除",
			"relatedAiGroup": "关联AI分组",
			"alreadyRelatedAiGroup": "已关联AI分组",
			"confirmSelect": "确认选择",
			"confirmSelectAiGroup": "选择分组将会更改空调及关联测点所属AI组，确认选择？",
			"notSelectAiGroup": "需先选择AI分组"
		},
		"idcDeviceMonitor": {
			"relatedEventConfig": "关联事件配置",
			"signalConfig": "信号配置",
			"signalNotRelateEvent": "该信号未关联事件",
			"signalBatchApply": "信号批量应用",
			"alarmConditionApplyInEquip": "告警规则设备内批量应用",
			"alarmConditionBatchApplyAcrossEquip": "告警规则跨设备批量应用",
			"applyItem": "应用项",
			"selectedSignal": "已选信号",
			"deviceToBeApplied": "待应用设备",
			"selectAllDevice": "选中所有设备",
			"onlyShowSelectedDevice": "只显示所有选中的设备",
			"apply": "应用",
			"signalName": "信号名称",
			"storagePeriod": "存盘周期（秒）",
			"threshold": "阈值",
			"absoluteThreshold": "绝对值阈值",
			"percentageThreshold": "百分比阈值",
			"statisticalPeriod": "统计周期（时）",
			"equipmentPosition": "设备位置",
			"pleaseSelectSignal": "请选择要应用的信号",
			"pleaseSelectApplyItem": "请选择要应用的项",
			"pleaseSelectApplyDevice": "请选择待应用的设备",
			"batchApplySuccess": "批量应用成功",
			"batchApplyFail": "批量应用失败",
			"coverEventCondition": "全覆盖选中的规则（新事件的原规则会删除）",
			"applyMethod": "应用方式",
			"alarmRule": "告警规则",
			"signalListToSelect": "可选信号列表",
			"selectAllSignal": "选中所有信号",
			"onlyShowSelectedSignal": "只显示所有选中的信号",
			"pleaseSelectApplyCondtion": "请选择要应用的规则",
			"selectedSignalApplySuccess": "所选信号全部应用成功！",
			"selectedSignalAllNotRelateEvent": "所选信号全都未关联事件！规则无法应用",
			"partOfSignalApply": "以上信号未关联事件！规则不能应用,其他信号应用成功",
			"alarmLevelNotFound": "告警等级无匹配：",
			"noAlarmLevel": "无告警等级",
			"canNotConfigBatteryOrHvc": "电池或高压直流设备的模板较大批量动态配置有风险，系统已设置为关闭，如有需要请联系管理员在系统设置里打开开关",
			"noDynamicConfigPermission": "无动态配置权限",
			"online": "在线",
			"offline": "离线",
			"pleaseEnterStartTime": "请输入开始时间",
			"pleaseEnterEndTime": "请输入结束时间",
			"pleaseEnterReason": "请输入工程注释",
			"stationOnProjectCannotMask": "局站处于工程状态中，无法设置屏蔽",
			"stationOnMaskCannotProject": "局站处于屏蔽中，无法设置工程状态",
			"stationAction": "操作",
			"stationConnectState": "局站在线状态",
			"visible": "是否可见",
			"isEnable": "是否有效",
			"stationDevice": "局站设备",
			"returnToTree": "返回设备树",
			"historySignal": "历史信号",
			"signalDate": "信号时间",
			"signalValue": "信号值",
			"nameExist": "该名称已存在",
			"noData": "无数据",
			"allSignal": "所有信号",
			"aSignal": "模拟信号",
			"dSignal": "开关信号",
			"showPrecision": "显示精度(格式)",
			"staticsPeriod": "统计周期(时)",
			"absThreshold": "绝对值阀值",
			"startExpression": "开始条件",
			"suppressExpression": "抑制条件",
			"canOnlyDragEquipNode": "只能排序设备节点",
			"canOnlyDropOnSameParentNode": "节点只能放置在当前父节点下",
			"noCurrentSignalValue": "当前无信号值"
		},
		"virtualEquipment": {
			"virtualEquipment": "虚拟设备",
			"virtualSignal": "虚拟信号",
			"instanceSignal": "实例信号",
			"virtualEquipmentName": "虚拟设备名",
			"virtualStationName": "虚拟设备下挂局站",
			"virtualHouseName": "虚拟设备下挂局房",
			"virtualMonitorUnit": "虚拟设备下挂设备监控单元",
			"virtualEquipmentTemplate": "虚拟设备设备模板",
			"virtualEquipmentType": "虚拟设备类型",
			"virtualPortName": "虚拟设备下挂COM口",
			"equipmentInfo": "设备详情",
			"batchAdd": "批量生成",
			"export": "导出选中配置",
			"exportAll": "全部导出",
			"virtualSignalName": "虚拟信号名",
			"originalEquipmentName": "源设备名",
			"originalSignalName": "源设备信号名",
			"originalChannelNo": "源设备信号通道",
			"virtualSamplerUnitName": "虚拟设备下挂采集单元",
			"succeedOrNot": "是否成功",
			"feedback": "结果反馈",
			"importErrotMsg": "导入错误，是否下载错误信息？",
			"equipmentList": "设备列表",
			"signalList": "信号列表",
			"relatedEventList": "关联事件列表",
			"virtualEquipPreview": "虚拟设备预览",
			"signalName": "信号名",
			"eventName": "事件名",
			"equipmentName": "设备名",
			"houseName": "房间名",
			"selectedSignalNum": "已选信号",
			"selectedEventNum": "已选事件",
			"removeConfigAll": "移除所有配置",
			"createConfig": "生成配置",
			"pleaseEnterVitualEqName": "请输入设备名称",
			"pleaseSelectMonitor": "请选择",
			"pleaseSelectEqType": "请选择",
			"virtualComPort": "虚拟设备下挂COM口",
			"pleaseSelectComPort": "请选择",
			"pleaseSelectConfig": "请先从左侧栏选择配置",
			"createVirtualEqSuccess": "已完成虚拟设备生成，需手动生成并下发配置",
			"createVirtualEqNameExist": "系统中已存在同名设备，请更换名称重试",
			"saveSelectConfig": "保存选中配置",
			"removeSelectConfig": "移除选中配置",
			"virtualEqNameExist": "该名称已存在",
			"confrimRemoveAllConfig": "确认移除所有配置吗？"
		},
		"about": {
			"officialVersion": "正式版",
			"trialVersion": "试用版",
			"featureList": "功能列表",
			"expireDate": "到期时间",
			"updateLicense": "更新License",
			"update": "更新",
			"actived": "已激活",
			"inactivated": "未激活",
			"exportC2Vfile": "导出C2V文件",
			"importV2Cfile": "导入V2C文件",
			"select": "选择",
			"uploadV2Ctips": "请上传V2C文件",
			"clientName": "客户名称",
			"contractNo": "合同号",
			"contractNonotnull": "合同号不能为空！",
			"uploadSuccess": "上传成功",
			"uploadFail": "上传失败",
			"uploadFail2": "上传失败，请检查文件格式",
			"noLicense": "功能未激活，请购买正式版License！",
			"licenseExpiringSoon": "License即将到期,请及时更新License",
			"licenseExpired": "License已到期,请及时更新License",
			"licenseError": "License存在异常,请及时更新License"
		},
		"project": {
			"startTime": "开始时间",
			"endTime": "结束时间",
			"batchSet": "批量设置工程状态",
			"batchRelease": "批量解除工程状态",
			"allRelease": "解除所有",
			"setProject": "设置工程",
			"noTime": "请输入完整的时间段！",
			"updateError": "有设备更新失败！",
			"confiltc": "更新失败的设备",
			"reason": "原因",
			"confirmDelete": "确认删除?"
		}
	},
	"workflow": {
		"common": {
			"desktop": "流程管理",
			"users": "用户管理",
			"groups": "组",
			"basicOperation": "流程基本操作",
			"basicInfo": "流程基本信息",
			"attachment": "附件",
			"attachmentList": "附件列表",
			"deleteAttachment": "删除附件",
			"approval": "审批",
			"agree": "同意",
			"disagree": "不同意",
			"approvedRecord": "审批记录",
			"currentApproval": "当前处理人",
			"delegate": "转发",
			"delegateTo": "转发给",
			"processDelegate": "流程转发",
			"initiatedBy": "创建人",
			"timeOfInitiation": "创建时间",
			"currentNode": "当前环节",
			"signature": "签名",
			"newWorkflow": "新建",
			"queryWorkflowHistory": "流程历史查询",
			"leaveWorkflow": "请假单",
			"alarmWorkOrder": "告警工单",
			"powerObsolescence": "动力设备报废",
			"assetDelivery": "资产备件出库",
			"itWorkflow": "IT设备上下架",
			"maintenanceJob": "维护作业",
			"attachmentSizeNotAllowed": "附件大小必须小于1M",
			"onlyOneAttachment": "仅允许上传一个附件",
			"approver": "审批人",
			"approvedTime": "审批时间",
			"approvedResult": "审批结果",
			"remark": "备注",
			"processInstanceId": "流程ID",
			"applicantName": "发起人",
			"invalidStartTime": "请选择开始日期!",
			"invalidEndTime": "请选择结束日期!",
			"historicApply": "申请历史",
			"taskName": "任务环节",
			"operator": "处理人"
		},
		"todoList": {
			"title": "待办事项",
			"processDefinitionName": "流程名称",
			"name": "当前节点",
			"applyer": "发起人",
			"createTime": "发起时间",
			"duringTime": "滞留时间",
			"opeation": "操作",
			"day": "天",
			"hour": "小时",
			"minute": "分",
			"second": "秒"
		},
		"history": {
			"title": "查询条件",
			"startTime": "填单日期  从",
			"to": "至",
			"reset": "重置",
			"search": "查询",
			"searchResult": "查询结果"
		},
		"todoDetail": {
			"title": "待办任务处理"
		},
		"historyProcess": {
			"title": "流程详细信息"
		},
		"operation": {
			"remarkValidator": "意见不可为空，长度为2到128",
			"commitFailure": "提失交败",
			"claimFailure": "任务认领失败",
			"updateFailure": "获取数据失败",
			"transferFailure": "任务转发失败",
			"createFailure": "新建流程失败",
			"uploadFailure": "上传附件失败",
			"downloadFailure": "下载附件失败",
			"deleteAttachmentFailure": "删除附件失败",
			"initFailure": "初始化数据失败"
		},
		"leave": {
			"fillInRequest": "填写申请",
			"endProcess": "流程结束",
			"leaveType": "请假类型",
			"leaveTypeNotNull": "请假类型不可为空",
			"endTimeNotValid": "结束时间必须大于开始时间",
			"generalHoliday": "公休",
			"sickLeave": "病假",
			"leaveInLieu": "调休",
			"casualLeave": "事假",
			"marriageLeave": "婚假",
			"leaveReasonValidator": "请假原因不可为空，长度为6到128",
			"managementApproval": "部门领导审批",
			"hrApproval": "人事审批",
			"userToDelegateToNotNull": "待转发人不允许为空",
			"userNotFound": "找不到指定的用户",
			"delegateSuccess": "转发成功",
			"newLeaveRequest": "新建申请单"
		},
		"deleteSuccess": "删除成功!",
		"user": {
			"title": "用户",
			"cardtitle": "用户信息",
			"placeholder": "请输入检索关键字",
			"userId": "用户ID",
			"userName": "用户名",
			"userEmail": "邮箱",
			"action": "操作",
			"confirmDeleteUser": "请确认是否删除用户:",
			"userIdNotNull": "用户ID不能为空！",
			"userNameNotNull": "用户名不能为空！",
			"updateUserSuccess": "修改用户{0}成功!",
			"updateUserError": "修改用户{0}失败，原因:",
			"addUserSuccess": "添加用户{0}成功!",
			"addUserError": "添加用户{0}失败，原因:"
		},
		"group": {
			"title": "组列表",
			"groupId": "组ID",
			"groupName": "组名",
			"membersTitle": "已选人员",
			"notMembersTitle": "可选人员",
			"groupIdAlreadyCreated": "组ID已经存在",
			"confirmDeleteGroup": "请确认是否删除组：",
			"notselectedMembersForAdd": "未选中需新增成员！",
			"notselectedMembersForDelete": "未选中需删除成员！",
			"confirmDeleteGroupMember": "请确认是否将用户{0}，从组{1}中移除",
			"addGroupMemberSuccess": "添加组成员成功!",
			"addGroupMemberError": "添加组成员{0}失败,原因:",
			"deleteGroupMemberSuccess": "删除组成员成功!",
			"deleteGroupMemeberError": "删除组成员{0}失败，原因:",
			"groupIdNotNull": "组ID不能为空",
			"groupNameNotNull": "组名不能为空",
			"updateGroupSuccess": "修改组{0}成功!",
			"updateGroupError": "修改组{0}失败，原因:",
			"addGroupSuccess": "添加组{0}成功!",
			"addGroupError": "添加组{0}失败，原因:"
		},
		"faulthandling": {
			"faultHandling": "故障处理",
			"faultName": "故障名称",
			"faultDevice": "故障设备",
			"faultType": "故障类型",
			"faultLevel": "故障等级",
			"faultLocation": "故障位置",
			"faultContent": "故障内容",
			"startTime": "故障开始时间",
			"faultNameNotNull": "故障名称不允许为空",
			"faultTypeNotNull": "故障类型不允许为空",
			"faultLevelNotNull": "故障等级不允许为空",
			"faultLocationNotNull": "故障位置不允许为空",
			"faultContentNotNull": "故障内容不允许为空",
			"managementConfirm": "代维专业主管确认",
			"needToDealWith": "需处理",
			"notNeedToDealWith": "不需处理",
			"alarm": "告警",
			"other": "其他",
			"faultHandlingEndTime": "故障处理完成时间",
			"confirmApprover": "确认人",
			"confirmApproved": "已确认",
			"faultHandlingTicketReport": "告警工单报表"
		},
		"maintenanceJob": {
			"managementAudit": "代维专业主管审核",
			"requestDetail": "工单详情",
			"operation": "工单处理",
			"workTaskCard": "维护作业",
			"workTaskCardNotNull": "维护作业不允许为空",
			"auditApprover": "审核人",
			"auditApproved": "是否审核",
			"startTime": "作业开始时间",
			"endTime": "作业结束时间",
			"workTaskCardTitle": "作业任务标题",
			"workTaskCardContent": "作业任务内容",
			"maintenanceJobTicketReport": "维护作业工单报表",
			"maintenanceJobTicketReportDetail": "维护作业工单报表详情"
		}
	},
	"verification": {
		"taskSummation": {
			"completion": "完成情况",
			"completionMaintenance": "完成维护",
			"expireDate": "剩余天数",
			"totalDevice": "设备总数",
			"onlineCount": "在线数量",
			"equipmentClass": "设备类",
			"maintenancePersonal": "维护人员"
		},
		"verification": {
			"title": "设备维护管理",
			"it": "IT",
			"elec": "配电",
			"havc": "暖通",
			"roomNumber": "房间号",
			"assetNumber": "资产编号",
			"assetName": "资产名称",
			"deviceInspectDetail": "设备巡检详情",
			"jobCompletion": "岗位完成情况",
			"personalCompletion": "人员完成情况",
			"deviceCompletion": "设备完成情况",
			"monthlyMaintenanceReport": "月度维护报告",
			"unmaintainedReport": "未维护报告"
		}
	},
	"fault": {
		"faultBase": {
			"faultName": "工单名称",
			"faultNameRequired": "工单名称不能为空",
			"faultCategoryRequired": "故障分类不能为空",
			"faultObject": "故障对象",
			"faultObjectRequired": "故障对象不能为空",
			"faultLocation": "故障位置",
			"faultLocationRequired": "故障位置不能为空",
			"writeFaultCause": "填写故障原因",
			"faultCauseRequired": "故障原因不能为空",
			"initiator": "创建人",
			"principal": "负责人",
			"principalRequired": "负责人不能为空",
			"participants": "参与人",
			"deadline": "截止时间",
			"detectedTime": "检出时间",
			"attachment": "附件",
			"defect": "指定缺陷",
			"placeholder": "请输入检索关键字",
			"selectOption": "请选择",
			"test": "test",
			"maxLength": "长度限制为255个字符",
			"title": "故障列表",
			"faultId": "故障ID",
			"faultCategoryId": "故障分类ID",
			"faultCategory": "故障分类",
			"faultCause": "故障原因",
			"transactorId": "处理人ID",
			"transactor": "处理人",
			"startTime": "开始时间",
			"endTime": "结束时间",
			"comment": "备注"
		},
		"faultCategory": {
			"placeholder": "请输入检索关键字, 回车键查询",
			"title": "故障字典",
			"faultCategoryId": "故障字典ID",
			"faultType": "故障字典码",
			"faultTypeName": "故障类型名",
			"comment": "备注",
			"faultTypeIsNumber": "字段应为数字类型",
			"maxCommentLength": "长度限制为255个字符",
			"maxFaultTypeNameLength": "长度限制为64个字符"
		}
	},
	"defect": {
		"defectBase": {
			"title": "缺陷列表",
			"defectName": "缺陷名",
			"defectNameEmpty": "缺陷名不能为空",
			"defectCategory": "缺陷诱因",
			"defectCategoryEmpty": "缺陷诱因不能为空",
			"defectTags": "缺陷标签",
			"description": "描述"
		},
		"defectEvent": {
			"title": "缺陷事件",
			"defectEventName": "缺陷事件名",
			"eventSourceType": "缺陷事件源类型",
			"eventObject": "缺陷对象",
			"createTime": "发生时间",
			"content": "内容",
			"description": "描述"
		}
	},
	"minotor": {
		"historyTakeDevicePointConfig": {
			"title": "历史数据存储设置",
			"deviceName": "设备名称",
			"deviceCategory": "设备类型",
			"buildingName": "楼栋",
			"floorName": "层",
			"roomName": "房间",
			"takeRate": "采样间隔(小时)",
			"configActive": "是否启用",
			"notSettedDevice": "未配置设备",
			"setting": "设置",
			"pleaseSelectedRows": "请勾选需要存储数据的设备",
			"takeRateNotEmpty": "采样间隔不能为空!",
			"settingEnabled": "已启用",
			"settingDisabled": "已禁用",
			"settingSuccess": "数据存储设置成功,重启storm生效!",
			"settingError": "数据存储设置失败"
		}
	},
	"report": {
		"common": {
			"catagoryName": "报表类型名称",
			"catagoryDesc": "报表类型描述",
			"catagoryOrder": "显示顺序",
			"customReport": "自定义报表",
			"timerReport": "定时报表",
			"reportInformation": "报表信息",
			"reportName": "报表名称",
			"reportDescription": "报表描述",
			"isPublic": "是否公开",
			"queryParameters": "查询参数",
			"QueryParameterDisplay": "查询参数展示",
			"outputParameterDisplay": "输出参数展示",
			"ReportNameRequired": "报表名称不能为空",
			"grouptNameRequired": "分组名称不能为空",
			"reportEntryPrompt": "该参数为报表查询必须的参数不能同时为空和隐藏",
			"queryParamRequried": "请完善查询参数",
			"startTime": "开始时间",
			"endTime": "结束时间",
			"deviceType": "设备类型",
			"device": "设备",
			"alarmLevel": "告警等级",
			"signalName": "信号名称",
			"confirmor": "确认人",
			"location": "位置",
			"deviceSignal": "设备信号",
			"templateFile": "模板文件",
			"templateFileRequired": "模板文件不能为空",
			"configFile": "配置文件",
			"configFileRequired": "配置文件不能为空",
			"chart": "图表",
			"time": "时间",
			"value": "值",
			"download": "下载文件",
			"csv": "查看CSV文件",
			"display": "显示",
			"hide": "隐藏",
			"saveConfig": "保存参数配置",
			"addGroup": "新建分组",
			"editGroup": "修改分组",
			"groupName": "分组名称",
			"editHierarchy": "修改层级",
			"parentGroup": "上级分组",
			"addTemplate": "新建模板",
			"editTemplate": "修改模板",
			"reportCatagory": "报表类型",
			"reportCatagoryRequired": "报表类型不能为空"
		},
		"shiftChangeReport": {
			"pageIndexName": "第{0}页",
			"actionType": "行为",
			"print": "打印",
			"assignee": "执行人",
			"occurTime": "执行时间",
			"signIn": "签到",
			"handOver": "交班",
			"takeOver": "接班",
			"groupName": "值班组",
			"invalidStartTime": "请选择开始日期!",
			"invalidEndTime": "请选择结束日期!",
			"baseSituation": "基本情况",
			"shiftChangeReportTitle": "交接班报表",
			"shiftChangeReportDetail": "交接班记录详情",
			"to": "至",
			"selectResult": "查询结果",
			"select": "查询",
			"reset": "重置",
			"shifter": "交班人",
			"currentShift": "当前班次",
			"successor": "接班人",
			"successionShift": "接班班次",
			"handoverLocation": "交接地点",
			"handoverTime": "交接时间",
			"actualHandoverTime": "实际交接班时间",
			"description": "描述",
			"startTime": "开始时间",
			"endTime": "结束时间",
			"environmentAndGood": "环境及物品",
			"alarmInformation": "报警信息"
		},
		"alarmHistoryReport": {
			"resultTile": "查询结果",
			"conditionTitle": "查询条件",
			"alarmHistoryPeriod": "历史告警时间段"
		},
		"dailyInspectDayReport": {
			"resultTitle": "查询结果",
			"conditionTitle": "查询条件",
			"totalTime": "统计时间",
			"to": "至",
			"houseAndDeviceCategory": "房间-设备类型",
			"maintainItem": "工作内容",
			"maintainItemRequirement": "工作要求",
			"roomName": "房间名称",
			"deviceCategory": "设备类别",
			"reset": "重置",
			"select": "查询",
			"notSupportAcrossMonth": "不允许跨月查询",
			"timeSpanBeyound": "日期区间不允许大于31天",
			"print": "打印",
			"exportDataNullWarning": "该时间段查询数据为空，无法导出！",
			"excelFileName": "日常巡检日报表",
			"downloadExcelFileFail": "导出Excel发生内部错误，请联系管理员！",
			"excelTitle": "数据中心维护作业——日项目完成情况",
			"excelMonth": "月份：",
			"excelTip": "要求：每日完成巡检项目后在相应位置打“√”，周六、周日请空开。",
			"excelSign": "维护人员签名："
		},
		"historyDataReport": {
			"historyData": "历史数据时间段",
			"device": "设备",
			"corepoint": "设备测点",
			"corepointValue": "测点值",
			"acquisitionTime": "采集时间",
			"historyDataTrend": "历史数据趋势",
			"resultTitle": "查询结果列表",
			"historyDataTips": "最多只能选择10个设备测点",
			"submitStr": "确定",
			"close": "关闭",
			"selectDevice": "请选择设备",
			"selectCorepoint": "请选择设备测点",
			"backupInfo": "已选{{date}}项",
			"room": "房间",
			"basicType": "设备类型",
			"excelFileName": "历史数据曲线报表.xlsx",
			"selectPoint": "选择信号",
			"selectCorePoint": "已选测点",
			"deleteAll": "全部删除",
			"delectConfirm": "请确认是否全部删除"
		},
		"verification": {
			"taskName": "设备名称",
			"recordMonth": "月份",
			"formId": "表单类型",
			"state": "核查状态",
			"checkState": "维护结果",
			"confirmTime": "审核时间",
			"approvalComment": "审核意见",
			"approvalResult": "审核结果",
			"checkInterval": "状态",
			"actualExecutorId": "维护人",
			"actualExecutorName": "维护人",
			"confirmerId": "审核人",
			"confirmerName": "审核人",
			"queryCond": "查询条件",
			"endTime": "结束时间",
			"staTime": "开始时间",
			"vvd": "维护人",
			"deviceType": "设备类型",
			"allDevice": "所有人",
			"allPerson": "所有设备"
		},
		"dailyInspectAnalysis": {
			"reset": "重置",
			"select": "查询",
			"executor": "处理人",
			"startTime": "开始日期",
			"endTime": "结束日期",
			"selectTitle": "查询条件",
			"resultTitle": "结果列表",
			"dailyRoomInspectTaskId": "巡检任务ID",
			"personId": "人员编号",
			"personName": "责任人",
			"taskName": "任务名称",
			"parentTaskName": "上级任务名称",
			"completedTime": "任务完成时间",
			"analysisResult": "分析结果",
			"dataException": "疑似作弊",
			"GpsException": "定位异常",
			"handleFastException": "操作频率过快",
			"taskExceptionNo": "异常任务量",
			"dailyInspectTaskException": "日常巡检异常分析",
			"dailyInspectPersonalException": "日常巡检-个人异常总览"
		},
		"routineInspectAnalysis": {
			"reset": "重置",
			"select": "查询",
			"personName": "责任人",
			"recordMonth": "所属月份",
			"selectTitle": "查询条件",
			"resultTitle": "结果列表",
			"parentTaskId": "月度维护任务ID",
			"latitude": "经度",
			"longitude": "纬度",
			"taskName": "任务名称",
			"formName": "维护项",
			"completedTime": "任务完成时间",
			"analysisState": "状态",
			"analysisResult": "分析结果",
			"dataException": "疑似作弊",
			"GpsException": "定位异常",
			"handleFastException": "操作频率过快",
			"taskExceptionNo": "异常任务量",
			"routineInspectTaskException": "月度维护异常分析",
			"routineInspectPersonalException": "月度维护-个人异常总览"
		},
		"loginLogReport": {
			"loginLogDate": "登录时间",
			"resultTile": "查询结果",
			"conditionTitle": "查询条件",
			"alarmHistoryPeriod": "历史告警时间段",
			"client": "用户",
			"loginTime": "登录时间",
			"operation": "登录操作",
			"clientType": "类别",
			"clientIp": "Ip地址",
			"login": "登入",
			"logout": "登出",
			"pickUpTime": "请选择登录时间"
		},
		"batteryAlarmHistoryReport": {
			"conditionTitle": "查询条件",
			"batteryAlarmHistoryPeriod": "查询时间范围",
			"totalAlarms": "告警总数",
			"unfinishedAlarmCount": "未结束告警数",
			"finishedAlarmCount": "已结束告警数",
			"alarmLevelStatistics": "告警等级统计",
			"alarmCategoryStatistics": "告警类型统计",
			"roomAlarmStatistics": "房间告警统计",
			"deviceCategoryAlarmStatistics": "设备告警类型统计",
			"count": "数量",
			"excelFileName": "蓄电池告警数据报表.xlsx",
			"alarmStatistics": "告警统计",
			"alarmDetails": "告警详情",
			"exportDataNullWarning": "该告警时间段查询数据为空，无法导出！",
			"downloadExcelFileFail": "导出Excel发生内部错误，请联系管理员！"
		},
		"maintainPlanAnalysis": {
			"reset": "重置",
			"select": "查询",
			"operator": "维护人",
			"startTime": "开始日期",
			"endTime": "结束日期",
			"selectTitle": "查询条件",
			"resultTitle": "结果列表",
			"maintainPlanId": "维护计划ID",
			"personId": "人员编号",
			"personName": "维护人",
			"maintainPlanName": "维护计划名称",
			"planTime": "计划时间",
			"completeTime": "完成时间",
			"analysisResult": "分析结果",
			"dataException": "疑似作弊",
			"GpsException": "定位异常",
			"handleFastException": "操作频率过快",
			"taskExceptionNo": "异常任务量",
			"maintainPlanException": "维护计划异常分析",
			"maintainPlanPersonalException": "维护计划-个人异常总览"
		},
		"timingTaskReport": {
			"taskName": "任务名称",
			"reportTemplate": "报表模板",
			"storageCycle": "存储周期",
			"startTime": "开始时间",
			"endTime": "结束时间",
			"reportTaskName": "报表任务名称",
			"reportTemplateType": "报表模板分类",
			"report": "报表",
			"recipient": "收件人",
			"recipientTip": "多个收件人用分号隔开",
			"cc": "抄送",
			"ccTip": "多个抄送人用分号隔开"
		}
	},
	"daily-check": {
		"title": "日常维护"
	},
	"task-timing": {
		"title": "定时巡检"
	},
	"maintain": {
		"daily-inspect": {
			"title": "日常巡视",
			"inspTime": "巡视时间",
			"inspObj": "巡视对象",
			"timeSel": "选择打印时间段",
			"printButtonContent": "日常报告",
			"printButtonContentArr": "日常报告 ( 全天 )",
			"printButtonSummary": "日常报告 ( 汇总 )",
			"nothing": "没有相关数据",
			"printButtonDays": "日常报告 ( 时间段 )",
			"inspNo": "没有数据",
			"today": "今日",
			"all": "全部"
		},
		"customization": {
			"title": "日常巡视任务定制",
			"dailyInspectCustomizationId": "巡视任务ID",
			"taskName": "巡视任务名",
			"isEnabled": "启用状态",
			"enabled": "启用",
			"disabled": "禁用",
			"placeholder": "请输入检索关键字",
			"comment": "备注"
		},
		"dailyInspectObject": {
			"title": "日常巡视对象",
			"list": "日常巡视对象列表"
		},
		"routineInspectObject": {
			"title": "月度维护对象",
			"list": "月度维护对象列表"
		},
		"maintain-object": {
			"title": "维护对象",
			"assetId": "资产ID",
			"deviceId": "设备ID",
			"checkDevice": "选择设备",
			"deviceName": "名称",
			"devicePosition": "位置信息",
			"deviceModel": "型号",
			"itemCategory": "所属类型",
			"assetNo": "资产编码",
			"quantity": "数量",
			"formId": "FormID",
			"isEnable": "是否启用",
			"query": "查询",
			"enabled": "启用",
			"disabled": "禁用",
			"import": "批量导入",
			"templateDownload": "导入模板下载",
			"beginImport": "开始导入",
			"goBackPage": "返回",
			"baseInfo": "摘要信息：",
			"fileName": "文件名",
			"fileSize": "文件大小",
			"importResult": "导入结果",
			"recordSize": "总记录数",
			"uploadingCount": "总记录条数：",
			"successSize": "导入成功：",
			"failureSize": "失败：",
			"qrCodePrint": "打印二维码",
			"numberType": "仅允许输入数字",
			"require": "必填字段",
			"generatingQRCode": "正在生成二维码",
			"maintainObjectForm": "维护对象导入表",
			"deviceCategory": "设备类型"
		},
		"maintain-staff": {
			"title": "维岗调配",
			"placeholder": "请输入检索关键字",
			"staffName": "姓名",
			"gender": "性别",
			"phone": "手机号",
			"mail": "邮件",
			"duty": "维护岗位",
			"setting": "设置"
		},
		"faultHandlingTask": {
			"eventSeveritySetting": "告警等级及工单延时",
			"delayTime": "工单延时时间",
			"eventSeveritySaveHint": "系统将按照已选择的告警等级及工单延时时间派发告警工单",
			"responsiblitySetting": "值班组关联设备类型",
			"availableDeviceCategoriesNotSelected": "未选中可选设备类型",
			"unavailableDeviceCategoriesNotSelected": "未选中已选设备类型",
			"addAailableDeviceCategoriesSuccess": "添加可选设备类型成功",
			"addAailableDeviceCategoriesError": "添加可选设备类型失败",
			"removeUnavailableDeviceCategoriesSuccess": "删除已选设备类型成功",
			"removeUnavailableDeviceCategoriesError": "删除已选设备类型失败",
			"availableDeviceCategories": "可选设备类型",
			"unavailableDeviceCategories": "已选设备类型",
			"notifyRule": "工单通知规则",
			"timeoutRule": "工单超时规则",
			"ruleId": "规则编号",
			"deviceBaseTypeName": "设备类型",
			"alarmBaseTypeName": "告警基类",
			"groupLeaderTimeout": "通知组长超时时间（分钟）",
			"executiveTimeout": "通知主管超时时间（分钟）",
			"managerTimeout": "通知经理超时时间（分钟）"
		},
		"alarmFilter": {
			"alarmFilter": "告警过滤",
			"ruleId": "规则编号",
			"sourceDeivceName": "源设备",
			"sourceBaseTypeId": "源设备告警基类",
			"filterValidSeconds": "过滤有效时间（秒）",
			"destDeviceName": "被过滤设备",
			"destBaseTypeId": "被过滤设备告警基类"
		}
	},
	"deviceBook": {
		"common": {
			"deviceInstallLog": "安装记录",
			"deviceMaintenanceInfo": "设备信息",
			"deviceRepairRecord": "维修记录",
			"deviceMaintenanceLog": "维护记录",
			"deviceParameterChangeLog": "参数变更",
			"deviceTransferLog": "转移记录",
			"deviceId": "设备ID",
			"personId": "人员ID",
			"deviceName": "设备名称",
			"personName": "执行人",
			"createTime": "时间",
			"content": "内容",
			"type": "类型",
			"result": "结果",
			"grade": "健康等级",
			"remark": "备注",
			"description": "描述",
			"contact": "联系方式",
			"mobile": "手机",
			"phone": "电话",
			"email": "邮箱",
			"address": "地址",
			"tools": "工具",
			"supervisor": "负责人",
			"guidance": "说明书",
			"safetyTips": "安全手册",
			"ownerSupervisor": "业主负责人",
			"dimensionSupervisor": "代维负责人",
			"supplierSupervisor": "供应商负责人",
			"transferTime": "转移时间",
			"originLocation": "原始位置",
			"currentLocation": "当前位置",
			"getMaintenanceRecordIdError": "获取维护记录ID异常",
			"getRepairRecordIdError": "获取维修记录ID异常",
			"ok": "确定",
			"cancel": "取消",
			"create": "新增",
			"updateSuccess": "修改成功",
			"updateFailure": "修改失败",
			"insertSuccess": "新增成功",
			"insertFailure": "新增失败",
			"confirmDelete": "确认删除?",
			"deleteSuccess": "删除成功",
			"operation": "操作",
			"deviceConfiguration": "设备组态",
			"configuration": "设备组态",
			"indicator": "设备指标"
		},
		"maintenanceRecord": {
			"deviceId": "设备ID",
			"assetId": "资产ID",
			"assetName": "资产名称",
			"sopInstanceId": "SOP-ID",
			"deviceName": "设备名称",
			"personId": "人员ID",
			"personName": "处理人",
			"createMode": "创建方式",
			"executeTime": "处理时间",
			"attachmentFileName": "附件",
			"healthLevel": "健康等级",
			"description": "描述",
			"deviceIdRequired": "设备ID为必填字段",
			"personIdRequired": "处理人为必填字段",
			"executeTimeRequired": "处理时间为必填字段",
			"autoCreate": "自动创建",
			"manualCreate": "手动创建",
			"maintenanceRecordEdit": "维护记录[编辑]",
			"maintenanceRecordAdd": "维护记录[新增]"
		},
		"deviceRepairRecord": {
			"repairRecordEdit": "维修记录[编辑]",
			"repairRecordAdd": "维修记录[新增]",
			"deviceId": "设备ID",
			"assetId": "资产ID",
			"deviceName": "设备名称",
			"malfunctionalSource": "故障来源",
			"malfunctionalContent": "故障内容",
			"malfunctionalOccurredTime": "故障时间",
			"processContent": "处理过程",
			"personId": "人员ID",
			"personName": "处理人",
			"executeTime": "处理时间",
			"attachmentFileName": "附件",
			"healthLevel": "健康等级",
			"fieldRequired": "必填"
		},
		"deviceInstallLog": {},
		"deviceparameterchangelog": {
			"id": "序号",
			"deviceId": "设备ID",
			"deviceName": "设备名称",
			"parameterId": "参数ID",
			"parameterName": "参数名称",
			"changeTime": "变更时间",
			"originalValue": "原始值",
			"currentValue": "当前值",
			"personName": "变更人",
			"changeReason": "变更原因"
		}
	},
	"crewscheduling": {
		"common": {
			"istrue": "是",
			"isfalse": "否",
			"deleteSuccess": "删除成功!",
			"timeRange": "时间段",
			"action": "操作",
			"confirmDelete": "请确认是否删除:",
			"idNotNull": "ID不能为空！",
			"personNotEmpty": "人员不能为空",
			"name": "名称",
			"nameNotNull": "名称不能为空！",
			"updateSuccess": "修改{0}成功!",
			"updateError": "修改{0}失败，原因:",
			"addSuccess": "添加{0}成功!",
			"addError": "添加{0}失败，原因:",
			"monday": "星期一",
			"tuesday": "星期二",
			"wednesday": "星期三",
			"thursday": "星期四",
			"friday": "星期五",
			"saturday": "星期六",
			"sunday": "星期日"
		},
		"flight": {
			"cardtitle": "班次信息",
			"placeholder": "请输入检索关键字",
			"flightId": "ID",
			"flightColor": "颜色",
			"flightColorNull": "显示颜色不能为空！",
			"workingDay": "工作时间",
			"sumTime": "时长（小时）",
			"systemType": "是否系统班次",
			"crossDay": "是否跨天",
			"flightShortName": "短名称",
			"flightShortNameNotNull": "短名称不能为空",
			"autoScheduling": "自动排班",
			"nextFlightNotNull": "请选择交接班",
			"autoSchedulingInvalid": "该班次有交接班，不能修改为自动排班"
		},
		"scheduling": {
			"cardtitle": "排班信息",
			"placeholder": "请输入检索关键字",
			"schedulingId": "排班ID",
			"schedulingName": "排班名称",
			"duty": "职责",
			"name": "姓名",
			"currentWeek": "本周",
			"Validate": "验证",
			"ValidateTime": "24小时有未排班的时间段"
		},
		"scheduleperson": {
			"noSelectPerson": "未选人员",
			"selectedPerson": "已选人员",
			"selectedGroup": "选择分组",
			"groupName": "分组名",
			"groupNamelNotNull": "分组名不能为空",
			"teamLeader": "组长",
			"teamLeaderNotNull": "请选择组长",
			"settingCaptain": "设置班长",
			"deleteGroupError": "所选组下有人员信息，请先删除人员信息"
		},
		"calendar": {
			"label": "标签",
			"labelNotNull": "标签不能为空",
			"calendarTitleHoliday": "节日",
			"calendarTitleWeekend": "周末",
			"calendarTitleWorkDay": "工作日",
			"addCalendar": "添加日历项",
			"description": "描述",
			"today": "今日",
			"month": "月",
			"week": "周",
			"day": "天"
		}
	},
	"admin": {
		"common": {
			"ssoAccount": "人主账号",
			"ssoAccountS6Login": "人主账号允许直接在本系统登录",
			"account": "帐号",
			"organization": "组织",
			"role": "角色",
			"permission": "权限",
			"permissionCategory": "权限分类",
			"person": "人员",
			"action": "操作",
			"confirmDelete": "请确认是否删除:",
			"idNotNull": "ID不能为空！",
			"nameNotNull": "名称不能为空！",
			"updateSuccess": "修改{0}成功!",
			"updateError": "修改{0}失败，原因:",
			"addSuccess": "添加{0}成功!",
			"addError": "添加{0}失败，原因:",
			"deleteSuccess": "删除成功!",
			"responsibility": "职责",
			"duty": "岗位",
			"onDutyResponsibility": "值班组",
			"add": "新增",
			"edit": "编辑",
			"name": "名称",
			"description": "描述",
			"namePlaceholder": "请输入名称",
			"selectAll": "全选",
			"deleteAll": "全部删除"
		},
		"deleteSuccess": "删除成功!",
		"account": {
			"remote": "是否远程",
			"lock": "是否锁定",
			"enable": "是否启用",
			"title": "帐号管理",
			"addAccount": "新增帐号",
			"accountId": "帐号ID",
			"personId": "人员",
			"personName": "名称",
			"UUID": "",
			"name": "帐号名",
			"password": "密码",
			"confirmPassword": "确认密码",
			"newpassword": "新密码",
			"maxError": "最大出错次数",
			"validTime": "有效时间",
			"description": "描述",
			"roleId": "所属角色",
			"roles": "所属角色",
			"action": "操作",
			"placeholder": "请输入检索关键字",
			"confirmDeleteUser": "请确认是否删除用户：",
			"updateAccountSuccess": "修改用户{0}成功!",
			"updateAccountError": "修改用户{0}失败，原因：",
			"addAccountSuccess": "添加用户{0}成功!",
			"telephoneNotEmpty": "手机号不能为空",
			"phoneFormatError": "手机号格式不正确，请输入11位有效手机号",
			"describeNotEmpty": "注册说明不能为空",
			"addAccountError": "添加用户{0}失败，原因：",
			"nameNotEmpty": "用户名不能为空",
			"imgCodeNotEmpty": "图形码不能为空",
			"messageCodeNotEmpty": "短信验证码不能为空",
			"getImgCode": "获取验证码",
			"personNotEmpty": "人员不能为空",
			"maxErrorNotEmpty": "最大出错次数不能为空",
			"validTimeNotEmpty": "有效时间不能为空",
			"roleNotEmpty": "必须选择所属角色",
			"nameNotLessThan": "用户名不能少于4个字母",
			"passwordNotEmpty": "密码不能为空",
			"passwordNotLessThan": "密码不能少于8位数",
			"imgCodeLessThan": "图形码不能少于4位数",
			"passwordIsInconsistent": "两次输入的密码不一致",
			"passwordIsConsistent": "新密码与原密码一致",
			"alreadyCreated": "帐号已经被注册",
			"passwordIsError": "密码输入错误",
			"passwordIsCurrent": "当前密码",
			"accountMaxErrorSection": "请填写4位内的整数",
			"low": "弱",
			"middle": "一般",
			"high": "强",
			"meetRequirements": "符合要求",
			"passwordcheck": "密码不符合复杂度规范",
			"pwdrequire": "长度至少8位，大小写字母、数字、特殊字符每类至少1位",
			"error1": "参数校验失败!",
			"error2": "帐号ID不存在!",
			"error3": "旧密码不匹配!",
			"error4": "新密码与历史密码重复!",
			"success": "修改成功!"
		},
		"organization": {
			"departmentList": "部门列表",
			"id": "部门ID",
			"name": "部门名称",
			"caption": "标题",
			"description": "描述",
			"role": "角色管理",
			"parentName": "上级部门",
			"addOrganization": "新增部门",
			"editOrganization": "编辑部门",
			"notEmpty": "必填",
			"action": "操作",
			"sort": "人员排序",
			"organizationNameNotEmpty": "请先选择一个组织再进行操作!",
			"updateOrganizationSuccess": "更新组织成功！",
			"updateOrganizationError": "更新组织失败",
			"updateOrganizationRequired": "组织名字不能为空",
			"addOrganizationSuccess": "添加成功！",
			"addOrganizationError": "添加失败",
			"editOrganizationSuccess": "修改成功！",
			"deleteOrganizationSuccess": "删除成功！",
			"placeholder": "请输入检索关键字",
			"personName": "姓名",
			"organizationName": "组织名称",
			"currentOrganization": "当前组织",
			"personNameTitle": "人员名称",
			"title": "新增组织成员关系",
			"addSuccess": "添加成功!",
			"addError": "添加失败",
			"confirmDelete": "请确认是否删除: ",
			"organization": "组织架构",
			"change": "修改名称",
			"del": "删除",
			"print": "打印",
			"printTitle": "人员信息表",
			"firstDepart": "顶层组织",
			"confirmDeleteAppend": " 部门及下面的相关人员",
			"confirmDeleteCanNot": "该组织下面存在子组织，请首先删除对应的子组织"
		},
		"role": {
			"title": "角色管理",
			"placeholder": "请输入检索关键字",
			"roleId": "角色ID",
			"name": "角色",
			"description": "简介",
			"action": "操作",
			"confirmDelete": "请确认是否删除角色：",
			"confirmDeleteBatch": "确认要删除选中记录？",
			"addRole": "新增角色",
			"updateRole": "修改角色",
			"nameNotNull": "角色名称不能为空",
			"updateRoleSuccess": "修改角色{0}成功!",
			"updateRoleFailed": "修改角色{0}失败，原因：",
			"addRoleSuccess": "新增角色{0}成功!",
			"addRoleFailed": "新增角色{0}失败，原因：",
			"permissionCategory": "分类标签",
			"permission": "权限",
			"permissionNotEmpty": "必须选择权限",
			"allSelect": "全选",
			"cancelAllSelect": "取消"
		},
		"person": {
			"aliasCheck": "启用状态",
			"mobileTooltip": "联系方式二（手机号）不是11位数字，请确认是否通过?",
			"alias": "别名",
			"postAddress": "邮寄地址",
			"mobile": "联系方式二",
			"newAccount": "创建帐号",
			"editAccount": "编辑帐号",
			"account": "用户帐号",
			"department": "部门",
			"male": "男",
			"female": "女",
			"leader": "领导",
			"staff": "普通员工",
			"employeeTitle": "职级",
			"list": "人员列表",
			"encryption": "信息加密",
			"closeEncryption": "取消加密",
			"personId": "人员ID",
			"patternStrOrNum": "请填写数值,字母或者_",
			"organizationId": "组织ID",
			"organizationName": "组织",
			"name": "姓名",
			"genderName": "性别",
			"sortIndex": "序号",
			"title": "头衔",
			"employeeId": "工号",
			"avatar": "照片",
			"phone": "联系方式一",
			"phoneNumber": "电话",
			"routerConfig": "人员创建成功，是否创建相关帐号",
			"mail": "邮箱",
			"dutyId": "岗位ID",
			"dutyName": "岗位",
			"placeholder": "请输入检索关键字",
			"action": "操作",
			"confirmDelete": "请确认是否删除：",
			"confirmDeleteBatch": "确认要删除选中记录？",
			"confirmDeleteAccount": " ,并删除相应的帐号：",
			"addPerson": "人员新增",
			"editPerson": "人员编辑",
			"nameNotNull": "必填",
			"genderNotNull": "必填",
			"organizationNotNull": "必填",
			"positionNotNull": "必填",
			"phoneNotNull": "必填",
			"phoneInvalid": "手机号码格式错误",
			"emailInvalid": "邮箱格式错误",
			"updatePersonSuccess": "修改人员{0}成功!",
			"updatePersonFailed": "修改人员{0}失败，原因：",
			"addPersonSuccess": "新增人员{0}成功!",
			"addPersonFailed": "新增人员{0}失败，原因：",
			"exists": "该人员已经存在",
			"selectImage": "选择照片",
			"uploadImage": "上传照片",
			"invalidFormat": "无效图片格式",
			"uploadSuccess": "照片上传成功",
			"uploadFail": "照片上传失败",
			"minLength": "不能小于2位数",
			"acountTitle": "帐号管理",
			"addAccount": "新增帐号",
			"accountId": "帐号ID",
			"personName": "名称",
			"UUID": "",
			"acountName": "帐号名",
			"password": "密码",
			"confirmPassword": "确认密码",
			"passwordTip": "大小写字母，数字，特殊字符各至少一个",
			"accountFilter": "输入角色名称以过滤...",
			"maxError": "最大出错",
			"count": "次数",
			"validTime": "有效时间",
			"description": "描述",
			"roleId": "所属角色",
			"roles": "所属角色",
			"confirmDeleteUser": "请确认是否删除用户：",
			"updateAccountSuccess": "修改用户{0}成功!",
			"updateAccountError": "修改用户{0}失败，原因：",
			"addAccountSuccess": "添加用户{0}成功!",
			"addAccountError": "添加用户{0}失败，原因：",
			"nameNotEmpty": "用户名不能为空",
			"personNotEmpty": "人员不能为空",
			"maxErrorNotEmpty": "最大出错次数不能为空",
			"validTimeNotEmpty": "必填",
			"roleNotEmpty": "必填",
			"notEmpty": "必填",
			"nameNotLessThan": "用户名不能少于2个字母",
			"passwordNotEmpty": "必填",
			"passwordNotLessThan": "密码不能少于8位数",
			"passwordIsInconsistent": "两次输入的密码不一致",
			"alreadyCreated": "帐号已经被注册",
			"accountMaxErrorSection": "填写4位内整数",
			"show": "查看帐号",
			"hidden": "隐藏帐号",
			"createAccount": "创建帐号",
			"updateAccount": "更新帐号",
			"departmentPerson": "部门人员",
			"defaultMenu": "默认菜单",
			"passwordcheck": "密码不符合复杂度规范",
			"code": "验证码",
			"getMessageCode": "获取验证码",
			"resend": "重新发送",
			"messageCodePrompt": "短信验证码",
			"sendError": "验证码发送失败",
			"codeError": "验证码错误",
			"passwordUpdateTime": "密码更新",
			"low": "弱",
			"middle": "一般",
			"high": "强",
			"passwordRules": "密码验证规则为：至少包含一个大写字母；至少包含一个小写字母；至少包含一个数字；至少包含一个特殊字符；密码至少8位长度。",
			"showPassword": "修改密码",
			"selectDepartment": "选择部门",
			"selectedDepartment": "已选部门",
			"validTimeLimited": "有效期限",
			"validTimeUnlimited": "永久有效",
			"export": "是脱敏导出吗？",
			"exportType": "导出方式"
		},
		"permission": {
			"permissionId": "权限ID",
			"permissionName": "权限名称",
			"permissionNameNotNull": "权限名称不能为空",
			"categoryNameNotNull": "权限类型不能为空",
			"permissionCategory": "分类标签",
			"permissionCategoryName": "分类标签名称",
			"returnToPermission": "返回权限",
			"description": "描述",
			"placeholder": "请输入检索关键字",
			"caption": "标签",
			"categoryId": "权限类型ID",
			"categoryName": "类型名",
			"confirmDelete": "请确认是否删除权限：",
			"action": "操作",
			"noDataMessage": "无数据结果",
			"deleteSuccess": "删除成功",
			"updatePermissionSuccess": "修改权限{item}完成",
			"updatePermissionFailure": "修改权限{item}失败",
			"insertPermissionSuccess": "新增权限{item}完成",
			"insertPermissionFailure": "新增权限{item}失败",
			"permissionNameValid": "仅允许汉字、英文字母及下划线;",
			"permissionCategoryRequired": "分类标签为必填项;"
		},
		"duty": {
			"cardtitle": "岗位信息",
			"placeholder": "请输入检索关键字",
			"dutyId": "岗位ID",
			"dutyName": "岗位",
			"duty": "职责",
			"dutyNameNotEmpty": "岗位不能为空",
			"dutyNotEmpty": "职责描述不能为空",
			"dutyIsUsed": "该岗位正在被人员：{0}使用，不能删除！"
		},
		"responsibility": {
			"placeholder": "请输入检索关键字",
			"responsibilityId": "职责ID",
			"responsibilityName": "职责名称",
			"description": "描述",
			"responsibilityNameNotEmpty": "职责不能为空",
			"descriptionNotEmpty": "职责描述不能为空",
			"minLength": "不能小于2位"
		},
		"navigationTarget": {
			"placeholder": "请输入检索关键字",
			"confirmDelete": "请确认是否删除导航：",
			"nameNotNull": "导航名称不能为空",
			"linkNotNull": "链接地址不能为空",
			"name": "导航名称",
			"link": "链接地址",
			"iconPath": "图标地址",
			"visible": "是否可见",
			"params": "参数说明",
			"description": "描述"
		},
		"navigationDock": {
			"name": "导航坞名称",
			"type": "类型",
			"visible": "是否可见",
			"confirmDelete": "请确认是否删除导航坞：",
			"nameNotNull": "导航坞名称不能为空",
			"description": "描述"
		},
		"areaPermissions": {
			"operation": "操作",
			"add": "新增",
			"edit": "编辑",
			"name": "名称",
			"description": "描述",
			"areaGroup": "区域权限分组",
			"area": "区域",
			"selectGroupFirst": "请先在左侧栏选择区域权限分组",
			"areaGroupAdd": "新增区域权限组",
			"areaGroupEdit": "修改区域权限组",
			"getAreaDataFail": "获取区域信息失败",
			"namePlaceholder": "请输入名称",
			"canNotEditDefault": "不能修改默认组",
			"canNotDeleteDefault": "不能删除默认组",
			"onlyShowSelected": "只显示选中",
			"nodeParent": "位置",
			"nodeLeaf": "设备"
		},
		"operationPermissions": {
			"operation": "操作",
			"selectGroupFirst": "请先在左侧栏选择操作权限分组",
			"operationGroup": "操作权限分组",
			"operationGroupAdd": "新增操作权限组",
			"operationGroupEdit": "修改操作权限组",
			"getOperationDataFail": "获取操作信息失败"
		},
		"menuPermissions": {
			"menu": "菜单",
			"selectGroupFirst": "请先在左侧栏选择菜单权限分组",
			"menuGroup": "菜单权限分组",
			"menuGroupAdd": "新增菜单权限组",
			"menuGroupEdit": "修改菜单权限组",
			"getMenuDataFail": "获取菜单信息失败"
		},
		"roleAuthorization": {
			"role": "角色",
			"description": "描述",
			"authorization": "权限",
			"canNotEditDefault": "不能修改默认角色",
			"canNotDeleteDefault": "不能删除默认角色",
			"selectRoleFirst": "请先在左侧栏选择角色",
			"menu": "菜单",
			"canNotEditSysRole": "不能编辑系统管理员",
			"canNotDeleteSysRole": "不能删除系统管理员",
			"operate": "操作"
		},
		"accountManagement": {
			"account": "帐号",
			"logonId": "帐号名",
			"userName": "用户名",
			"enableStatus": "启用状态",
			"maxError": "最大出错",
			"locked": "是否锁定",
			"validTime": "帐号有效时间",
			"passwordValidTime": "密码有效时间",
			"resetPassword": "重置密码",
			"unbindPhone": "解绑手机",
			"unbindPhoneConfirm": "请确认是否解绑该手机,绑定时间【{{1}}】绑定ID为【{{0}}】?",
			"unbindPhoneNone": "用户【{{0}}】还未绑定手机",
			"unbindPhoneSuccess": "解绑手机成功",
			"confirmResetPassword": "请确认是否重置【{{0}}】的帐号密码",
			"newPassword": "重置后密码为：{{0}}",
			"success": "操作成功！",
			"canNotDeleteCurrentAccount": "不能删除当前帐号！",
			"canNotDeleteAdminAccount": "不能删除管理员账号！",
			"canNotDeleteAdmin": "不能删除管理员！",
			"canNotDeleteCurrentAccountPerson": "不能删除当前帐号人员！",
			"employeeHasCardDeleteConfirm": "该人员有关联门禁卡，删除人员将删除关联门禁卡！",
			"deleteEmployeeCardSucceed": "删除人员关联门禁卡成功！",
			"deleteEmployeeCardFail": "删除人员关联门禁卡失败！"
		}
	},
	"note": {
		"common": {
			"placeholder": "输入日志...",
			"addSuccess": "添加成功!",
			"editSuccess": "修改成功!",
			"noteCompelted": "日志已完成!",
			"noteReOpened": "日志被重新打开!",
			"deleteSuccess": "删除成功!",
			"del": "删除",
			"edit": "编辑",
			"createTime": "创建",
			"updateTime": "更新"
		},
		"person": {
			"title": "Note日志",
			"content": "内容",
			"createTime": "创建时间",
			"description": "描述",
			"contentSearch": "请输入内容查询",
			"minlength": "最小长度不能小于2",
			"NotNull": "不能为空"
		}
	},
	"settings": {
		"name": "名称",
		"value": "值",
		"version": "版本",
		"description": "描述",
		"placeholder": "请输入检索关键字",
		"action": "操作",
		"createSetting": "新增",
		"editSetting": "修改设置",
		"confirmDeleteSetting": "请确认是否删除",
		"editSettingSuccess": "修改设置{0}成功",
		"editSettingFailure": "修改设置{0}失败",
		"insertSettingSuccess": "新增设置{0}成功",
		"insertSettingFailure": "新增设置{0}失败",
		"deleteSettingSuccess": "删除成功",
		"deleteSettingFailure": "删除失败",
		"alreadyCreated": "名称已存在",
		"nameNotEmpty": "名称不能为空",
		"valueNotEmpty": "属性值不能为空"
	},
	"shiftChange": {
		"handoverItems": "交接项",
		"environmentAndGood": "环境及物品",
		"alarmInformation": "告警信息",
		"remark": "重要提示",
		"signOut": "签退",
		"signIn": "签到",
		"notCurrentShiftMonitor": "请登陆当前班次的帐号",
		"dutyPerson": {
			"dayShift": "白班",
			"nightShift": "夜班",
			"oftenShift": "常班",
			"noMessage": "未查询到相关人员信息"
		},
		"verifyLogin": {
			"verifyLoginSuccess": "帐号验证成功",
			"verifyLoginError": "帐号验证失败"
		},
		"workLogs": {
			"workLog": "工作日志"
		},
		"reguliationsPanel": {
			"reguliationPanelTitle": "岗位交接班管理制度",
			"handover": "交接者",
			"theFirstOne": "1、努力做好本职工作，圆满完成本班任务，为下班生产打好基础。",
			"articleTwo": "2、下班前进行全面检查，稳定生产，使各项指标符合技术规定。",
			"articleThree": "3、向接班者详细介绍本班生产情况，发生问题，处理经过和结果及交班时依存度问题。",
			"articleIV": "4、虚心听取接班者意见，发现问题由交班者负责，接班者协助处理。",
			"theFifth": "5、接班者过时未到，当班人员必须坚守岗位保证生产正常，并向报告班组长予以解决",
			"articleVI": "6、经交接班双方签字后方可下班",
			"successor": "接班者",
			"one": "1、提前10分钟达到本岗位，开交接班会",
			"two": "2、对上班的生产、设备情况进行全面检查，发现问题及时",
			"three": "3、耐心听取交班者介绍情况，详细检查和阅读交接班记录，诚恳征求交班者意见。",
			"four": "4、交接班时发现问题，两者共同处理。",
			"five": "5、接班后发现、发生的问题由接班者负责。",
			"six": "6、与交接者发生争执向有关班长报告予以解决。"
		}
	},
	"ubitManage": {
		"common": {
			"curve": "总U位变化曲线",
			"devicetype": "IT设备类型分类统计",
			"engineRoomUsage": "机房U位使用率",
			"frame": "机架U位使用率",
			"businessAttributes": "IT设备业务属性分类统计",
			"rackChangeLog": "近期机架变更记录"
		}
	},
	"tagManage": {
		"common": {
			"tagValue": "标签码",
			"itdeviceName": "绑定IT设备",
			"computerRackName": "机架名称",
			"uBit": "U位",
			"position": "资源位置",
			"itdeviceModelName": "设备模型名称",
			"bindTime": "更新日期",
			"configTag": "批量配置标签码",
			"downTemplate": "下载模板",
			"bindItDevice": "绑定IT设备",
			"placeholder": "请输入检索关键字",
			"unbind": "解除绑定",
			"selectDevice": "选择设备",
			"tagList": "标签列表",
			"unBindDevice": "该标签未绑定设备,请检查配置",
			"toUnbind": "请确认是否解除:标签{{0}}和设备{{1}}的绑定",
			"unbindSuccess": "解除绑定成功",
			"confirmDelete": "请确认是否删除标签:",
			"tagTemplate": "标签模板"
		}
	},
	"shiftResponsibility": {
		"certification": "交接认证",
		"transferItem": "交接项",
		"environmentAndGood": "环境及物品",
		"alarmInformation": "报警信息",
		"remarks": "重要提示",
		"handover": "交接",
		"handoverCorrect": "交接确认",
		"handoverDuty": "交接职责",
		"handoverDutyEmpty": "无职责可交接",
		"handoverDutyNotEmpty": "请选择要交接的职责",
		"handoverPerson": "交班人",
		"successor": "接班人",
		"handoverTips": "选择交接职责；交班人、接班人输入帐号密码进行确认交接",
		"handoverSuccess": "交接成功",
		"handoverFail": "交接失败",
		"handoverAlertContent": "职责交接成功！",
		"accountInputTips": "请输入您的帐号和密码进行认证",
		"tips": {
			"commonTip1": "请确认交接双方需要进行交接的职责；",
			"accountTip1": "请填写交接双方的帐号名、密码进行验证。",
			"fingerprintTip1": "请交接双方使用指纹仪进行指纹识别；",
			"fingerprintTip2": "按提示点击〖开始识别〗后，把手指放在指纹仪上即可进行指纹识别。",
			"faceTip1": "请交接双方正视摄像头，进行人脸识别验证；",
			"faceTip2": "按提示点击〖开始识别〗后，正视摄像头，按圆圈内所示调整位置，即可进行人脸识别认证。",
			"accountCertificationSuccessTip": "帐号密码认证成功！",
			"accountCertificationFailTip": "帐号密码认证失败！",
			"fingerprintCertificationSuccessTip": "指纹认证成功！",
			"fingerprintCertificationFailTip": "指纹认证失败！",
			"faceCertificationSuccessTip": "人脸认证成功！",
			"faceCertificationFailTip": "人脸认证失败！",
			"importanceTip": "双方已阅读交接条件，未处理告警已经由双方确认，确认交接。",
			"handoverInconsistent": "接班人与上一重认证的接班人不一致，请冷静下来，思考该交接给哪位接班人，然后重新交接！"
		},
		"currentDuty": {
			"title": "当前当班人",
			"noDuty": "无人当班"
		},
		"workLog": {
			"title": "维护日志"
		},
		"faceCamera": {
			"identify": "开始识别",
			"resIdentify": "重新识别",
			"identifySuccess": "认证通过",
			"noSupport": "您的浏览器不支持打开摄像头！请选用现代浏览器！",
			"noFindDevice": "没有找到摄像头设备！"
		},
		"fingerprint": {
			"successTips": "指纹验证通过！",
			"failTips": "指纹识别失败或未检测到指纹，请把手指放在指纹仪上，重新识别！"
		},
		"registration": {
			"updateTime": "上次签到时间",
			"registrationSuccess": "签到成功",
			"registrationSuccessContent": "职责签到成功",
			"registrationFail": "签到失败"
		}
	},
	"ngForm": {
		"create": {
			"success": "创建成功",
			"error": "创建失败",
			"timeIntervalError": "报表查询时间间隔大于允许值！",
			"illegalName": "非法的报表名称",
			"hour": "小时",
			"noParamTips": "注意：没有选择输出参数"
		},
		"update": {
			"success": "更新成功",
			"error": "更新失败",
			"repeat": "密码与之前设置的密码重复！"
		},
		"copy": {
			"success": "复制成功",
			"error": "复制失败"
		},
		"delete": {
			"timingReportError": "请先停用该定时报表！"
		}
	},
	"sop": {
		"common": {
			"title": "sop",
			"sopName": "名称",
			"sopVersion": "版本",
			"author": "作者",
			"prerequisties": "资源",
			"risk": "风险",
			"purpose": "目标",
			"description": "描述",
			"sopNameNotNull": "名称不能为空",
			"noSopItem": "请按SOP列表填写",
			"sopIdNotNull": "请先在右边添加sop数据",
			"stringLengthLimit64": "长度不能超过64",
			"stringLengthLimit4000": "长度不能超过4000",
			"stringLengthLimit255": "长度不能超过255",
			"stringLengthLimit128": "长度不能超过128",
			"stringLengthLimit30": "长度不能超过30",
			"healthDegreeInputTip": "请输入设备健康度"
		},
		"list": {
			"placeholder": "请输入检索关键字",
			"confirmDelete": "请确认是否删除sop:",
			"deleteSuccess": "删除成功!"
		}
	},
	"taskDevice": {
		"common": {
			"title": "月度维护"
		}
	},
	"sopVariable": {
		"common": {
			"title": "sop参数",
			"variableName": "参数名",
			"dataType": "类型",
			"defaultValue": "默认值",
			"description": "描述",
			"variableNameNotNull": "参数不能为空"
		},
		"list": {
			"placeholder": "请输入检索关键字",
			"confirmDelete": "请确认是否删除:",
			"deleteSuccess": "删除成功!"
		},
		"validate": {
			"sopVariableNameNotEmpty": "名称不能为空"
		}
	},
	"sopItem": {
		"common": {
			"title": "sop节点",
			"sopItemName": "名称",
			"displayCategory": "显示分类",
			"isRootPoint": "是否为根节点",
			"resultExpression": "计算结果",
			"transitExpression": "计算表达式",
			"timeOut": "超时期限",
			"passContent": "执行成功",
			"failContent": "执行失败",
			"description": "描述"
		},
		"list": {
			"placeholder": "请输入检索关键字",
			"confirmDelete": "请确认是否删除名称为:{0}的节点,\n 删除同时将删除该节点下的节点参数和节点流转标记?",
			"deleteSuccess": "删除成功!",
			"deleteError": "删除失败!",
			"saveSuccess": "sop节点保存成功!",
			"saveError": "sop节点保存失败!"
		},
		"validate": {
			"isRootPointNotEmpty": "是否为根节点不能为空",
			"timeOutNotEmpty": "超时期限不能为空",
			"sopItemNameNotEmpty": "节点名称不能为空"
		}
	},
	"sopItemVariable": {
		"common": {
			"title": "sop节点参数",
			"variableName": "参数名称",
			"dataType": "参数类型",
			"poolingInterval": "轮询时间(秒)",
			"Parameters": "接口参数",
			"isRequired": "是否轮询",
			"synType": "获取方式",
			"actionName": "获取接口",
			"defaultValue": "默认值",
			"description": "描述"
		},
		"list": {
			"placeholder": "请输入检索关键字",
			"confirmDelete": "请确认是否删除:",
			"deleteSuccess": "删除成功!",
			"saveSuccess": "sop节点参数保存成功!",
			"saveError": "sop节点参数保存失败!"
		},
		"validate": {
			"actionNameNotEmpty": "接口名称不能为空",
			"sopItemVariableNameNotEmpty": "sop节点参数名称不能为空",
			"sopItemVariableSynTypeNotEmpty": "获取方式不能为空"
		}
	},
	"sopItemVariableMeanings": {
		"common": {
			"title": "sop节点参数定义",
			"displayName": "展示名称",
			"variableValue": "参数值",
			"description": "描述"
		},
		"list": {
			"placeholder": "请输入检索关键字",
			"confirmDelete": "请确认是否删除:",
			"deleteSuccess": "删除成功!",
			"saveSuccess": "sop节点参数定义保存成功!",
			"saveError": "sop节点参数定义保存失败!"
		},
		"validate": {
			"displayNameNotEmpty": "展示名称不能为空",
			"variableValueNotEmpty": "参数值不能为空"
		}
	},
	"transitionFlag": {
		"common": {
			"title": "SOP节点流转标记",
			"sopItemId": "SOP节点ID",
			"transitionFlag": "名称",
			"faultReason": "故障原因",
			"delete": "删除"
		},
		"list": {
			"placeholder": "请输入检索关键字",
			"confirmDelete": "请确认是否删除:",
			"deleteSuccess": "删除成功!"
		},
		"validate": {
			"transitionFlagNotEmpty": "名称不能为空",
			"faultReasonNotEmpty": "故障原因不能为空"
		}
	},
	"sopItemTransition": {
		"common": {
			"title": "sop节点流转",
			"sopItemTransitionId": "sop项目转换ID",
			"sopId": "sopID",
			"fromSopItemName": "sop节点从",
			"toSopItemName": "sop节点到",
			"transitionFlagName": "跳转条件",
			"description": "描述"
		},
		"validate": {
			"fromSopItemNameNotEmpty": "sop节点从不能为空",
			"toSopItemNameNotEmpty": "sop节点到不能为空",
			"transitionFlagNameNotEmpty": "跳转条件不能为空"
		}
	},
	"deviceProbe": {
		"common": {
			"diseaseRule": "疾病规则",
			"subHealth": "亚健康规则",
			"subHealthNormal": "该指数正常！",
			"normal": "设备运行正常！"
		},
		"diseaseRule": {
			"diseaseRuleId": "疾病规则ID",
			"enable": "是否启用",
			"diseaseRuleName": "疾病指标名称",
			"notEmptyDiseaseRuleName": "名称不能为空",
			"maxLengthDiseaseRuleName": "名称不能超过64个字符",
			"deviceCategory": "类别",
			"diseaseLevel": "等级",
			"diseaseIndex": "疾病指数(%)",
			"minErrorStr": "最小值不能小于0",
			"maxErrorStr": "最大值不能大于100",
			"notEmptyDiseaseIndex": "疾病指数不能为空",
			"standardId": "基类名",
			"notEmptyStandardId": "基类名不能为空",
			"operator": "运算符",
			"triggerValue": "触发值",
			"reference": "参考",
			"notEmptyReference": "参考值不能为空",
			"isFault": "默认",
			"isOperationalRisk": "操作风险",
			"description": "描述",
			"notEmptyDescription": "描述不能为空",
			"deviceCategoryName": "设备类型",
			"notEmptyDeviceCategoryName": "设备名称不能为空",
			"sampleValue": "模板值",
			"placeholder": "请输入检索关键字",
			"addDiseaseRule": "新增疾病规则"
		},
		"subHealth": {
			"subHealthRuleId": "亚健康指标ID",
			"subHealthRuleName": "亚健康指标名称",
			"disableValue": "屏蔽",
			"enableValue": "启用",
			"enable": "是否启用",
			"notEmptySubHealthRuleName": "名称不能为空",
			"maxLengthSubHealthRuleName": "名称不能超过64个字符",
			"deviceCategory": "设备类别",
			"subHealthLevel": "亚健康等级",
			"subHealthIndex": "亚健康指数(%)",
			"minErrorStr": "最小值不能小于0",
			"maxErrorStr": "最大值不能大于100",
			"notEmptySubHealthIndex": "疾病指数不能为空",
			"standardId": "基类名",
			"notEmptyStandardId": "基类名不能为空",
			"operator": "运算符",
			"triggerValue": "触发值",
			"reference": "参考",
			"notEmptyReference": "参考值不能为空",
			"description": "描述",
			"notEmptyDescription": "描述不能为空",
			"sampleValue": "模板值",
			"deviceCategoryName": "设备类型",
			"notEmptyDeviceCategoryName": "设备名称不能为空",
			"placeholder": "请输入检索关键字",
			"addSubHealth": "新增疾病规则"
		}
	},
	"emergency": {
		"dashboard": {
			"dealWith": "已处理"
		},
		"emergencyTabs": {
			"noEmergency": "无风险"
		},
		"relatedPerson": {
			"title": "相关人员"
		},
		"baseInfo": {
			"title": "风险类型",
			"malfunction_1": "双路市电断电",
			"happenTime": "发生时间",
			"eventType": "事件分类",
			"eventLevel": "事件等级",
			"eventReason": "事件原因",
			"lessTime": "剩余时间",
			"duration": "持续时间",
			"noEmergencyEvent": "无风险事件"
		},
		"realTime": {
			"title": "实时数据",
			"impactDevices": "影响设备",
			"impactBrackets": "影响机架",
			"impactUsers": "影响客户"
		},
		"interaction": {
			"robot": "机器人",
			"plan": "预案",
			"exception": "信息获取异常",
			"closeEmergency": "结束风险"
		},
		"chatRoom": {
			"progressTitle": "处理进展",
			"chatRooTitle": "处理进度",
			"sendMsg": "发送"
		}
	},
	"misc": {
		"announcement": {
			"announcementId": "公告ID",
			"title": "标题",
			"titleRequired": "标题不能为空",
			"content": "内容",
			"contentRequired": "内容不能为空",
			"emergencyLevel0": "紧急",
			"emergencyLevel1": "重要",
			"emergencyLevel2": "普通",
			"emergencyLevel3": "一般",
			"emergencyLevel": "紧急度",
			"emergencyLevelRequired": "紧急度不能为空",
			"reader": "读者范围",
			"readerRequired": "范围不能为空",
			"sender": "发布人",
			"senderRequired": "发布人不能为空",
			"creatorId": "创建人ID",
			"category": "类别",
			"categoryRequired": "类别不能为空",
			"createTime": "创建时间",
			"placeholder": "请输入检索关键字"
		},
		"logo": {
			"logoText": "系统名称",
			"logoTextNotNull": "文本不能为空",
			"logoImg": "系统Logo",
			"backgroundImg": "系统背景图",
			"websiteTabText": "网站标签名称",
			"websiteTabIco": "网站标签Icon（16×16.ico）"
		},
		"BasePointDictionary": {
			"placeholder": "请输入检索关键字",
			"baseTypeId": "基类ID",
			"baseTypeIdNotLessThan": "基类ID长度为9-10位",
			"baseTypeName": "基类名",
			"englishName": "英文名",
			"baseEquipmentId": "基类设备",
			"baseLogicCategoryId": "基类分类",
			"baseTypeIdNotNull": "基类ID不能为空",
			"baseTypeIdCreated": "基类ID已经创建",
			"baseTypeNameNotNull": "基类名不能为空",
			"englishNameNotNull": "英文名不能为空",
			"baseEquipmentIdNotNull": "基类设备不能为空",
			"baseLogicCategoryIdNotNull": "基类分类不能为空",
			"description": "描述"
		},
		"labelPrint": {
			"assetNo": "资产编号",
			"deviceName": "设备名称"
		},
		"pageconfig": {
			"add": "新增",
			"key": "参数名称",
			"value": "值",
			"addConfig": "新增参数",
			"save": "保存全部",
			"addError": "名称或值不能为空！",
			"addSuccess": "添加成功！",
			"removeSuccess": "删除成功！",
			"operate": "操作",
			"editConfig": "编辑参数",
			"isBoolean": "使用布尔值",
			"yes": "是",
			"no": "否",
			"saveSuccess": "保存成功",
			"saveError": "保存失败",
			"loginParam": "登录参数",
			"ssoParam": "单点登录参数",
			"sysParam": "系统参数",
			"alarmOpParam": "告警页参数",
			"othersParam": "其他参数"
		}
	},
	"collection": {
		"cameras": {
			"name": "名称",
			"cameraIndexCode": "摄像机唯一码",
			"show": "查看",
			"ip": "ip地址",
			"port": "端口号",
			"cameraGroupName": "分组名称",
			"userName": "用户名",
			"password": "密码",
			"deviceName": "设备名称",
			"vendorName": "厂商",
			"model": "型号",
			"channelNumber": "通道号",
			"back": "回放",
			"updateTime": "更新时间",
			"description": "描述",
			"nameNotNull": "名称不能为空！",
			"ipNotNull": "IP地址不能为空！",
			"ipNotCorrect": "IP地址格式不正确！",
			"portNotNull": "端口号不能为空！",
			"portNotCorrect": "端口号范围：0-65535",
			"userNameNotNull": "用户名不能为空！",
			"passwordNotNull": "密码不能为空！",
			"channelNumberNotNull": "通道号不能为空!",
			"channelNumberNotCorrect": "通道号为数字！",
			"cameraIndexCodeNotNull": "摄像机唯一码不能为空",
			"cameraGroupNameNotNull": "摄像机分组不能为空",
			"vendorNameNotNull": "厂商不能为空！",
			"tips": "该摄像机关联的分组ID不存在！",
			"excelImportSuccess": "excel导入成功",
			"excelImportError": "excel导入出错",
			"templateError": "excel模板错误",
			"ImportFromExcel": "从Excel导入",
			"associateCamera": "关联摄像机",
			"selectCamera": "选择摄像机",
			"cameraTemplate": "摄像机模板"
		},
		"cameraGroups": {
			"cameraGroupId": "分组编号",
			"cameraGroupName": "分组名称",
			"parentGroupName": "上级分组",
			"description": "描述",
			"cameraGroupNameNotNull": "摄像机分组名称不能为空！",
			"cameraGroupNotAllowDelete": "分组下挂有子组,不允许删除！"
		},
		"cameraPollGroups": {
			"cameraPollGroupId": "轮巡分组编号",
			"PatrolCamera": "轮巡组摄像机",
			"addCamera": "添加摄像机",
			"cameraName": "摄像机名称",
			"addcamerasToCurrent": "请添加摄像机到当前轮巡组",
			"cameraPollGroupName": "轮巡分组名称",
			"pollInterval": "轮巡间隔(秒)",
			"description": "描述",
			"ip": "ip地址",
			"channelNumber": "通道号",
			"cameraGroupNameNotNull": "轮巡分组名称不能为空！"
		}
	},
	"announcementDetail": {
		"title": "标题",
		"category": "类别",
		"sender": "发布人：",
		"announcementList": "公告列表",
		"time": "时间："
	},
	"business": {
		"customer": {
			"customerName": "客户名",
			"placeholder": "请输入检索关键字",
			"nameNotEmpty": "客户不能为空",
			"creator": "创建人",
			"customerType": "客户类型",
			"customerTypePersonal": "个人",
			"customerTypeEnterprise": "企业",
			"certificateType": "证件类型",
			"certificateTypeIDCard": "身份证",
			"certificateTypeBusinesslicense": "营业执照",
			"certificateTypeOther": "其他",
			"certificateId": "证件号",
			"certificateIdNotEmpty": "证件号不能为空",
			"address": "客户地址",
			"addressNotEmpty": "地址不能为空",
			"postCode": "邮编",
			"contact": "联系人",
			"organizationId": "组织ID",
			"description": "描述"
		},
		"contract": {
			"title": "合同标题",
			"code": "合同编码",
			"customerId": "甲方ID",
			"customerName": "甲方名称",
			"seller": "乙方名称",
			"startTime": "开始日期",
			"createTime": "合同签订时间",
			"validDuration": "期限(单位/天)",
			"amount": "金额",
			"currencyUnit": "货币单位",
			"placeholder": "请输入检索关键字",
			"notEmpty": "不能为空",
			"customerIdCheck": "请输入1~8位的数字",
			"validDurationCheck": "请输入1~8位的数字",
			"validAmount": "金额必须为数字"
		},
		"contractTerm": {
			"contractTermId": "合同条款ID",
			"contractId": "所属合同ID",
			"displayIndex": "合同条款序号",
			"title": "条款标题",
			"contractTermDetail": "合同条款详情",
			"content": "条款内容",
			"placeholder": "请输入检索关键字",
			"notEmpty": "不能为空",
			"lengthCheck": "请输入1~8位的数字"
		},
		"rank": {
			"alarm": "告警",
			"roomLocation": "机房位置",
			"whereRank-u": "所在机架U位",
			"assetNumber": "设备资产编号",
			"equipmentModel": "设备型号",
			"equipmentManufacturers": "设备厂家",
			"contactInformation": "联系方式"
		}
	},
	"document": {
		"documentTypeTitle": "文档分类管理",
		"documentType": {
			"documentTypeCode": "文档分类号",
			"documentTypeCodeNotNull": "文档分类号不能为空",
			"documentTypeName": "文档分类名",
			"documentTypeNameNotNull": "文档分类名不能为空",
			"description": "说明"
		},
		"documentLabel": {
			"title": "文档标签",
			"labelName": "标签名称",
			"placeholder": "请输入检索关键字",
			"documentLabelNameNotNull": "标签名称不能为空",
			"description": "备注"
		},
		"securityClassification": {
			"title": "文档密级",
			"securityClassificationName": "文档密级名称",
			"placeholder": "请输入检索关键字",
			"securityClassificationNameNotNull": "文档密级名称不能为空",
			"securityClassificationNotRepeat": "文档密级名称不能重复",
			"description": "备注"
		},
		"documentUpload": {
			"title": "文档上传",
			"documentName": "文档名称",
			"documentTypeName": "文档分类",
			"sercurityLevel": "文档密级",
			"documentLabelName": "文档标签",
			"placeholder": "请输入检索关键字",
			"documentNameNotNull": "上传文件不能为空",
			"documentShouldUpload": "请先上传文档",
			"uploadTime": "上传时间",
			"upLoaderName": "上传者",
			"uploadSuccess": "上传成功",
			"uploadError": "上传失败",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消"
		},
		"documentSearch": {
			"title": "文档查找",
			"fileName": "文件名",
			"fileType": "文件类型",
			"filePath": "文件路径",
			"uploadTime": "上传时间",
			"uploaderName": "上传者",
			"securityLevelName": "文档密级",
			"documentLabelName": "文档标签",
			"documentTypeName": "文档分类",
			"download": "下载",
			"retention": "保留",
			"search": "搜索",
			"timePeriod": "时间段",
			"endTime": "结束时间",
			"reset": "重置",
			"fullTextSearch": "全文检索",
			"generalSearch": "普通搜索"
		},
		"documentBrowsing": {
			"title": "文档浏览",
			"classification": "文档分类",
			"reName": "编辑",
			"create": "新增",
			"delete": "删除",
			"confirmDelete": "请确认是否删除名称为:{0}的节点",
			"updateSuccess": "更新成功",
			"updateFail": "更新失败",
			"createSuccess": "创建成功",
			"createFail": "创建失败",
			"maxlengthError": "名称不能超过32个字",
			"list": {
				"filePath": "文件路径",
				"fileName": "文件名称",
				"uploadTime": "上传时间",
				"fileType": "文件类型",
				"retention": "保留",
				"documentType": "文档类型",
				"documentLabelName": "文档标签",
				"securityLevel": "安全等级",
				"uploaderId": "上传者ID",
				"documentTypeName": "文档类型",
				"securityLevelName": "文档密级",
				"uploaderName": "上传者",
				"download": "下载"
			}
		}
	},
	"emergencyPlan": {
		"emergencyPlanId": "应急方案ID",
		"emergencyPlanName": "名称",
		"nameNotEmpty": "名称不能为空",
		"timeLimit": "SLA时限（分钟）",
		"timeLimitPlaceholder": "分钟",
		"timeLimitNotEmpty": "SLA时限不能为空",
		"timeLimitType": "SLA时限只能为数字",
		"timeLimitMax": "SLA时限不允许超过10000分钟",
		"timeLimitMin": "SLA时限必须为大于0的正整数",
		"dutyName": "岗位",
		"personName": "人员",
		"emergencyLevelId": "等级ID",
		"emergencyLevelName": "等级",
		"emergencyLevelNotEmpty": "等级不能为空",
		"emergencyTriggerId": "触发器ID",
		"emergencyTriggerName": "触发器名称",
		"affectedFacilities": "受影响的设备过滤器",
		"affectedRack": "受影响的机架过滤器",
		"emergencyTriggerParameter": "触发参数",
		"description": "描述",
		"placeholder": "请输入检索关键字",
		"title": "应急方案",
		"sopName": "SOP名称",
		"person": {
			"relatedPersons": "相关人员",
			"name": "姓名",
			"gender": "性别",
			"organization": "组织",
			"selected": "已选成员",
			"placeholder": "请输入检索关键字",
			"personNotEmpty": "人员不能为空"
		},
		"personGroup": {
			"relatedPersonGroups": "相关值班组",
			"selected": "已选值班组",
			"personGroupNotEmpty": "值班组不能为空"
		},
		"emergencyTrigger": {
			"alarmCategory": "告警触发器",
			"alarmCategoryNotEmpty": "告警类别不能为空",
			"base": "详情",
			"baseTypeId": "基类型ID",
			"baseTypeName": "基类型名称",
			"englishName": "英文名",
			"categoryName": "分类名称",
			"description": "描述",
			"logicCategoryName": "逻辑类别名称",
			"selectedSubmit": "确认",
			"nameLabel": "名称：",
			"idLabel": "ID：",
			"nameNotEmpty": "告警类别不允许为空",
			"isSelected": "已选基类型",
			"required": "基类型必选",
			"maxLength": "最多只能选100个基类型"
		},
		"affectedDeviceFilter": {
			"name": "受影响的设备过滤",
			"nameNotEmpty": "受影响的设备不能为空",
			"selectedName": "已选设备",
			"deviceName": "设备名称：",
			"deviceCategory": "设备类型：",
			"affectedDeviceParams": "按具体设备必选某设备",
			"maximum": "最多只能选100个设备"
		},
		"affectedDevice": {
			"description": "描述",
			"deviceName": "名称",
			"roomId": "房间ID",
			"roomName": "房间名称",
			"deviceCategory": "设备类型",
			"status": "状态",
			"maskedName": "是否屏蔽",
			"stateName": "状态名称",
			"placeholder": "请输入检索关键字",
			"room": "房间"
		},
		"affectedRackFilter": {
			"name": "受影响的机架过滤",
			"nameNotEmpty": "受影响的机架不能为空",
			"rackName": "名称：",
			"roomId": "房间ID：",
			"selectedName": "已选机架",
			"affectedRackParams": "按具体机架必选某机架",
			"maxError": "最多只能选100个机架"
		},
		"affectedRacks": {
			"rackId": "机架ID",
			"name": "机架名称",
			"assetId": "关联资产",
			"model": "模式",
			"symbolVersion": "符号版本",
			"serialNumber": "序列号",
			"powerCapacity": "功率容量",
			"weightCapacity": "重量容量",
			"coolingCapacity": "制冷量",
			"renderId": "渲染ID",
			"supplyMode": "供应模式",
			"customerId": "客户ID",
			"using": "应用",
			"unitHeight": "整机高度",
			"business": "商业",
			"roomId": "房间ID",
			"usedTime": "使用时间",
			"description": "描述",
			"containerPath": "所在位置",
			"rfidposition": "rf id位置",
			"rfid": "rf ID",
			"placeholder": "请输入检索关键字"
		},
		"affectedLevel": {
			"name": "影响等级",
			"nameNotEmpty": "影响等级不能为空"
		},
		"rank": {
			"alarm": "告警",
			"roomLocation": "机房位置",
			"whereRank-u": "所在机架U位",
			"assetNumber": "设备资产编号",
			"equipmentModel": "设备型号",
			"equipmentManufacturers": "设备厂家",
			"contactInformation": "联系方式"
		}
	},
	"customServicesRegister": {
		"placeholder": "请输入检索关键字",
		"customServiceConfigId": "服务Id",
		"customServiceGroup": "所属服务组",
		"name": "服务名称",
		"uniqueIdentify": "唯一标识",
		"category": "类型",
		"servicesZip": "服务压缩包",
		"imagePath": "图标",
		"link": "域名URL",
		"path": "分类路径",
		"params": "URL参数",
		"checkStatus": "是否审核",
		"checked": "是",
		"unchecked": "否",
		"builtInService": "是否为内置服务",
		"url": "URL地址",
		"serviceNameNotNull": "服务名称不能为空",
		"serviceGroupsNotNull": "所属服务组不能为空",
		"serviceKeyNotNull": "服务标识不能为空",
		"serviceImagePathNotNull": "图标不能为空",
		"serviceLinkNotNull": "域名URL不能为空",
		"serviceZipfileNotNull": "压缩包不允许为空",
		"serviceNameValid": "仅允许汉字、英文字母及下划线;",
		"serviceCategoryRequired": "服务类型为必填项",
		"updateServiceSuccess": "修改服务{item}完成",
		"updateServiceFailure": "修改服务{item}失败",
		"insertServiceSuccess": "新增服务{item}完成",
		"insertServiceFailure": "新增服务{item}失败",
		"deleteServiceSuccess": "删除服务成功",
		"deleteServiceFailure": "删除服务失败",
		"uploadFailure": "上传压缩包失败",
		"illegalFormat": "非法的文件格式",
		"confirmDeleteService": "请确认删除服务",
		"nameNotLessThan": "服务名称不能少于4个字母",
		"patternStrOrNum": "请填写数值,字母或者_",
		"alreadyCreated": "服务标识已经存在",
		"serviceKeyInValid": "服务标识无效，需由英文数字组成",
		"serviceZipInvalid": "服务压缩包格式错误"
	},
	"ubitFramework": {
		"assetc": "资产配置",
		"ubitList": "U位列表",
		"noBindFrameworkName": "未绑定机架名称",
		"ipAddress": "IP地址",
		"bindFramework": "绑定机架",
		"frameworkName": "机架名称",
		"frameworkId": "机架ID",
		"bindUbitName": "关联u位",
		"unitHeight": "u高",
		"containerPath": "位置",
		"search": "请输入检索关键字",
		"action": "操作"
	},
	"task": {
		"iAsInitiator": "我创建的",
		"iAsPrincipal": "我负责的",
		"iAsParticipant": "我参与的",
		"iAsStarTask": "我收藏的",
		"common": {
			"task": "任务",
			"activeTask": "活动任务",
			"taskManage": "任务管理",
			"tasklist": "个人任务列表",
			"personalTaskStatistics": "人员任务统计",
			"globaltasklist": "全员任务列表",
			"taskedit": "任务详情",
			"subTask": "子任务",
			"taskName": "任务内容",
			"taskNameNotEmpty": "任务内容不能为空",
			"taskNameLengthMinLimit": "任务内容最小长度为4",
			"taskType": "任务类型",
			"startTime": "开始时间",
			"taskAdd": "新建任务",
			"taskNameHint": "请输入任务内容，最大长度128",
			"principal": "负责人",
			"principalNotEmpty": "负责人不能为空",
			"participant": "参与人",
			"taskDeadline": "截止日期",
			"taskNotify": "提醒",
			"addAttachment": "增加附件",
			"deleteAttachmentSuccess": "删除附件成功",
			"deleteAttachmentError": "删除附件失败",
			"descriptionHint": "备注，最大长度255",
			"attachment": "附件",
			"description": "备注",
			"principalRequired": "请选择负责人",
			"taskNameRequired": "请输入任务内容",
			"minLengthMorethan": "最小长度为",
			"maxLengthLessthan": "最大长度为",
			"notNotify": "不提醒",
			"notify15minutesAgo": "截止前15分钟提醒",
			"notify1HourAgo": "截止前1小时提醒",
			"notify3HoursAgo": "截止前3小时提醒",
			"notify1DayAgo": "截止前1天提醒",
			"cancelTask": "取消任务",
			"cancelTaskSuccess": "取消任务成功",
			"cancelTaskFail": "取消任务失败",
			"inspectType": "月度维护类型",
			"inspectTypeRequired": "请选择月度维护类型",
			"completedTaskSuccess": "任务完成成功",
			"completedTaskFail": "任务完成失败",
			"reopenTaskSuccess": "任务重新打开成功",
			"reopenTaskFail": "任务重新打开失败",
			"finishTaskAlert": "是否确认完成任务",
			"reOpenTaskAlert": "是否确认重新打开任务",
			"createTaskSuccess": "新增任务成功",
			"createTaskFailed": "新增任务失败",
			"updateTaskSuccess": "编辑任务成功",
			"updateTaskFailed": "新增任务失败",
			"starTaskSuccess": "收藏任务成功",
			"starTaskError": "收藏任务失败",
			"cancelStarTaskSuccess": "取消收藏成功",
			"cancelStarTaskError": "取消收藏失败",
			"reOpenTaskMessage": "此任务已完成,确定要将其标记为未完成吗?",
			"completed": "完成",
			"canceled": "取消",
			"overdue": "逾期",
			"batchTip": "批量操作提示",
			"resume": "恢复",
			"resumeSuccess": "恢复任务成功",
			"resumeFail": "恢复任务失败",
			"cancelTaskAlert": "是否确认取消任务",
			"resumeTaskAlert": "是否确认恢复任务",
			"batchComplete": "批量完成",
			"batchReopen": "批量重新打开",
			"batchCancel": "批量取消",
			"batchResume": "批量恢复"
		},
		"tasktag": {
			"tag": "标签",
			"tagTitle": "任务标签",
			"inputTagName": "请输入标签名...",
			"innerTagNotRemove": "系统内置标签，不允许修改或删除!",
			"taskTagHadUsedNotRemove": "该标签已有任务使用，不能删除!",
			"maxLengthExplain": "标签名长度不得超过128！"
		},
		"taskComment": {
			"title": "动态",
			"sendMessage": "评论",
			"hadSendMessage": "发表了评论:",
			"sendMessageSuccess": "评论发表成功!",
			"sendMessageError": "评论发表失败!",
			"commentPlaceholder": "请输入留言..",
			"commentText": "留言:",
			"showAll": "显示全部",
			"closeAll": "隐藏，保留最新5条"
		},
		"alarmTask": {
			"title": "告警工单任务",
			"alarmTask1": "一级告警任务",
			"alarmTask2": "二级告警任务",
			"alarmTask3": "三级告警任务",
			"alarmTask4": "四级告警任务"
		},
		"taskTrend": {
			"title": "近30天任务趋势",
			"taskCount": "任务数"
		},
		"taskStatusStatistics": {
			"title": "近30天任务状态统计",
			"cancelTaskCounts": "取消的",
			"completedTaskCounts": "完成的",
			"empireTaskCounts": "逾期的",
			"onTaskCounts": "进行中"
		},
		"todoTask": {
			"title": "待办任务TOP10"
		}
	},
	"maintenance": {
		"timingCheck": {
			"jobForm": "巡检表单",
			"jobResult": "巡检结果",
			"result": {
				"jobName": "任务名称",
				"scheduleTime": "启动时间",
				"lastFiredTime": "上一次执行时间",
				"groupName": "群组名称",
				"jobStatus": "当前状态",
				"nextFireTime": "下一次执行时间",
				"view": "详情"
			}
		}
	},
	"deviceTaskForm": {
		"formBackup": "表单备份",
		"expands": "全部展示",
		"collapses": "全部隐藏",
		"empty": "无",
		"edit": "编辑",
		"deleteForm": "删除表单",
		"enable": "使能",
		"maintenanceProject": "维护项目",
		"add": "添加",
		"quicklyAdd": "快速添加",
		"detail": "明细",
		"inputType": "输入类型",
		"signalId": "信号Id",
		"baseId": "基类Id",
		"dataPoint": "数据点",
		"cycle": "周期",
		"arrangement": "安排",
		"action": "操作",
		"unbind": "没有绑定",
		"bind": "绑定",
		"delete": "删除",
		"save": "保存",
		"cancelEditing": "取消编辑",
		"createAndSave": "创建项目并保存",
		"addDeviceType": "增加设备分类",
		"inputDeviceType": "请输入名称",
		"noData": "没有数据",
		"dataCheckerTitle": "数据校验"
	},
	"autoInspectDash": {
		"base": {
			"routeName": "自动巡检"
		},
		"overview": {
			"title": "自动巡检总览"
		},
		"detail": {
			"title": "自动巡检结果详情"
		}
	},
	"timingCheck": {
		"results": {
			"autoInspect": "自动巡检",
			"print": "数据打印",
			"expands": "全部展示",
			"collapses": "全部隐藏",
			"checkItem": "检查项",
			"result": "结果",
			"noData": "没有相关数据",
			"autoInspectReport": "定时巡检报告",
			"verificationProject": "核查项目"
		}
	},
	"timerSetting": {
		"chooseRightTime": "选择正确的时间",
		"fiveMinutes": "每 5 分钟",
		"fifteenMinutes": "每 15 分钟",
		"halfHours": "每 30 分钟",
		"anHours": "每小时",
		"eightHours": "每8小时",
		"month": "每月 ",
		"day": " 号 ",
		"hours": "点",
		"daily": "每日 ",
		"submit": "提交",
		"close": "关闭",
		"date": "日期",
		"setInterval": "定时器设置",
		"hour": "小时"
	},
	"selectPoint": {
		"choosePoint": "数据点选择",
		"deviceChoosePoint": "设备选择",
		"building": "楼栋",
		"floor": "楼层",
		"room": "房间",
		"device": "设备",
		"point": "数据点",
		"noChoose": "没有选项",
		"signal": "信号",
		"camera": "摄像头",
		"selectSignal": "选择信号",
		"selectDevice": "选择设备",
		"selectControl": "选择控制",
		"selectByEquipment": "根据设备绑定",
		"selectByCurrentEquipment": "根据当前设备绑定"
	},
	"selectResource": {
		"checkResource": "选择资源",
		"resourceType": "资源类型",
		"resourceLabel": "资源标签",
		"submit": "确定",
		"cancel": "取消",
		"number": "id",
		"resourceName": "资源名",
		"monitoring": "监控设备",
		"upDevice": "上架设备",
		"assetName": "资产名",
		"preserveDevice": "维护设备"
	},
	"taskDeviceMonth": {
		"maintenanceForm": "维护表单",
		"viewCalendarYear": "查看历年"
	},
	"license": {
		"uploadLicense": "上传及更新License",
		"uploadFileTypeInValid": "license文件类型不正确，请上传.v2c文件！",
		"features": "功能模块",
		"featuresID": "功能ID",
		"featuresContent": "内容",
		"featureValid": "有效",
		"featureInval": "无效",
		"contractNo": "合同号",
		"applyFile": "生成License申请文件",
		"customerNameEmpty": "客户名不能为空",
		"contractNoEmpty": "合同号不能为空",
		"exportFail": "生成文件失败",
		"exportSuccess": "生成文件成功",
		"licenseUpdatetFail": "更新license文件失败",
		"licenseUpdateSuccess": "更新license文件成功"
	},
	"tag": {
		"title": "标签",
		"searchText": "搜索标签...",
		"createTag": "新建标签",
		"updateTag": "更改标签",
		"tagName": "标签名称",
		"selectTagColor": "请选择一个颜色",
		"tagTypeName": "标签类型名称",
		"createOpt": "创建",
		"saveOpt": "保存",
		"deleteOpt": "删除",
		"deleteContent": "是否删除标签？此操作无法撤销。",
		"createTagFail": "创建标签失败",
		"createTagSuccess": "创建标签成功",
		"updateTagFail": "修改标签失败",
		"updateTagSuccess": "修改标签成功"
	},
	"from": {
		"month": "月",
		"quarter": "季度",
		"halfYear": "半年",
		"year": "年",
		"input": "输入",
		"judgment": "判断",
		"combination": "组合"
	},
	"workbench": {
		"report": "报表",
		"monthMaintance": "月度维护",
		"signOk": "签到",
		"switchDuty": "交接班",
		"searchDevice": "查找设备",
		"alarmReview": "告警总览",
		"weather": {
			"sunny": "晴",
			"overcast": "阴",
			"cloudy": "多云",
			"sunToCloudy": "晴转多云",
			"passingRain": "阵雨",
			"middleRain": "中雨",
			"gale": "大风",
			"haze": "霾",
			"flurry": "小雨",
			"thunderShower": "雷阵雨",
			"rainstorm": "暴雨",
			"lightSnow": "小雪",
			"middleSnow": "中雪",
			"sleet": "雨夹雪",
			"fog": "雾",
			"heavySnow": "大雪",
			"extraHeavyRain": "特大暴雨",
			"sinkFloat": "浮尘",
			"sandStorm": "沙尘暴"
		}
	},
	"asset": {
		"common": {
			"assetOverview": "资产总览",
			"row": "行",
			"column": "列",
			"fieldRequired": "单元格内容不允许为空",
			"minlength": "最小长度为",
			"maxlength": "最大长度为",
			"integerRequired": "必须为整型",
			"numericRequired": "必须为数字类型",
			"datetimeRequired": "单元格必须为文本类型，格式例如1970-01-01 15:01:01",
			"uniqueFieldRequired": "单元格内容在该列必须唯一",
			"uniqueFieldRequiredInSameGroup": "该列单元格同一层级下必须唯一",
			"foreignKeyRequired": "单元格内容不满足外键约束",
			"excelImportSuccess": "excel导入成功",
			"excelImportError": "excel导入出错",
			"exportexcelError": "导出错误信息",
			"templateError": "excel模板错误",
			"assetId": "资产ID",
			"assetCode": "资产编码",
			"assetName": "资产名称",
			"description": "备注"
		},
		"assetPark": {
			"owner": "拥有人",
			"endUser": "使用人",
			"vendorId": "供应商ID",
			"vendorName": "供应商",
			"validDate": "有效期",
			"startUsingTime": "启用时间",
			"location": "位置",
			"longitude": "经度",
			"latitude": "纬度"
		},
		"assetBuilding": {
			"assetParkId": "园区ID",
			"assetParkName": "园区",
			"owner": "拥有人",
			"endUser": "使用人",
			"vendorId": "供应商ID",
			"vendorName": "供应商",
			"validDate": "有效期",
			"startUsingTime": "启用时间",
			"location": "位置",
			"longitude": "经度",
			"latitude": "纬度",
			"antiEarthquakeLevel": "抗震等级",
			"firstImport": "请先在园区导入园区excel表",
			"tips": "该园区ID不在园区表的资产ID中"
		},
		"assetFloor": {
			"assetBuildingId": "大楼ID",
			"assetBuildingName": "大楼",
			"owner": "拥有人",
			"endUser": "使用人",
			"startUsingTime": "启用时间",
			"height": "层高(m)",
			"area": "面积(m^2)",
			"firstImport": "请先在大楼导入大楼excel表",
			"tips": "该大楼ID不在大楼表的资产ID中"
		},
		"assetRoom": {
			"assetFloorId": "楼层ID",
			"assetFloorName": "楼层",
			"owner": "拥有人",
			"endUser": "使用人",
			"startUsingTime": "启用时间",
			"location": "位置",
			"width": "宽度(m)",
			"length": "长度(m)",
			"floorHeight": "层高(m)",
			"netHeight": "净高(m)",
			"floorBoardHeight": "地板高(m)",
			"ridgeHeight": "垄高(m)",
			"area": "面积(m^2)",
			"powerCapacity": "电量(w)",
			"weightCapacity": "承重(kg)",
			"coolingCapacity": "冷量(kj)",
			"firstImport": "请先在楼层导入楼层excel表",
			"tips": "该楼层ID不在楼层表的资产ID中"
		},
		"assetMdc": {
			"assetRoomId": "房间ID",
			"assetRoomName": "房间",
			"owner": "拥有人",
			"endUser": "使用人",
			"model": "型号",
			"validDate": "有效期",
			"startUsingTime": "启用时间",
			"location": "位置",
			"width": "宽度(m)",
			"length": "长度(m)",
			"height": "高度(m)",
			"powerCapacity": "电量(W)",
			"weightCapacity": "承重(KG)",
			"coolingCapacity": "冷量(W)",
			"columnCount": "列数",
			"firstImport": "请先在房间导入房间excel表",
			"tips": "该房间ID不在房间表的资产ID中"
		},
		"assetRack": {
			"assetMDCId": "MDCID",
			"assetMDCName": "MDC",
			"assetRoomId": "房间ID",
			"assetRoomName": "房间",
			"unitCount": "U高",
			"symbolVersion": "版本",
			"serialNumber": "序列号",
			"owner": "拥有人",
			"endUser": "使用人",
			"model": "型号",
			"vendorId": "供应商ID",
			"vendorName": "供应商",
			"validDate": "有效期",
			"startUsingTime": "启用时间",
			"location": "位置",
			"width": "宽度(m)",
			"length": "长度(m)",
			"height": "高度(m)",
			"ratedPowerCapacity": "额定电量(W)",
			"ratedWeightCapacity": "额定承重(KG)",
			"ratedCoolingCapacity": "额定冷量(W)",
			"supplyMode": "供电方式",
			"purpose": "用途",
			"assetMdcTips": "该MDCID不在MDC表的资产ID中",
			"assetRoomTips": "该房间ID不在房间表的资产ID中"
		},
		"assetDeviceOfRack": {
			"assetRackId": "机架ID",
			"assetRackName": "机架",
			"owner": "拥有人",
			"endUser": "使用人",
			"vendorId": "供应商ID",
			"vendorName": "供应商",
			"model": "型号",
			"type": "类型",
			"ucount": "U高",
			"unitIndex": "所在U位",
			"ratedPower": "额定功率(W)",
			"client": "客户",
			"business": "业务",
			"validDate": "有效期",
			"startUsingTime": "启用时间",
			"productionDate": "生产日期",
			"location": "位置",
			"width": "宽度(m)",
			"length": "长度(m)",
			"height": "高度(m)",
			"weight": "重量(kg)",
			"firstImport": "请先在机架导入机架excel表",
			"tips": "该机架ID不在机架表的资产ID中",
			"list": {
				"searchPlaceholder": "请输入检索关键字"
			}
		},
		"assetGeneral": {
			"owner": "拥有人",
			"endUser": "使用人",
			"vendorId": "供应商ID",
			"vendorName": "供应商",
			"model": "型号",
			"startUsingTime": "启用时间",
			"location": "位置",
			"size": "尺寸",
			"assetType": "资产类型",
			"business": "业务"
		},
		"assetOverview": {
			"assetDeviceOfRack": "上架设备",
			"assetGeneral": "通用资产"
		},
		"assetRackInformation": {
			"title": "资产上下架信息",
			"equipment": "资产编号",
			"rack": "机架",
			"operation": "资产名称"
		},
		"assetDevice": {
			"title": "资产列表",
			"assetDeviceId": "资产ID",
			"deviceCode": "编号",
			"deviceCodeNotDuplicate": "编号不能重复",
			"assetCategoryNotRepeat": "类型名称不能重复",
			"assetExtendNameNotRepeat": "扩展字段名称不能重复",
			"deviceName": "名称",
			"assetCategoryId": "类型ID",
			"deviceType": "类型",
			"brand": "品牌",
			"model": "型号",
			"capacityParameter": "容量参数",
			"settingPosition": "安装位置",
			"serialNumber": "序列号",
			"manufactor": "厂家",
			"extendField": "扩展字段",
			"exportType": "导入方式",
			"exportTypeSelect": "选择导入方式",
			"retainCategory": "保留新增资产类型",
			"retainTips": "点击'是',系统将会自动保存用户填写且系统不存在的资产类型",
			"excelFileName": "资产列表",
			"exportAssetDevice": "导入资产列表",
			"date": "日期",
			"monthAsset": "当月新增资产",
			"deviceCodeNotNull": "编号不能为空",
			"deviceNameNotNull": "名称不能为空",
			"notNull": "不能为空"
		},
		"assetCategory": {
			"assetCategoryName": "类型名称",
			"remarks": "备注"
		},
		"extendField": {
			"extCode": "编码",
			"extName": "名称",
			"extDesc": "描述",
			"extOrder": "序号",
			"extNecessary": "是否必填",
			"extDataType": "类型",
			"text": "文本",
			"number": "数字",
			"date": "日期",
			"bindDevice": "绑定设备",
			"bindTable": "绑定表",
			"tableName": "数据库表名",
			"fieldName": "表字段（用于下拉框显示）",
			"fieldNameTips": "使用数据表里的某字段作为下拉框的显示名称",
			"fieldValue": "表字段（用于下拉框存储）",
			"fieldValueTips": "使用数据表里的某字段作为下拉框的存储值",
			"extNecessaryNotNull": "是否必填不能为空",
			"extDataTypeNotNull": "类型不能为空",
			"extCodeNotNull": "编码不能为空",
			"extNameNotNull": "名称不能为空"
		},
		"configurationTool": {
			"stationName": "局站",
			"houseName": "局房",
			"monitorUnitName": "监控单元",
			"portName": "端口",
			"ipAddress": "IP地址",
			"portAttribute": "端口号",
			"samplerUnitName": "采集单元",
			"equipmentName": "设备名",
			"equipmentTemplateName": "引用设备模板",
			"address": "地址",
			"dllPath": "动态库名称",
			"spUnitInterval": "采集周期",
			"isUpload": "是否已上传",
			"uploadConfig": "上传配置",
			"deleteConfig": "删除配置",
			"driveTemplateName": "引用驱动模板",
			"isReset": "是否需要下发",
			"isDeleteConfig": "请确认是否要删除与",
			"isUploadConfig": "请确认是否要上传与",
			"isAllUpload": "上传全部",
			"isAllDelete": "删除全部",
			"rwAttributes": "读写属性"
		}
	},
	"realTime": {
		"common": {
			"searchFilter": "请输入条件查询",
			"searchFilter2": "请输入条件查询",
			"searchFilter4": "请输入服务器名快速定位",
			"title": "设备列表",
			"searchFilter3": "请输入条件查询（区分大小写）"
		}
	},
	"material": {
		"warehouse": {
			"title": "库房管理",
			"placeholder": "请输入检索关键字",
			"warehouseName": "库房名",
			"warehouseType": "库房类型",
			"location": "位置",
			"belong": "归属",
			"remark": "备注",
			"warehouseNameNotEmpty": "库房名不能为空",
			"warehouseNameNotRepeat": "库房名不能重名",
			"warehouseRelateMaterial": "当前库房已经有出入库记录,不可以删除库房:"
		},
		"materialItem": {
			"title": "物资项管理",
			"placeholder": "请输入检索关键字",
			"materialItemName": "物资项名",
			"specifications": "规格",
			"materialItemCategory": "物资项类型",
			"equipmentCode": "设备编码",
			"assetCode": "资产编码",
			"unit": "单位",
			"materialItemNameNotEmpty": "物资项名不能为空",
			"materialItemNameNotRepeat": "物资项名不能重名",
			"specificationsNotEmpty": "规格不能为空",
			"materialRelateWarehouse": "当前物资项已经有出入库记录,不可以删除物资项:"
		},
		"materialManagement": {
			"title": "物资管理",
			"enterWarehouseTitle": "物资入库",
			"outWarehouseTitle": "物资出库",
			"materialWarehouseDetailTitle": "物资详情",
			"placeholder": "请输入检索关键字",
			"operation": "操作",
			"materialName": "物资名称",
			"specifications": "物资规格",
			"materialCategory": "物资类型",
			"overdualCount": "逾期",
			"warehouseCount": "借出/库存",
			"ownerWarehouse": "所在库房",
			"enterWarehouseTip": "入库",
			"outWarehouseTip": "出库",
			"materialWarehouseDetailTip": "详情",
			"materialDetail": {
				"materialInfo": "物资信息",
				"materialLentLog": "借出记录",
				"materialOverdualLog": "逾期记录",
				"warehouseChangeCurve": "库存变化曲线",
				"warehouseChangeLog": "物资变化记录",
				"lentCountTitle": "借出",
				"returnDateTitle": "日应归还",
				"overdualCountTitle": "逾期",
				"materialName": "物资名称",
				"specifications": "物资规格",
				"equipmentCode": "设备编码",
				"assetCode": "资产编码",
				"unit": "单位",
				"warehouseCount": "剩余库存",
				"chanageAmount": "变更数量",
				"chanageDirection": "变更方向",
				"chanageOperation": "变更操作",
				"returnDate": "归还日期",
				"returnStatus": "归还状态",
				"returnedMaterial": "已归还",
				"unreturnMaterial": "未归还",
				"manager": "操作员",
				"warehouseName": "库房",
				"requester": "请求人",
				"chanageDate": "变更日期",
				"remark": "备注",
				"lentCount": "借出数量",
				"lentDate": "借出日期",
				"warehouseCountTip": "库存数",
				"entryDirectionEnum": "入库",
				"outDirectionEnum": "出库",
				"receiveOperationEnum": "领用",
				"entryOperationEnum": "入库",
				"lentOperationEnum": "借用",
				"returnOperationEnum": "归还"
			},
			"enterWarehouse": {
				"warehouseName": "库房",
				"materialItemName": "物资项",
				"changeOperation": "变更操作",
				"changeAmount": "变更数量",
				"changeDate": "变更日期",
				"returnDate": "归还日期",
				"lentRecord": "借出记录",
				"remark": "备注",
				"warehouseNameRequired": "库房不允许为空",
				"changeAmountRequired": "变更数量不允许为空",
				"changeAmountInvalid": "变更数量只能是正整数",
				"changeAmountBeyound": "变更数量必须等于选择的借出记录对应的借出数量",
				"changeDateRequired": "变更日期不允许为空",
				"returnDateRequired": "归还日期不允许为空",
				"returnDateInvalid": "归还日期不允许小于变更日期",
				"lentRecordRequired": "借出记录不允许为空"
			},
			"outWarehouse": {
				"warehouseName": "库房",
				"materialItemName": "物资项",
				"changeOperation": "变更操作",
				"changeAmount": "变更数量",
				"changeDate": "变更日期",
				"returnDate": "归还日期",
				"requesterName": "请求人",
				"remark": "备注",
				"materialTotal": "库存",
				"warehouseNameRequired": "库房不允许为空",
				"changeAmountRequired": "变更数量不允许为空",
				"changeAmountInvalid": "变更数量只能是正整数",
				"changeAmountBeyound": "变更数量不能大于当前库房下的库存",
				"changeDateRequired": "变更日期不允许为空",
				"returnDateRequired": "归还日期不允许为空",
				"returnDateInvalid": "归还日期不允许小于变更日期",
				"requesterNameRequired": "请求人不允许为空"
			}
		}
	},
	"generalLabel": {
		"label": {
			"title": "标签",
			"placeholder": "请输入检索关键字",
			"tagName": "名称",
			"color": "颜色",
			"tagType": "标签类型",
			"tagTypeName": "标签类型名称",
			"colorNotNull": "显示颜色不能为空",
			"tagTypeNotNull": "标签类型不能为空",
			"createTime": "创建时间",
			"createType": "新建标签类型",
			"deleteContent": "是否删除标签类型？此操作无法撤销。",
			"updateLabelSuccess": "修改标签类型成功",
			"updateLabelFailed": "修改标签类型失败",
			"editLabelType": "编辑标签类型",
			"labelNotAllowEdit": "此标签不允许修改",
			"labelNotAllowDelete": "此标签不允许删除",
			"Notrepetition": "标签类型名称不能重复"
		}
	},
	"resource": {
		"custom": {
			"selectedAsset": "已选资产",
			"notNull": "不能为空"
		},
		"itDevice": {
			"property": {
				"itdeviceName": "IT设备名称",
				"serialNumber": "序列号",
				"customer": "客户",
				"itDeviceModel": "IT设备模型",
				"bunsiness": "业务",
				"launchDate": "启用时间",
				"remark": "备注",
				"uindex": "U位",
				"state": "状态",
				"rackUpDown": "上下架",
				"DownloadTemplate": "下载模板",
				"itdeviceTemplate": "IT设备模板",
				"totalSum": "总条数",
				"IP": "IP地址"
			}
		},
		"deviceModel": {
			"property": {
				"modelId": "关联设备模型",
				"modelName": "设备模型名称",
				"deviceCategory": "设备型号",
				"categoryName": "模型分类",
				"modelType": "类型",
				"length": "长度(m)",
				"width": "宽度(m)",
				"height": "高度(m)",
				"weight": "重量(kg)",
				"ratePower": "额定功率(kW)",
				"coolingCapacity": "冷量(kJ)",
				"unitHeight": "U高",
				"faceMaterial": "正面材质",
				"sideMaterial": "侧面材质",
				"backMaterial": "背面材质",
				"description": "描述",
				"selectedDeviceModel": "已选设备模型",
				"modelNameNotNull": "设备模型名称不允许为空",
				"deviceCategoryNotNull": "设备型号不允许为空",
				"ratePowerNotNull": "额定功率不允许为空",
				"rateCoolingNotNull": "冷量不允许为空",
				"rateWeightNotNull": "重量不允许为空",
				"modelTypeNotNull": "模型类型不允许为空",
				"unitHeightNotNull": "U高不允许为空",
				"unitHeightMinValue": "U高最小值不能小于1",
				"unitHeightMaxValue": "U高最大值不能大于99",
				"invalidInput": "输入的格式不正确",
				"doubleOutofRangeValue": "输入的数值不能大于999999.99",
				"factory": "厂家",
				"brand": "品牌",
				"capacityParameter": "容量参数",
				"modelFile": "3D模型",
				"serialNumber": "序列号",
				"referenced": "设备模型被IT设备引用，不能执行删除操作",
				"devciceNameNotNull": "IT设备名称不允许为空",
				"select3DModel": "选择3D模型"
			},
			"list": {
				"title": "设备模型列表",
				"deleteSuccess": "删除成功!",
				"deleteError": "删除失败!",
				"searchPlaceholder": "请输入检索关键字"
			},
			"add": {
				"title": "添加设备模型",
				"addSuccess": "添加成功!",
				"addError": "添加失败!"
			},
			"edit": {
				"title": "修改设备模型",
				"editSuccess": "修改成功!",
				"editError": "修改失败!"
			},
			"common": {
				"modeTypeNotDelete": "模型类型已使用，不能删除",
				"nameExist": "该名称已存在",
				"addItem": "添加选项"
			}
		},
		"rackDevice": {
			"property": {
				"selectRackDevice": "选择",
				"rackDeviceId": "容量设备ID",
				"rackDeviceName": "IT设备名称",
				"modelId": "关联设备模型",
				"modelName": "设备模型名称",
				"rackId": "关联机柜",
				"assetId": "关联资产",
				"assetName": "资产名称",
				"assetCode": "资产编码",
				"unitHeight": "U高",
				"weight": "重量(KG)",
				"ratePower": "电量(W)",
				"coolingCapacity": "冷量(kJ)",
				"startRackPosition": "起始位置",
				"launchDate": "启用日期",
				"purchaseDate": "购买日期",
				"client": "客户",
				"business": "业务",
				"description": "描述",
				"deviceNameNotNull": "容量设备名称不允许为空",
				"modelIdNotNull": "设备模型不允许为空",
				"rackDeviceNameNotNull": "容量设备名称不允许为空",
				"invalidInput": "输入的格式不正确",
				"doubleOutofRangeValue": "输入的数值不能大于999999.99",
				"launchDateAfterPurchaseDate": "启用时间不能小于购买时间",
				"operation": "操作",
				"offRackSucess": "设备下架操作成功",
				"offRackFailure": "设备下架操作失败",
				"recommendShelfSucess": "设备推荐上架操作成功",
				"recommendShelfFailure": "设备推荐上架操作失败",
				"uindexPoistion": "上架位置",
				"onRackName": "所属机架",
				"updateSuccess": "修改成功",
				"notEnoughSpace": "修改失败,机架空位不足。",
				"updateFail": "修改失败",
				"models": "模型",
				"device": "设备",
				"length": "长(m)",
				"width": "宽(m)",
				"height": "高(m)",
				"state": "状态",
				"power": "额定功率(kW)",
				"serialNumber": "序列号",
				"capacityParameter": "容量参数",
				"brand": "品牌",
				"manufactor": "厂家",
				"model": "型号",
				"devicesFromS6": "从S6资产关联设备",
				"modelInfo": "模型信息",
				"ITDeviceInfo": "IT设备信息",
				"IP": "IP地址"
			},
			"list": {
				"title": "容量设备列表",
				"deleteSuccess": "删除成功!",
				"deleteError": "删除失败!",
				"searchPlaceholder": "请输入检索关键字"
			},
			"add": {
				"title": "添加容量设备",
				"addSuccess": "添加成功!",
				"addError": "添加失败!"
			},
			"edit": {
				"title": "修改容量设备",
				"editSuccess": "修改成功!",
				"editError": "修改失败!"
			}
		},
		"rack": {
			"property": {
				"selectRack": "选择上架机架",
				"rackId": "机架ID",
				"name": "机架名称",
				"model": "模型名称",
				"assetId": "关联资产",
				"assetName": "资产名称",
				"containerPath": "所属位置",
				"roomId": "所属房间",
				"unitHeight": "U高",
				"business": "业务",
				"deviceCount": "上架设备数量",
				"customerName": "客户",
				"powerCapacity": "电量(W)",
				"weightCapacity": "重量(KG)",
				"coolingCapacity": "冷量(W)",
				"supplyMode": "供电方式",
				"purpose": "用途",
				"renderName": "租户",
				"usedTime": "启用时间",
				"symbolVersion": "版本",
				"rfidposition": "RFID位置",
				"rfid": "RFID",
				"serialNumber": "序列号",
				"description": "描述",
				"nameNotNull": "机架名称不允许为空",
				"modelNotNull": "模型不允许为空",
				"unitHeightNotNull": "U高不允许为空",
				"unitHeightMinValue": "U高最小值不能小于1",
				"unitHeightMaxValue": "U高最大值不能大于99",
				"invalidInput": "输入的格式不正确",
				"doubleOutofRangeValue": "输入的数值不能大于999999.99",
				"operation": "操作",
				"spaceUsageRate": "已用空间",
				"powerUsageRate": "已用电量",
				"weightUsageRate": "已用重量",
				"coolingUsageRate": "已用冷量"
			},
			"list": {
				"title": "容量机架列表",
				"deleteSuccess": "删除成功!",
				"deleteError": "删除失败!",
				"searchPlaceholder": "请输入检索关键字"
			},
			"add": {
				"title": "添加容量机架",
				"addSuccess": "添加成功!",
				"addError": "添加失败!"
			},
			"edit": {
				"title": "修改容量机架",
				"editSuccess": "修改成功!",
				"editError": "修改失败!"
			}
		},
		"rackDeviceOnOffTicket": {
			"property": {
				"ticketId": "工单ID",
				"ticketName": "工单名称",
				"rackDeviceOnOffRackSubTickets": "选择容量设备",
				"createTime": "创建时间",
				"startTime": "开始时间",
				"endTime": "结束时间",
				"cancelTime": "取消时间",
				"deadline": "截止时间",
				"state": "工单状态",
				"businessProcess": "业务处理",
				"taskId": "关联任务ID",
				"principalId": "负责人",
				"initiatorId": "创建人",
				"appUrl": "app跳转url",
				"appUrlParams": "app跳转url参数",
				"participants": "参与人",
				"attachments": "附件",
				"description": "描述",
				"startTimeNotNull": "开始时间不可为空",
				"ticketNameNotNull": "工单名称不允许为空",
				"principalRequired": "负责人不能为空"
			},
			"onoff": {
				"title": "容量设备上下架",
				"rackDevice": "容量设备",
				"selectRackDevices": "选择待上下架的容量设备",
				"rackDeviceNotNull": "容量设备不能为空",
				"selectedRackDevices": "已选容量设备",
				"outOfMaxRange": "选择的容量设备数量超出最大限制",
				"bussinessClosed": "工单业务闭环",
				"opeartionSuccess": "工单容量设备上下架操作生效！",
				"optionalRack": "可选机架",
				"submitted": "提交工单",
				"unfinished": "当前工单没有处理完，确认要提交吗？",
				"insufficient": "当前位置连续空间不足，请重新选择起始位置！",
				"onRackSuccess": "上架操作成功",
				"batchOffRack": "批量下架",
				"batchOffRackSuccess": "批量下架成功",
				"batchOffRackError": "批量下架失败",
				"search": "查找",
				"controlSend": "查找命令下发成功",
				"controlFail": "查找命令下发失败",
				"UNKNOWN_ERROR": "未知错误",
				"PARAM_ERROR": "参数错误",
				"COMMAND_EXISTS": "活动控制命令已存在",
				"IN_PROJECT": "局站或设备处于工程状态",
				"NO_PERMISSION": "没有执行控制命令的权限",
				"EQUIPMENT_OFFLINE": "设备不在线",
				"IT_DEVICE_NOT_FOUND": "找不到IT设备",
				"RACK_INFO_NOT_FOUND": "找不到机架信息",
				"U_POSITION_DEVICE_NOT_FOUND": "U位设备不存在",
				"errorReason": "有设备查找失败，错误原因:"
			},
			"list": {
				"title": "上下架工单列表",
				"deleteSuccess": "删除成功!",
				"deleteError": "删除失败!",
				"canDeleteReason": "工单状态处于[已关闭]或[已取消]，无法删除！",
				"searchPlaceholder": "请输入检索关键字",
				"processing": "处理中",
				"ended": "已结束",
				"canceled": "已取消"
			},
			"add": {
				"title": "添加上下架工单",
				"addSuccess": "添加成功!",
				"addError": "添加失败!"
			},
			"edit": {
				"title": "修改上下架工单",
				"editSuccess": "修改成功!",
				"editError": "修改失败!"
			}
		},
		"rackOpenonTicket": {
			"writeWorkOrder": "填写工单",
			"rackOpen": "机柜开通",
			"parentWorkOrder": "上级工单",
			"client": "客户",
			"state": "状态",
			"principal": "负责人",
			"participants": "参与人",
			"initiator": "创建人",
			"rackId": "机柜编号",
			"openType": "开通方式",
			"taskName": "工单名称",
			"electric": "通电",
			"internet": "通网",
			"submitTime": "提交时间",
			"deadline": "截止时间",
			"endTime": "完成时间",
			"cancelTime": "关闭时间",
			"workOrderDetail": "工单详情",
			"parentWorkOrderRequired": "上级工单不能为空",
			"clientRequired": "客户不能为空",
			"executorRequired": "负责人不能为空",
			"rackIdRequired": "至少添加一个机架",
			"openTypeRequired": "请选择开通方式",
			"taskNameRequired": "任务名称不能为空",
			"timeWarning": "截止时间必须大于开始时间",
			"rackTaskEnd": "已关闭",
			"done": "已完成",
			"rackTaskDoing": "处理中",
			"attachments": "附件{{index}}",
			"detail": "详情"
		},
		"complaintManagement": {
			"prototype": {
				"title": "投诉管理",
				"suggestionTitle": "投诉主题",
				"respondent": "投诉人",
				"complainant": "投诉对象",
				"complainantIssue": "投诉内容",
				"createTime": "创建时间",
				"deadline": "截止时间",
				"principal": "负责人",
				"participants": "参与人",
				"contactWay": "联系方式",
				"state": "状态"
			},
			"edit": {
				"title": "编辑",
				"processResult": "处理结果",
				"relatedHiddenDanger": "相关隐患",
				"relatedFault": "相关故障",
				"reason": "理由",
				"taskDetail": "任务详情",
				"descriptionOne": "若需要了解其他人的处理意见，请点击【",
				"descriptionTwo": "】查看！",
				"manage": "处理"
			},
			"add": {
				"title": "新增",
				"addComplain": "新增投诉",
				"titleRequired": "投诉主题不能为空",
				"respondentRequired": "投诉人不能为空",
				"complainantRequired": "投诉对象不能为空",
				"complainantIssueRequired": "投诉内容不能为空",
				"contactError": "联系方式格式有误"
			}
		}
	},
	"dashboards": {
		"setting": "配置dashboards",
		"editWidget": "修改控件配置",
		"deleteWidget": "删除控件",
		"deleteWidgetOrNot": "确认删除该控件？",
		"deleteWidgetSuccess": "删除控件成功",
		"widgetSettings": "控件配置",
		"widgetPanelSetting": "布局配置",
		"widgetOptionSetting": "选项配置",
		"selectWidget": "添加控件",
		"widgetTemplateTitle": "搜索控件名称",
		"addWidget": "选择",
		"mockData": "模拟数据",
		"category": "分类",
		"resources": "资源视图",
		"common": "公共控件",
		"panelLayout": {
			"title": "标题显示",
			"titleName": "标题名称",
			"rows": "占据行数",
			"columns": "占据列数",
			"rowStart": "起始行",
			"columnStart": "起始列",
			"iconShow": "图标显示",
			"iconClassName": "选择图标",
			"rowStartNotNull": "起始行不能为空",
			"rowStartInvalid": "起始行应为1到8的数字",
			"columnStartNotNull": "起始列不能为空",
			"columnStartInvalid": "起始列应为1到12的数字",
			"rowsNotNull": "占据行数不能为空",
			"rowsInvalid": "占据行数应为1到8的数字",
			"columnsNotNull": "占据列数不能为空",
			"columnsInvalid": "占据列数应为1到12的数字",
			"settingUpdateSuccess": "配置更新成功",
			"settingUpdateFail": "配置更新失败",
			"settingAddSuccess": "配置添加成功",
			"settingAddFail": "配置添加失败"
		},
		"pieChart": {
			"dataSource": "数据源",
			"pieType": "饼图类型",
			"circle": "圆",
			"ring": "环",
			"innerRingRadius": "内环半径",
			"outterRingRadius": "外环半径",
			"circleRadius": "饼图半径",
			"showOrHideLegend": "图例显示",
			"positionOfLegend": "图例位置",
			"titleText": "标题显示",
			"positionOfTitle": "标题位置",
			"unitTip": "单位为%",
			"top": "顶部",
			"bottom": "底部",
			"left": "左侧",
			"right": "右侧",
			"center": "居中",
			"navigateTo": "点击跳转",
			"navigateRoute": "跳转路由"
		},
		"entrance": {
			"title": "dashboard列表",
			"titleName": "标题名称",
			"description": "描述",
			"creator": "创建人"
		},
		"lineChart": {
			"showOrHideArea": "面积显示",
			"showOrHideSlider": "滑块显示",
			"showOrHideXLine": "X分割线",
			"showOrHideYLine": "Y分割线",
			"intervalOfColor": "区间颜色",
			"to": "至",
			"greaterThan": "大于",
			"lessThan": "小于"
		}
	},
	"personGroup": {
		"groupType": {
			"title": "组分类",
			"groupTypeAdd": "新增组分类",
			"personCategoryName": "名称",
			"personCategoryNameNotNull": "组分类不能为空",
			"deleteNotAdmit": "当前组分类已经有组记录，不可以删除",
			"editNotAllow": "当前组分类为内置组分类，不允许进行操作",
			"confirmDelete": "请确认是否删除: ",
			"Notrepetition": "组分类名不能重复",
			"operation": "操作"
		},
		"group": {
			"title": "组",
			"groupName": "组名",
			"groupNameNotNull": "组名不能为空",
			"groupCategory": "组分类",
			"groupCategoryNotNull": "组分类不能为空",
			"action": "操作",
			"groupAdd": "新增组",
			"groupAddSuccess": "组新增成功",
			"groupAddFailed": "组新增失败",
			"groupEdit": "编辑组",
			"groupEditSuccess": "组编辑成功",
			"groupEditFailed": "组编辑失败",
			"sure": "确定",
			"cancel": "取消",
			"groupType": "组分类",
			"groupTypeAdd": "新建组分类",
			"groupTypeAddSuccess": "组分类新增成功",
			"groupTypeAddFailed": "组分类新增失败",
			"groupTypeEdit": "编辑组分类",
			"groupTypeEditSuccess": "组分类编辑成功",
			"groupTypeEditFailed": "组分类编辑失败",
			"save": "保存",
			"searchText": "搜索组分类",
			"deleteTips": "请确认是否删除: ",
			"selectedTips": "请先选择组分类",
			"Notrepetition": "组名不能重复",
			"operation": "操作",
			"deleteNotAdmit": "当前组已经有组员记录，不可以删除",
			"editNotAllow": "当前组为内置组，不允许进行操作"
		},
		"person": {
			"title": "新增人员",
			"name": "组员",
			"group": "组",
			"groupPersonAdd": "新增人员",
			"selectedTips": "请先选择组",
			"isOrNotCharge": "是否为负责人",
			"isCharge": "是",
			"NotCharge": "否",
			"operation": "操作",
			"editNotAllow": "值班组下的组员，不允许进行操作"
		}
	},
	"rack": {
		"equipment": {
			"placeholder": "请输入检索关键字",
			"advancedSearch": "高级搜索",
			"customer": "客户",
			"business": "业务",
			"floor": "楼层",
			"rome": "房间",
			"space": "空间",
			"power": "电量",
			"cool": "冷量",
			"weight": "重量",
			"reset": "重置",
			"search": "查询",
			"info": "基本信息",
			"rack": "上架设备",
			"workOrder": "工单流程",
			"position": "位置",
			"workOrderCount": "上架设备数",
			"order": "显示顺序",
			"selectionRange": "选择范围",
			"ratedPower": "额定功率",
			"loadRate": "负载率",
			"contractNumber": "合同编号",
			"contractStartTime": "合同开始日期",
			"contractEndTime": "合同结束日期",
			"contractStatus": "合同状态",
			"businessStatistics": "业务统计",
			"customerStatistics": "租户统计",
			"rackDue": "机架到期情况",
			"contract": "合同",
			"building": "楼栋",
			"noWorkOrder": "没有相关流程"
		}
	},
	"theme": {
		"star": "星空",
		"classic": "经典",
		"sky": "蓝白"
	},
	"menu": {
		"menuPlan": "菜单方案",
		"menuPlanAdd": "新增方案",
		"menuPlanEdit": "方案编辑",
		"planName": "方案名称",
		"scene": "场景",
		"menuPlanPrompt": "方案名称不能为空",
		"usePlan": "使用方案",
		"nameRepeat": "方案名称重复",
		"currentMenuPlan": "当前方案",
		"addParentMenu": "选择父级菜单",
		"menuLineOptions": "菜单列选项",
		"menuLinePrompt": "按住鼠标左键拖至右侧树列表中",
		"menuTreePrompt": "点击鼠标右键进行操作",
		"treeList": "树列表 (配置完成后请重新登录使配置生效)",
		"workbench": "工作台",
		"createMenu": "新建菜单链接",
		"addMenuItem": "添加菜单项",
		"menuName": "名称",
		"menuENName": "英文名",
		"menuIcon": "图标",
		"asTabShow": "作为Tab页展示",
		"showInIfame": "嵌入内容区展示",
		"selectedMenu": "选择菜单",
		"expandedMenu": "展开菜单",
		"hiddenMenu": "隐藏菜单",
		"isLink": "是否为外部链接",
		"linkAddress": "路径",
		"maxLength": "名称不超过15个字符",
		"invalidName": "名称包含非法字符！",
		"required": "必填",
		"createFolder": "新建目录",
		"rename": "重命名",
		"edit": "编辑",
		"remove": "移除",
		"description": "备注",
		"confirmDelete": "请确认是否删除: ",
		"deletePrompt": "请先删除子菜单！",
		"movedInvalid": "拖动无效"
	},
	"unit": {
		"title": "单位",
		"unitName": "单位名称",
		"unitNamePrompt": "单位名称不能为空",
		"unitSymbol": "单位符号",
		"unitSymbolPrompt": "单位符号不能为空",
		"unitSymbolName": "单位中文",
		"unitSymbolNameEn": "单位英文",
		"unitNameCode": "单位代码",
		"description": "说明"
	},
	"cron": {
		"title": "周期",
		"cronName": "名称",
		"cronNamePrompt": "名称不能为空",
		"cronNameEn": "英文名称",
		"cronContent": "表达式",
		"cronContentPrompt": "表达式不能为空"
	},
	"idcManage": {
		"common": {
			"name": "名称",
			"nameNotNull": "名称不能为空",
			"nameNotRepeat": "名称不能重复",
			"photo": "图片",
			"photoName": "图片名称",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"photoNameNotNull": "图片名称不能为空",
			"remark": "备注",
			"enableTime": "系统启用时间",
			"select": "选择",
			"clear": "清空",
			"download": "下载",
			"longitudeMinError": "经度不能小于-180",
			"longitudeMaxError": "经度不能大于180",
			"latitudeMinError": "纬度不能小于-90",
			"latitudeMaxError": "纬度不能大于90",
			"info": "基本信息",
			"complexIndex": "指标",
			"diagram": "组态",
			"forewarming": "预警",
			"weight": "重量",
			"maintenanceTeam": "维护团队",
			"manager": "管理员",
			"uploadPhotoTips": "请先上传图片",
			"configurationShow": "组态显示",
			"isShow": "是",
			"notShow": "否",
			"asTreeRoot": "设置为根节点",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消",
			"corePoint": "测点",
			"confirmDeleteIndicator": "请确认是否删除指标：",
			"signalColor": "信号颜色配置",
			"branchSetting": "支路配置",
			"dynamicCurve": "实时曲线",
			"realtimeChart": "实时图表",
			"editExpression": "编辑表达式",
			"notNull": "不能为空"
		},
		"capacity": {
			"resourceList": "资源列表",
			"device": "设备",
			"rack": "机架",
			"currentSelected": "当前选中：",
			"noData": "没有找到数据",
			"itdevice": "IT设备",
			"baseType": "容量基类",
			"baseAttribute": "基类属性",
			"attributeName": "属性名称",
			"capacityAttribute": "容量属性",
			"logicType": "计算方式",
			"passiveUpdate": "被动更新",
			"complexIndex": "指标计算",
			"expression": "配置公式",
			"fromCorePoint": "从测点",
			"fromComplexIndex": "从指标",
			"complexIndexSetting": "指标配置",
			"ratedCapacity": "额定容量",
			"compensateFactor": "补偿因子",
			"minValue": "最小值",
			"maxValue": "最大值",
			"defaultValue": "默认值",
			"unit": "单位",
			"precision": "精度",
			"description": "简介",
			"submit": "确定",
			"cancel": "取消",
			"reset": "还原",
			"operation": "操作",
			"configureComplexIndex": "配置指标",
			"attributeOptions": "属性选项",
			"createAttributes": "创建属性",
			"batchCreation": "批量创建",
			"remark": "重要提示",
			"current": "当前对象",
			"sameLevel": "同类型对象",
			"childLevel": "及所有子级",
			"currentTip": "给当前资源创建容量属性",
			"sameLevelTip": "给同类型资源批量创建容量属性",
			"childLevelTip": "给当前资源以及子资源批量创建容量属性",
			"associateCorePoint": "关联测点",
			"associateComplexIndex": "关联指标",
			"associateCapacity": "关联容量",
			"modalTip": "请选择需要批量应用的字段",
			"recordTip1": "如果系统中已存在容量属性则返回成功原有的容量属性不改变且不重新执行创建指令（不会破坏现有的容量属性）。",
			"recordTip2": "属性名称：由容量基类属性决定，不可修改。 ",
			"recordTip3": "计算方式：由容量基类默认指定可手动选择，当使用指标时，创建容量属性会同时创建指标。",
			"recordTip4": "计算方式：由容量基类默认指定，可手动选择。",
			"deviceTip": "显示设备",
			"rackTip": "显示机架",
			"itDeviceTip": "显示IT设备时会自动显示机架",
			"creationTip1": "容量基类，不同的基类定义了不同的容量属性。",
			"creationTip2": "容量的基类属性",
			"creationTip3": "容量属性的字段名，不可修改，由容量基类属性决定。",
			"creationTip4": "被动计算只能等推送数据，指标计算可以计算指标表达式。",
			"creationTip5": "容量计算的额定容量值。",
			"creationTip6": "原始输入数据将于补偿因子组合。",
			"creationTip7": "原始输入数据的的最小输入值限制，超过此值时将视为无效数据。",
			"creationTip8": "原始输入数据的的最大输入值限制，超过此值时将视为无效数据。",
			"creationTip9": "容量数据所有阶段的计算精度。",
			"creationTip10": "容量数据的单位 默认为：%",
			"creationTip11": "当输入原始数据无效时，使用默认原始数据，默认为空",
			"creationTip12": "普通的描述，记录点不一样的东西",
			"indexTips": "该属性含有指标公式，删除属性时将会删除指标",
			"deleteTips": "正在删除{{0}}属性，{{1}}该操作不可恢复请谨慎操作。"
		},
		"hierarchy": {
			"title": "层级管理",
			"addTips": "当前节点下的新增子节点名称不能重复",
			"createSuccess": "新建成功",
			"createFailed": "新建失败",
			"deleteSuccess": "删除成功",
			"deleteTips": "当前层级下有设备，确认是否一起删除？",
			"indicatorEdit": {
				"indicatorName": "指标名称",
				"businessId": "业务分类",
				"globalResourceName": "指标对象",
				"businessIdNotNull": "业务分类不能为空",
				"countPeriod": "计算周期",
				"savePeriod": "存储周期",
				"unit": "单位",
				"precision": "精度",
				"description": "描述",
				"formula": "表达式",
				"fxFormula": "函数公式",
				"value": "数值",
				"addValue": "新增数值",
				"generateFormula": "插入表达式",
				"indicator": "指标",
				"points": "测点",
				"deviceTestpointSelect": "设备测点选择",
				"indicatorSelect": "指标选择",
				"classification": "分类",
				"center": "监控中心",
				"park": "园区",
				"building": "楼栋",
				"floor": "楼层",
				"room": "房间",
				"indicatorId": "指标ID",
				"insetPrompt": "请选择函数并完善计算项",
				"incorrect": "表达式配置有误",
				"formulaPrompt": "备注：可在此手动更改表达式",
				"formulaOperation": "操作符",
				"addIndicator": "添加指标",
				"addCorePoint": "添加测点",
				"addMath": "插入函数",
				"iscalctype": "使用差值计算",
				"corePointId": "信号ID",
				"corePointName": "信号名称",
				"indicatorTemplateName": "指标模板名称",
				"indicatorTemplate": "指标模板",
				"batchIndicator": "批量生成指标",
				"indicatorDefinition": "指标定义",
				"indicatorQuality": "质量表达式",
				"indicatorCalcOrder": "指标计算顺序",
				"current": "当前",
				"sameLevel": "同层级",
				"childLevel": "子级",
				"currentTip": "给当前资源添加指标",
				"sameLevelTip": "给同层级资源批量生成指标",
				"childLevelTip": "给当前资源以及子资源批量生成指标"
			}
		},
		"deviceManagement": {
			"title": "设备管理",
			"deviceList": "房间列表",
			"name": "名称",
			"basicType": "基础类型",
			"clientType": "自定义类型",
			"function": "功能",
			"searchBox": "设备名称",
			"searchContent": "搜索",
			"add": "设备新增",
			"edit": "设备编辑",
			"delete": "设备删除",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"selectBasicTypeTipsNotNull": "基础类型不能为空",
			"description": "描述",
			"devicebook": "机历簿",
			"allSelect": "全选",
			"deleteInBatches": "批量删除",
			"deleteInBatchesTips": "请确认是否删除",
			"PrintInBatches": "打印批量二维码",
			"PrintInAll": "打印全部二维码",
			"customeTypeName": "类型名称",
			"customeTypeNameNotRepeat": "类型名称不能重复",
			"customerTypeList": "自定义类型列表",
			"remark": "备注",
			"previous": "上一个",
			"next": "下一个",
			"addFromFSU": "从采集单元新增",
			"addFromExcel": "从excel导入",
			"FsuList": "采集单元列表",
			"gateWaysTitle": "采集器",
			"acquisitionUnit": "采集单元",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消",
			"addDeviceSuccess": "采集单元成功生成设备",
			"branchId": "支路",
			"branchName": "支路名称",
			"deviceId": "设备ID",
			"corepointId": "信号ID",
			"cabinetNo": "机柜编号",
			"branchNo": "支路编号",
			"deviceName": "设备名称",
			"deviceType": "设备类型",
			"roomName": "房间名称",
			"batchChanging": "批量修改中",
			"batchApply": "批量应用",
			"lastBatchApplyList": "当前组态页最近一次批量应用设备列表",
			"selectContent": "选择内容",
			"selectField": "选择字段",
			"selectType": "选择类型"
		},
		"monitor": {
			"title": "监控",
			"name": "监控单元"
		},
		"deviceMaintenanceInfo": {
			"deviceName": "设备名称",
			"principal": "负责人",
			"instrument": "工具",
			"direction": "图纸及说明书",
			"securityManual": "安全手册",
			"pretectionTools": "防护工具",
			"ownerInCharge": "业主负责人",
			"agentInCharge": "代维负责人",
			"supplierLeader": "供应商负责人",
			"deviceNameNotNull": "设备名称不能为空",
			"uploadSuccess": "文件上传成功",
			"uploadError": "文件上传失败",
			"illegalFormat": "非法的文件格式",
			"uploadTips": "请先上传文件",
			"uploadFiles": "文件上传"
		},
		"graphicPage": {
			"templateList": "模板列表",
			"submit": "确定",
			"multiple": "批量应用",
			"graphicPageName": "组态名称",
			"defaultGraphic": "默认组态",
			"graphicPageNameRequired": "组态名称不能为空",
			"graphicPageTemp": "组态模板",
			"deleteConfirm": "确认删除",
			"createForTemplate": "从模板新建",
			"createForNull": "新建空页面",
			"cancel": "取消"
		},
		"deviceMonitor": {
			"tabMenu": {
				"device": "设备",
				"signal": "信号",
				"alarm": "告警",
				"control": "控制"
			},
			"signal": {
				"name": "名称",
				"showIndex": "显示顺序",
				"signalId": "信号Id",
				"baseSignal": "基类信号",
				"classification": "分类",
				"aisleNo": "通道号",
				"aisleType": "通道类型",
				"formula": "表达式",
				"precision": "精度",
				"unit": "单位",
				"timePeriod": "时间周期",
				"show": "可见",
				"stateMeaning": "状态信号含义",
				"analogQuantity": "模拟量",
				"digitalQuantity": "数字量",
				"acquireSignal": "采集信号",
				"virtualSignal": "虚拟信号",
				"constantSignal": "常量信号",
				"delete": "删除",
				"value": "值",
				"means": "含义",
				"setSignalMeans": "信号含义设置",
				"have": "有",
				"no": "无",
				"copy": "复制",
				"valueNull": "值不能重复",
				"storeInterval": "存储周期（秒）",
				"absValueThreshold": "绝对值阈值",
				"percentThreshold": "百分比阈值",
				"staticsPeriod": "统计周期（小时）",
				"signalProperties": "信号属性"
			},
			"alarm": {
				"name": "名称",
				"showIndex": "显示顺序",
				"alarmId": "告警Id",
				"eventType": "事件类别",
				"condition": "条件",
				"beginExpression": "开始表达式",
				"relatedSignal": "关联信号",
				"show": "可见",
				"have": "有",
				"baseAlarm": "基类告警",
				"eventSeverity": "告警等级",
				"startOperation": "开始操作符",
				"startCompareValue": "开始比较值",
				"startDelay": "开始延时",
				"meanings": "含义",
				"setAlarmCondition": "告警条件设置",
				"shieldAlarm": "屏蔽告警",
				"alarmCount": "告警数量",
				"alarmStatus": "告警状态",
				"deviceCount": "设备数量",
				"normalStatus": "运行正常"
			},
			"control": {
				"name": "名称",
				"showIndex": "显示顺序",
				"controlId": "控制id",
				"baseControl": "基类控制",
				"controlType": "控制命令类型",
				"commandCategory": "命令分类",
				"remoteControl": "遥控",
				"remoteAdjustment": "遥调",
				"paramMax": "参数上限",
				"paramMin": "参数下限",
				"relatedSignal": "关联信号",
				"paramMean": "参数含义",
				"defaultValue": "默认值",
				"show": "可见",
				"setControlMeaning": "控制参数含义设置"
			},
			"setting": {
				"signalsName": "基类",
				"meaningValue": "阀值",
				"meanJudgeMode": "阀值判断方式",
				"showOfColor": "显示颜色",
				"enable": "启用",
				"notEnable": "不启用",
				"selectBaseTypes": "选择基类（支持多选）",
				"tip": {
					"emptyOfSignals": "基类不能为空！"
				}
			}
		},
		"powerDevice": {
			"name": "名称",
			"objectType": "对象类型",
			"ratedPower": "额定功率",
			"ratedCurrent": "额定电流",
			"firstRange": "一级预警阀值",
			"secondRange": "二级预警阀值",
			"thirdRange": "三级预警阀值",
			"fourthRange": "四级预警阀值",
			"powerSupplyMode": "供电方式",
			"selectType": "选择类型",
			"upStream": "上游",
			"downStream": "下游",
			"selectDevice": "选择设备",
			"selectRack": "选择机架"
		},
		"center": {
			"centerId": "系统ID",
			"timeServerAddress": "校时服务器地址",
			"introduction": "中心介绍",
			"createSuccess": "园区新建成功",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消"
		},
		"park": {
			"parkId": "园区ID",
			"description": "园区介绍",
			"parkAddress": "园区地址",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消"
		},
		"building": {
			"buildingName": "大楼名称",
			"description": "大楼介绍",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消"
		},
		"floor": {
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消",
			"description": "楼层介绍"
		},
		"room": {
			"roomName": "房间名称",
			"description": "房间介绍",
			"physicalTag": "物理标签",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消"
		},
		"mdc": {
			"mDCName": "MDC名称",
			"description": "MDC介绍",
			"manufactoy": "厂家",
			"model": "型号",
			"columnNumber": "列数",
			"columnNumberError": "请输入正整数",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消"
		},
		"indexAllocation": {
			"title": "指标名称",
			"businessType": "业务分类",
			"operation": "操作",
			"expression": "是否完成配置",
			"isExpression": "是",
			"NotExpression": "否"
		},
		"gateWays": {
			"title": "采集器",
			"refresh": "刷新",
			"name": "名称",
			"UUID": "UUID",
			"collector": "采集协议",
			"IP": "IP",
			"accessOrNotAcess": "是否接入",
			"asscesSuccess": "接入成功",
			"notAccessSuccess": "接出成功",
			"access": "接入",
			"notAccess": "接出",
			"isJoin": "是",
			"notJoin": "否",
			"notSelectBasetype": "未选择基础类型"
		},
		"menu": {
			"menuPlan": "菜单方案",
			"menuPlanAdd": "新增方案",
			"menuPlanEdit": "方案编辑",
			"planName": "方案名称",
			"menuPlanPrompt": "方案名称不能为空",
			"usePlan": "使用方案",
			"nameRepeat": "方案名称重复",
			"currentMenuPlan": "当前方案",
			"addParentMenu": "选择父级菜单",
			"menuLineOptions": "菜单列选项",
			"menuLinePrompt": "按住鼠标左键拖至右侧树列表中",
			"menuTreePrompt": "点击鼠标右键进行操作",
			"treeList": "树列表 (配置完成后请重新登录使配置生效)",
			"workbench": "工作台",
			"createMenu": "新建菜单链接",
			"addMenuItem": "添加菜单项",
			"menuName": "名称",
			"menuIcon": "图标",
			"asTabShow": "作为Tab页展示",
			"selectedMenu": "选择菜单",
			"expandedMenu": "展开菜单",
			"hiddenMenu": "隐藏菜单",
			"isLink": "是否为外部链接",
			"linkAddress": "路径",
			"maxLength": "名称不超过15个字符",
			"required": "必填",
			"createFolder": "新建目录",
			"rename": "重命名",
			"edit": "编辑",
			"remove": "移除",
			"description": "备注",
			"confirmDelete": "请确认是否删除: ",
			"deletePrompt": "请先删除子菜单！",
			"movedInvalid": "拖动无效"
		},
		"unit": {
			"title": "单位",
			"unitName": "单位名称",
			"unitNamePrompt": "单位名称不能为空",
			"unitSymbol": "单位符号",
			"unitSymbolPrompt": "单位符号不能为空",
			"unitSymbolName": "单位中文",
			"unitSymbolNameEn": "单位英文",
			"unitNameCode": "单位代码",
			"description": "说明"
		},
		"cron": {
			"title": "周期",
			"cronName": "名称",
			"cronNamePrompt": "名称不能为空",
			"cronNameEn": "英文名称",
			"cronContent": "表达式",
			"cronContentPrompt": "表达式不能为空"
		},
		"asset": {
			"title": "资产"
		},
		"generalObject": {
			"title": "通用对象",
			"commonObjectName": "名称",
			"uploadSuccess": "图片上传成功",
			"uploadError": "图片上传失败",
			"illegalFormat": "非法的文件格式",
			"fileExits": "文件已存在",
			"cover": "覆盖",
			"uploadCancel": "取消",
			"addGeneralObject": "通用对象新增",
			"deleteConfirm": "删除该对象将一并删除其下的组态与指标，是否继续？"
		},
		"rackManage": {
			"noUbPleaseCheck": "该机架未绑定U位,请检查配置",
			"unbindMsg_1": "请确认是否解除:【{0}】",
			"unbindMsg_2": "和【{0}】的绑定",
			"bindSuccess": "绑定成功",
			"rackLocation": "机架定位",
			"threeDNotExistRack": "3D场景不存在该机架",
			"unbindSuccess": "解除绑定成功",
			"computerRackName": "机架名称",
			"computerRackNameRequired": "机架名称不能为空",
			"computerRackNumber": "机架编号",
			"computerRackNumberReq": "机架编号不能为空",
			"parkName": "IDC名称",
			"parkNameRequired": "IDC名称不能为空",
			"buildingName": "楼栋名称",
			"buildingNameRequired": "楼栋名称不能为空",
			"floorName": "楼层名称",
			"floorNameRequired": "楼层名称不能为空",
			"roomName": "房间名称",
			"roomNameRequired": "房间名称不能为空",
			"customerName": "所属客户",
			"business": "所属业务",
			"ubitManagement": "U位管理器",
			"electricPowerConsumption": "用电量",
			"uindexPercent": "U位使用率",
			"resourcePosition": "资源位置",
			"checkBox": "CheckBox",
			"itEquipment": "IT设备名称",
			"state": "状态",
			"changeTime": "变更时间",
			"rackState": "机架状态",
			"ubit": "U位",
			"unitHeight": "U高",
			"description": "描述",
			"startTime": "启用时间",
			"Unassigned": "未分配",
			"notOpen": "未开通",
			"opened": "已开通",
			"rackList": "机架列表",
			"importSuccess": "excel导入成功",
			"importFailed": "excel导入出错",
			"downloadTempalte": "下载模板",
			"rackUsePrompt": "该机架被IT设备使用",
			"rackBindPrompt": "该机架已绑定U位管理器",
			"firstThreshold": "一级阈值",
			"secondThreshold": "二级阈值",
			"thirdThreshold": "三级阈值",
			"fourthThreshold": "四级阈值",
			"thresholdRequired": "阈值不能为空",
			"importRack": "导入机架",
			"rackOpen": "机架开通",
			"unitHeightRequired": "U高不能为空",
			"coolingCapacity": "冷量",
			"coolingCapacityRequired": "冷量不能为空",
			"weight": "重量",
			"weightRequired": "重量不能为空",
			"indicatorConfigStatus": "指标配置状态",
			"position": "位置",
			"deleteSuccess": "删除成功!",
			"basicInfo": "基本信息",
			"capacityAttribute": "容量属性",
			"selectUBit": "选择U位",
			"capacityBasetype": "容量基类",
			"baseTypeAttributeName": "基类属性名称",
			"selected": "已选",
			"unselected": "未选择",
			"rackTemplate": "机架模板",
			"createRack": "新增机架",
			"batchUpdateCustomer": "批量设置客户",
			"batchUpdatePosition": "批量设置位置",
			"bindDevice": "绑定设备",
			"unbind": "解除绑定",
			"batchUnbind": "批量解除绑定",
			"unbindMsg": "请确认是否解除绑定？",
			"customer": "客户",
			"roomNamePosition": "机架位置",
			"remark": "说明",
			"powerSource": "功率数据源",
			"ratedUHeight": "机架U位(U)",
			"ratedPower": "机架功率(kW)",
			"ratedCooling": "机架冷量(kJ)",
			"ratedWeight": "机架承重(kg)",
			"roomNamePositionReq": "机架位置不能为空",
			"ratedUHeightReq": "机架U位(U)不能为空",
			"ratedPowerReq": "机架功率(kW)不能为空",
			"ratedCoolingReq": "机架冷量(kJ)不能为空",
			"ratedWeightReq": "机架承重(kg)不能为空",
			"powerSourceReq": "功率数据源不能为空",
			"putOnRack": "上架",
			"takeOffRack": "下架",
			"notOnRack": "未上架",
			"hasOnRack": "已上架",
			"deviceAlreadyOnRack": "该设备已上架",
			"deviceAlreadyOffRack": "该设备已下架",
			"confirmTakeOffRack": "确认下架吗？",
			"remainingRackSpace": "剩余u位",
			"remainingComPower": "剩余电量",
			"remainingRackCooling": "剩余冷量",
			"remainingRackWeight": "剩余承重",
			"emptyUIndex": "空闲u位位置",
			"ratedRackSpace": "u高",
			"ratedComPower": "额定电量",
			"ratedRackCooling": "额定冷量",
			"ratedRackWeight": "额定承重",
			"exportRecommendRack": "导出推荐机架列表(该位置)",
			"recommendRackList": "推荐机架列表",
			"selectedUPoi": "已选择上架位置",
			"selectedUPoiNoSpace": "选择上架位置空间不足",
			"airExchangeOccupied": "选中位置占用了换气U位,位置: ",
			"ITDEVICE": "使用已上架IT设备功率",
			"COMPLEXINDEX": "使用指标配置机架功率",
			"exportRecommendRackAll": "导出推荐机架列表(全部)",
			"deviceNoUHeight": "设备无u高，无法上架",
			"pleaseSelectRack": "请在中间栏选择机架",
			"pleaseSelectUPoi": "请在右侧栏选择设备上架位置",
			"cannotDelteOnRackDevice": "该设备已上架，需先下架才能删除",
			"useRate": "使用率",
			"allUbit": "全部U位",
			"ITDeviceType": "IT设备类型",
			"ITDeviceServiceAttributes": "IT设备业务属性",
			"onRackInfo": "上架详情",
			"ITDeviceModelTemplate": "IT设备模型模板",
			"rackSpace": "U位使用容量",
			"rackWeight": "结构承重容量",
			"rackCooling": "制冷冷却容量",
			"comPower": "功率负载容量",
			"batchPropsTitle": "批量设置属性",
			"propTitle": "属性设置",
			"selectHouse": "选择建筑",
			"powerLoadRate": "市电进线负载率",
			"powerUseRate": "机房IT电力利用率排名TOP",
			"coldLoadRate": "机房制冷负载率排名TOP",
			"rackUseRate": "机房机柜开通率排名TOP",
			"year": "年",
			"month": "月",
			"quarter": "季度",
			"self": "自定义",
			"powerTotalRate": "总电力利用率",
			"ratedCapacity": "额定容量",
			"currentCapacity": "当前容量",
			"freeCapacity": "空闲容量",
			"itPowerRate": "IT电力利用率",
			"ratedItPower": "额定功率",
			"currentItPower": "当前功率",
			"freeItPower": "空闲功率",
			"coolLoadRate": "制冷负荷利用率",
			"rackUsedRate": "机柜开通率",
			"rackTotal": "机柜总数",
			"rackUsed": "使用中",
			"rackFree": "未使用"
		},
		"rackbranch": {
			"rackName": "机架名称",
			"deviceNo": "设备编号",
			"columnCabinetName": "列头柜名称",
			"columnCabinetNo": "列头柜编号",
			"switchName": "支路空开",
			"switchAlias": "支路空开别名",
			"downloadTemplate": "下载模板",
			"import": "导入",
			"title": "机架支路",
			"RackBranchTemplate": "机架支路模板"
		},
		"namebranch": {
			"branchName": "支路名称",
			"branchId": "支路编号",
			"equipmentName": "设备名称",
			"equipmentId": "设备编号",
			"downloadTemplate": "下载模板",
			"Excelimport": "Excel导入",
			"Excelexport": "全部导出",
			"title": "支路名称配置",
			"importSuccess": "导入成功",
			"importFailed": "导入失败",
			"deleteSuccess": "删除成功",
			"submit": "确认",
			"cancel": "取消"
		},
		"forewarnManage": {
			"objectName": "对象名称",
			"earlyWarnName": "预警名称",
			"complexIndex": "指标",
			"warningThreshold": "预警阀值",
			"warningJudgmentWay": "预警判断方式",
			"earlyWarningCategoryName": "预警类型",
			"roomName": "房间名称",
			"earlyWarnTypeName": "预警模块",
			"tip": {
				"earlyWarnNameNotNull": "预警名称不能为空！",
				"warningThresholdNotNull": "预警阀值不能为空！",
				"warningThresholdFormatError": "预警阀值只能为数字！"
			}
		},
		"rackSignal": {
			"rackName": "机架名称",
			"activationRate": "开通率",
			"template": "机柜信号配置模板",
			"powerRate": "功率",
			"activationRateExpression": "开通率表达式",
			"activationRateDescription": "开通率描述",
			"powerExpression": "功率表达式",
			"powerDescription": "功率描述",
			"complete": "完成",
			"updateSuccess": "更新成功",
			"updateFailed": "更新失败"
		},
		"itCabinet": {
			"searchPlaceholder": "请输入关键字搜索",
			"powerLoadRate": "电力负载率",
			"ratedPower": "额定功率",
			"currentPower": "当前有功功率",
			"uPositionRate": "U位使用率",
			"powerStats": "功率统计",
			"powerTrend": "机柜功率变化趋势",
			"loadRateTrend": "负载率趋势",
			"maxPower": "最大功率",
			"minPower": "最小功率",
			"totalPower": "总功率"
		}
	},
	"himManage": {
		"common": {
			"defaultGraphicTip": "不支持该设备类型的组态"
		},
		"template": {
			"settings": {
				"historyCurveVal": {
					"title": "标题",
					"queryDays": "查询时长",
					"showLegend": "显示Legend图例",
					"yAxisCoordinate": "Y轴坐标",
					"dynamicMaxMinValue": "自动设置",
					"defaultMaxMinValue": "手动设置",
					"coordinateMaxValue": "最大值",
					"coordinateMinValue": "最小值",
					"selectCurveType": "选择折线",
					"sampleCurve": "简单折线图",
					"visualMapCurve": "区域折线图",
					"addMarkLine": "添加标线",
					"markLine": "标线",
					"markLineValue": "阀值",
					"addVisualMap": "添加值域",
					"visualMapValue": "值域",
					"valueGt": "数值>",
					"valueLte": "并且≤",
					"setCurveColor": "设置曲线颜色",
					"latestWeek": "近一周数据",
					"latestMouth": "近一个月数据",
					"isShowPanel": "显示容器",
					"intervalType": "区间类型",
					"intervalAll": "全部",
					"intervalDay": "天",
					"selectTimeSpan": "时间段",
					"latestSixHours": "近6小时",
					"latestOneHours": "近1小时",
					"latestOneDay": "近一天",
					"customTime": "自定义",
					"today": "今天"
				},
				"customSignalVal": {
					"selectRestFul": "选择RestFul API:"
				},
				"multiBranchTableVal": {
					"placeholder": "请选择分路基类信号/告警",
					"baseSignal": "基类信号",
					"baseEvent": "基类告警",
					"mainBranchSetting": "主路设置",
					"partBranchSetting": "分路设置",
					"mainBranchSwitchStatus": "设置主路开关状态：",
					"partBranchSwitchStatus": "设置分路开关状态：",
					"mainBranchSwitchBaseType": "设置主路开关状态基类：",
					"partBranchSwitchBaseType": "设置分路开关状态基类：",
					"mainBranchBaseType": "选择主路基类：",
					"partBranchBaseType": "选择分路基类："
				},
				"iconVal": {
					"title": "图片字体"
				},
				"imgVal": {
					"title": "图片",
					"UPS": "UPS",
					"airCondition": "空调",
					"battery": "蓄电池",
					"ATS": "ATS",
					"electricCloset": "配电柜",
					"rectifier": "开关电源",
					"fuelGenerator": "燃油发电机",
					"WAC": "中央空调(水冷)",
					"Ventilator": "新风系统"
				},
				"canvasGaugesVal": {
					"maxValue": "最大值",
					"minValue": "最小值",
					"hiddenGaugeTitle": "隐藏标题",
					"hiddenGaugeValue": "隐藏读数",
					"hideGaugeTicks": "隐藏标尺",
					"hideGaugeNumber": "隐藏数字",
					"hideGaugeNeedle": "隐藏指针",
					"hideGaugeBorder": "隐藏边框",
					"ticksPadding": "标尺间距",
					"barWidth": "柱状宽度",
					"barOrientation": "显示方向",
					"horizontal": "横向",
					"vertical": "纵向",
					"tickSide": "标尺位置",
					"default": "默认",
					"left": "左",
					"right": "右",
					"colorBar": "柱状底色",
					"colorBarProgress": "柱状值色",
					"colorTicks": "标尺颜色",
					"colorNumbers": "数字颜色",
					"colorPlate": "背景色"
				},
				"statePicVal": {
					"defaultPic": "默认图",
					"state": "状态",
					"add": "添加",
					"stateCondition": "状态条件：",
					"checkSignal": "选择信号",
					"statePic": "状态图片：",
					"stateValue": "状态值："
				},
				"signalsAlarmVal": {
					"groupName": "组名：",
					"showType": "显示方式：",
					"pic": "图片",
					"color": "颜色",
					"icon": "图标",
					"checkPic": "选择图片：",
					"checkSignal": "选择信号",
					"haveAlarm": "有告警",
					"noAlarm": "无告警",
					"close": "关闭",
					"checkDevice": "选择设备",
					"checkBaseSignal": "选择基类"
				},
				"devicesAlarmVal": {
					"groupName": "组名：",
					"checkDevice": "选择设备",
					"activateAlarm": "实时",
					"historyAlarm": "历史"
				},
				"indicatorVal": {
					"title": "标题",
					"setDefaultValue": "默认值",
					"selectIndicator": "选择指标",
					"selectMainIndicator": "选择主示指标",
					"selectOtherIndicator": "选择辅示指标",
					"selectVerticalAlign": "垂直对齐方式"
				},
				"safetyDaysVal": {
					"setDefaultStartupTime": "设置系统启动时间",
					"placeHolder": "yyyy-MM-dd",
					"dateFormatInvalid": "日期格式错误"
				},
				"gaugeDigitalVal": {
					"title": "标题",
					"setDefaultValue": "使用默认值",
					"setPercent": "百分比(%)",
					"setValue": "数值",
					"selectCustomAPI": "API接口",
					"setPieChartColor": "设置圆环颜色"
				},
				"indexComparisonVal": {
					"chartType": "图表类型",
					"stack": "堆叠图",
					"bar": "柱状图",
					"ring": "圆环",
					"pie": "饼图",
					"table": "表格",
					"innerRadius": "环大小",
					"showLabel": "显示百分比值",
					"labelSize": "百分比字体大小",
					"selectLegendPosition": "图例位置",
					"showAxisx": "显示轴",
					"isRealTime": "实时数据",
					"isTableHeader": "显示表头",
					"isCellBorder": "显示单元格",
					"progressColor": "进度条颜色",
					"timeSpan": "间隔时段",
					"top": "顶部",
					"bottom": "底部",
					"left": "左侧",
					"right": "右侧"
				},
				"indicator-table": {
					"basic": "基本",
					"title": "标题",
					"setRow": "设置行",
					"setColumn": "设置列",
					"columnName": "列名",
					"showType": "显示方式",
					"text": "文本",
					"signalvalue": "信号值",
					"indicatorvalue": "指标值",
					"progress": "进度指标",
					"progressTitle": "带标题进度指标",
					"addrow": "添加行",
					"setindicatorvalue": "设置指标值",
					"objecttype": "对象类型",
					"area": "选择对象"
				}
			},
			"historyCurve": {
				"title": "历史曲线",
				"sampleTime": "采集时间",
				"sampleValue": "采集值",
				"selectSignalBaseType": "已选择基类信号",
				"multiBranch": "查询支路信号",
				"del": "删除"
			},
			"customTable": {
				"selectSignalBaseType": "已选择基类信号/基类告警：",
				"id": "编号",
				"noAlarm": "正常",
				"branchName": "支路名称"
			},
			"statePic": {
				"bindSignal": "绑定信号:"
			},
			"2dImgUpload": {
				"uploadSuccess": "上传成功",
				"uploadError": "图片上传失败",
				"imageOrigin": "图片源",
				"device": "设备",
				"configuration": "组态",
				"upload": "上传",
				"cancel": "取消",
				"uploadPrompt": "请绑定具体业务",
				"coverConfirm": "图片重复，是否覆盖？",
				"edit": "编辑",
				"cancelEdit": "退出编辑"
			},
			"alarmList": {
				"deviceHasAlarmTip": "本设备有",
				"deviceHasAlarmCount": "条告警:"
			},
			"alarmTable": {
				"noData": "没有数据",
				"ID": "编号",
				"deviceName": "设备名称",
				"alarmName": "告警",
				"alarmLevel": "等级",
				"value": "值",
				"status": "状态",
				"startTime": "时间",
				"historyAlarmList": "历史告警列表"
			},
			"authentication": {
				"certification": "操作认证",
				"user": "用户",
				"password": "密码",
				"inputPassword": "请输入密码"
			},
			"devicesAlarm": {
				"bindDevice": "绑定设备：",
				"lookAlarmList": "查看告警",
				"lookConfiguration": "查看组态",
				"alarm": "告警",
				"deviceList": "设备列表："
			},
			"safetyDays": {
				"dayUnit": "天"
			},
			"deviceMatrixs": {
				"title": "设备告警矩阵"
			},
			"gaugeIndicator": {
				"percent": "数值百分比"
			},
			"barChartIndicator": {
				"title": "告警统计"
			}
		},
		"cameraComponentSetForm": {
			"useTip": "若网页没有反应，请先安装视频客户端！"
		},
		"alarmDurationTimeCountChartSetForm": {
			"title": "告警时长分析",
			"legend": {
				"lessThan10Mins": "小于10分钟",
				"10to30Mins": "10到30分钟",
				"30to60Mins": "30到60分钟",
				"moreThan60Mins": "大于60分钟"
			}
		},
		"alarmOverviewVal": {
			"showAlarmStatistical": "显示告警总数统计",
			"dataSource": "数据源",
			"showAll": "显示全部",
			"showAccordingToSystemConfig": "根据系统参数过滤",
			"displayDetail": "显示详情",
			"alarmStatiscal": "告警统计",
			"alarmDetail": "告警列表",
			"enable": "启用",
			"showMethod": "告警展示方式",
			"circleChart": "圆环图表",
			"hideAlarmText": "隐藏告警等级文字",
			"hide": "隐藏"
		},
		"alarmOverview": {
			"level": "等级",
			"roomName": "房间",
			"alarmContext": "告警内容"
		}
	},
	"simulationpowerattached": {
		"resultTile": "模拟加电结果",
		"deviceName": "设备名称",
		"powerOnAttribute": "加电属性",
		"originCapacity": "加电前",
		"currentCapacity": "加电后",
		"beforePercent": "加电前容量占比",
		"currentPercent": "加电后容量占比",
		"capacityFloat": "容量占比变化",
		"highestPreAlarm": "最高预警",
		"ratedCapacity": "额定容量"
	},
	"distribution": {
		"title": "配电关系",
		"FromName": "上游设备",
		"toName": "下游设备",
		"type": "类型",
		"": "模拟加电结果",
		"excelFileName": "配电关系.xlsx",
		"delete": "请确认是否删除",
		"addUpstreamDeviceSuccess": "新增上游设备成功",
		"addDownstreamDeviceSuccess": "新增下游设备成功",
		"addRackSuccess": "新增下游设备成功",
		"relationship": {
			"device": "设备",
			"deviceOrRack": "设备/机架",
			"selectDeviceAndRack": "已选设备/机架模型",
			"selectRack": "选择机架",
			"rootDevices": "根设备",
			"deviceTree": "设备树"
		},
		"exportError": "机架不能作为上游设备"
	},
	"featuremanage": {
		"alarmconfig": {
			"autoconfirm": "自动确认",
			"alarm": "告警",
			"autoconfirmCount": "自动确认阈值",
			"autoconfirmunit": "条",
			"autoconfirmTip": "各级别告警条数超过设置的阈值将自动确认",
			"eventSeverityConfig": "告警等级设置",
			"eventReason": "告警原因分类",
			"name": "名称",
			"description": "描述"
		}
	},
	"devices": {
		"signalList": "信号列表",
		"controlList": "控制列表",
		"alarmList": "当前告警",
		"maskingAlarm": "屏蔽告警",
		"startUpMaskingAlarm": "屏蔽",
		"cancelMaskingAlarm": "修改屏蔽",
		"stateOn": "已屏蔽",
		"stateOff": "未屏蔽",
		"analog": "模拟量",
		"switch": "开关量",
		"BAcontrol": "BA实时控制命令",
		"endedUnconfirmed": "已结束未确认",
		"operation": {
			"selectSignal": "选择信号",
			"placeHolderOfSearch": "输入关键字搜索",
			"todayOfAlarm": "今天",
			"yesterdayOfAlarm": "昨天",
			"last7DaysOfAlarm": "最近7天",
			"last30DaysOfAlarm": "最近30天",
			"saveVisibleSignal": "保存"
		},
		"info": {
			"number": "编号",
			"deviceName": "名称",
			"brand": "品牌",
			"type": "类型",
			"model": "型号",
			"capacity": "容量参数",
			"location": "安装位置",
			"pipeInfo": "管网设备信息",
			"devicePosition": "设备位置",
			"deviceType": "设备类型",
			"monitorInfo": "监测设备信息",
			"deviceModel": "设备型号",
			"signalCount": "测点数量",
			"connectionStatus": "通信状态",
			"alarmStatus": "告警状态"
		},
		"excelColumns": {
			"deviceName": "设备名称",
			"signalName": "信号名称",
			"signalValue": "信号值",
			"sampleTime": "采集时间",
			"alarmName": "告警名称",
			"alarmMeaning": "告警含义",
			"alarmLevel": "告警等级"
		}
	},
	"energyConsumption": {
		"pueTab": {
			"currentDay": "当天",
			"24Hours": "24小时",
			"currentMonth": "当月",
			"currentYear": "当年",
			"selfDefine": "自定义"
		}
	},
	"maintenanceOrder": {
		"maintenanceOrder": "维护工单",
		"maintenanceProcedure": "维护规程",
		"orderName": "工单名称",
		"selectProcedure": "维护规程",
		"planTime": "维护时间",
		"devices": "维护设备",
		"doUser": "维护人员",
		"auditUser": "审核人员",
		"overTime": "超时时间(天)",
		"desc": "描述",
		"deleteTip": "删除确认",
		"planCompleteTrend": "维护计划趋势",
		"planProcedureStatic": "维护规程统计",
		"maintenancePlanStatic": "维护计划统计",
		"todomantencecount": "未维护计划",
		"overdyecount": "超时维护计划",
		"maintenancedReport": "已维护报告",
		"roomName": "房间名称",
		"deviceName": "设备名称",
		"deviceCatagory": "设备种类",
		"reportMaintenance": "按设备类型报告详情"
	},
	"machineCalendar": {
		"content": "内容",
		"contentNotNull": "内容不能为空",
		"type": "类型",
		"role": "处理人",
		"roleNotNull": "处理人不能为空",
		"time": "时间",
		"description": "描述"
	},
	"equipmentTemplate": {
		"id": "ID",
		"name": "名称",
		"agreement": "协议",
		"deviceVender": "设备厂家",
		"deviceModel": "设备型号",
		"deviceType": "设备类型",
		"reason": "模板创建原因",
		"explain": "说明",
		"tips": {
			"nameNotNull": "名称不能为空"
		},
		"signal": {
			"signalConfig": "信号配置",
			"yes": "是",
			"no": "否",
			"signalProperty": "信号属性",
			"propertyName": "属性名称",
			"propertyId": "属性ID",
			"signalMeanings": "状态信号含义",
			"value": "值",
			"means": "含义",
			"operate": "操作"
		},
		"event": {
			"title": "模板事件",
			"eventName": "名称",
			"displayIndex": "显示顺序",
			"eventId": "告警ID",
			"eventCategory": "事件类别",
			"eventCondition": "条件",
			"startExpression": "开始表达式",
			"signal": "关联信号",
			"visible": "可见"
		},
		"condition": {
			"title": "事件条件设置",
			"conditionId": "ID",
			"baseTypeName": "基类事件",
			"equipmentState": "事件等级",
			"startOperation": "开始操作符",
			"startCompareValue": "开始比较值",
			"startDelay": "开始延时",
			"meanings": "含义"
		}
	},
	"configTool": {
		"signalBaseDic": {
			"signalBaseDic": "信号基类字典表",
			"baseTypeId": "信号基类ID",
			"baseTypeName": "信号基类名称",
			"baseEquipmentId": "基类设备类型ID",
			"baseEquipmentName": "基类设备类型",
			"equipmentTypeName": "基础设备大类",
			"equipmentSubTypeName": "基础设备子类",
			"englishName": "基类名称",
			"baseLogicCategoryId": "信号逻辑分类ID",
			"baseLogicCategoryName": "信号逻辑分类",
			"storeInterval": "存储周期",
			"absValueThreshold": "绝对值阀值",
			"percentThreshold": "百分比阀值",
			"storeInterval2": "存储周期2",
			"absValueThreshold2": "绝对值阀值2",
			"percentThreshold2": "百分比阀值2",
			"unitId": "单位ID",
			"unitName": "单位",
			"baseFreqPeriod": "统计周期",
			"baseNameExt": "名称扩展表达式",
			"isSystem": "是否系统内置",
			"description": "备注",
			"equipmentTypeNotNull": "基础设备大类不能为空",
			"equipmentSubTypeNotNull": "基础设备子类不能为空",
			"baseTypeNameNotNull": "信号基类名称不能为空",
			"signalName": "信号名称",
			"signalNameNotNull": "信号名称不能为空",
			"signalBaseDicSetting": "基类信号设置",
			"signalBaseDicBinding": "关联基类信号",
			"branchIndex": "序号",
			"branchIndexMinValue": "最小值1",
			"branchIndexMaxValue": "最大值999",
			"branchSettingConfig": "序号配置说明",
			"exampleConfig": "示例说明",
			"operationWarning": "操作警告",
			"signalBaseDicIsBinded": "该基类信号已关联",
			"cancelBindingSignalBaseDic": "取消关联基类信号",
			"confirmCancelBinding": "确认要取消关联基类信号吗？",
			"bindingASignalBaseDic": "请选择关联的基类信号",
			"branchSettingDescription": "以电池的单体电压信号为例, 如果信号[单体电压1]关联的基类信号为[01#电池单体电压], 那么对信号[单体电压2]设置基类信号时, 如果基类信号列表中不包含[02#电池单体电压], 则可以在基类信号列表中选择[01#电池单体电压]并在序号中输入[2], 这样就可以完成对信号[单体电压2]的基类信号配置. ",
			"logicCategoryBaseDicName": "信号逻辑分类",
			"logicCategoryBaseDicNotNull": "信号逻辑分类不能为空",
			"baseNameExtDescription": "若需要增加01#模块远程开关控制这样一个基类控制, 而模块号本身是可扩展的, 则可以在其[名称扩展表达式]中输入[{0:D2}#模块远程开关控制]. 根据该控制基类可以扩展出如下的控制基类：02#模块远程开关控制、03#模块远程开关控制等；如果希望名称格式是001#模块远程开关控制、002#模块远程开关控制, 则其[名称扩展表达式]需输入[{0:D3}#模块远程开关控制]. ",
			"inputDataInvalid": "输入的格式不正确",
			"inputInteger": "要求输入整数",
			"ThreeDecimalFormatter": "精度最多支持三位小数"
		},
		"eventBaseDic": {
			"eventBaseDic": "告警基类字典表",
			"baseTypeId": "告警基类ID",
			"baseTypeName": "告警基类名称",
			"baseEquipmentId": "基类设备ID",
			"baseEquipmentName": "基类设备类型",
			"equipmentTypeName": "基础设备大类",
			"equipmentSubTypeName": "基础设备子类",
			"englishName": "告警基类名称",
			"baseLogicCategoryId": "告警逻辑分类ID",
			"baseLogicCategoryName": "告警逻辑分类",
			"baseNameExt": "名称扩展表达式",
			"isSystem": "是否系统内置",
			"description": "备注",
			"eventCondition": "告警条件",
			"equipmentTypeNotNull": "基础设备大类不能为空",
			"equipmentSubTypeNotNull": "基础设备子类不能为空",
			"baseTypeNameNotNull": "告警基类名称不能为空",
			"eventName": "告警名称",
			"eventNameNotNull": "告警名称不能为空",
			"eventBaseDicSetting": "基类告警设置",
			"eventBaseDicBinding": "关联基类告警",
			"branchIndex": "序号",
			"branchIndexMinValue": "最小值1",
			"branchIndexMaxValue": "最大值999",
			"branchSettingConfig": "序号配置说明",
			"operationWarning": "操作警告",
			"eventBaseDicIsBinded": "该基类告警已被其他告警条件关联",
			"confirmCancelBinding": "确认要取消关联基类告警吗？",
			"bindingAEventBaseDic": "请选择关联的基类告警",
			"cancelBindingEventBaseDic": "取消关联基类告警",
			"branchSettingDescription": "以电池的单体电压落后告警为例, 如果告警[单体电压1]关联的基类告警为[电池单体1落后告警], 那么对告警[单体电压2]设置基类告警时, 如果列表中不包含[单体电压2落后告警], 则可以在列表中选择[电池单体1落后告警]并在序号中输入[2], 这样就可以完成对告警[单体电压2]的基类告警配置. ",
			"baseNameExtDescription": "若需要增加电池单体01#落后告警这样一个基类告警, 而单体号本身是可扩展的, 则可以在其[名称扩展表达式]中输入[电池单体{0:D2}#落后告警]. 根据该告警基类可以扩展出如下的告警基类：电池单体02#落后告警、电池单体03#落后告警等；如果希望名称格式是电池单体001#落后告警、电池单体002#落后告警, 则其[名称扩展表达式]需输入[电池单体{0:D3}#落后告警]. "
		},
		"commandBaseDic": {
			"commandBaseDic": "控制基类字典表",
			"baseTypeId": "控制基类ID",
			"baseTypeName": "控制基类名称",
			"baseEquipmentId": "基类设备ID",
			"baseEquipmentName": "基类设备类型",
			"equipmentTypeName": "基础设备大类",
			"equipmentSubTypeName": "基础设备子类",
			"englishName": "控制基类名称",
			"baseLogicCategoryId": "控制逻辑分类ID",
			"baseLogicCategoryName": "控制逻辑分类",
			"commandType": "控制类型",
			"baseNameExt": "名称扩展表达式",
			"isSystem": "是否系统内置",
			"description": "备注",
			"equipmentTypeNotNull": "基础设备大类不能为空",
			"equipmentSubTypeNotNull": "基础设备子类不能为空",
			"baseTypeNameNotNull": "控制基类名称不能为空",
			"commandName": "控制名称",
			"commandNameNotNull": "控制名称不能为空",
			"commandBaseDicSetting": "基类控制设置",
			"commandBaseDicBinding": "关联基类控制",
			"branchIndex": "序号",
			"branchIndexMinValue": "最小值1",
			"branchIndexMaxValue": "最大值999",
			"branchSettingConfig": "序号配置说明",
			"operationWarning": "操作警告",
			"commandBaseDicIsBinded": "该基类控制信号已关联",
			"cancelBindingCommandBaseDic": "取消关联基类控制",
			"confirmCancelBinding": "确认要取消关联基类控制吗？",
			"bindingACommandBaseDic": "请选择关联的基类控制",
			"branchSettingDescription": "以空调的遥控开机信号为例, 如果控制信号[空调1遥控开机]关联的基类信号为[遥控空调1开机],那么对信号[空调2遥控开机]设置基类信号时, 如果基类信号列表中不包含[遥控空调2开机],则可以在基类信号列表中选择[遥控空调1开机]并在序号中输入[2], 这样就可以完成对信号[空调2遥控开机]的基类信号配置. ",
			"baseNameExtDescription": "若需要增加01#模块远程开关控制这样一个基类控制, 而模块号本身是可扩展的, 则可以在其[名称扩展表达式]中输入[{0:D2}#模块远程开关控制]. 根据该控制基类可以扩展出如下的控制基类：02#模块远程开关控制、03#模块远程开关控制等；如果希望名称格式是001#模块远程开关控制、002#模块远程开关控制, 则其[名称扩展表达式]需输入[{0:D3}#模块远程开关控制]. "
		},
		"equipmentBaseType": {
			"equipmentBaseType": "设备类型字典表",
			"baseEquipmentId": "基础类别ID",
			"baseEquipmentName": "基础类别名称",
			"equipmentSubTypeId": "基础设备子类ID",
			"equipmentSubTypeName": "基础设备子类",
			"equipmentTypeId": "基础设备大类ID",
			"equipmentTypeName": "基础设备大类",
			"description": "备注"
		}
	},
	"accessControl": {
		"areaManagement": {
			"title": "区域管理",
			"name": "区域名称",
			"center": "门禁区域中心",
			"addAreaTitle": "新增区域",
			"nameNotNull": "区域名称不能为空",
			"deleteConfirm": "确认删除{0}?",
			"deleteDoorAreaSuccess": "删除区域{0}成功!",
			"deleteDoorAreaFail": "删除区域{0}失败!",
			"mountDevices": "挂载设备",
			"mountDevicesSuccess": "区域{0}挂载设备成功!",
			"mountDevicesFail": "区域{0}挂载设备失败!",
			"updateDoorAreaSuccess": "修改区域{0}成功!",
			"updateDoorAreaFail": "修改区域{0}失败!"
		},
		"cardManagement": {
			"title": "卡管理",
			"cardName": "卡名称",
			"cardLost": "卡丢失",
			"cardCopy": "卡复制",
			"cardDelay": "卡延期",
			"cardType": "卡类型",
			"terminalType": "终端类型",
			"cardNo": "卡号",
			"originalCard": "原卡号",
			"targetCard": "目标卡号",
			"validTime": "有效日期",
			"cardPassword": "卡密码",
			"cardHolder": "持卡人",
			"cardGroup": "卡分组",
			"registrationDate": "注册时间",
			"inDate": "有效期",
			"inDateStart": "有效期开始",
			"inDateEnd": "有效期结束",
			"useState": "使用状态",
			"addCardTitle": "新增卡",
			"updateCardTitle": "修改卡",
			"addCardGroupTitle": "新增分组",
			"editCardGroupTitle": "修改分组",
			"groupName": "分组名",
			"groupNameNotNull": "分组名不能为空！",
			"searchForPersons": "请输入条件查询",
			"cardNameNotNull": "卡名称不能为空！",
			"expireDateLimit": "海康门禁有效期不能超过2037年",
			"cardNoNotNull": "卡号不能为空！",
			"cardHolderNotNull": "持卡人不能为空！",
			"addCardHolder": "请新增持卡人账号！",
			"originalCardNoNotNull": "原卡号不能为空！",
			"targetCardNoNotNull": "目标卡号不能为空！",
			"dateNoNotNull": "日期不能为空！",
			"deleteLostCard": "是否挂失原卡",
			"cardNoType": "卡号类型",
			"decimalism": "十进制",
			"hexadecimal": "十六进制",
			"cardNoPattern": "卡号需符合十进制格式且长度不能超过十位！",
			"cardNoPatternForHex": "卡号需符合十六进制格式且长度不能超过十位！",
			"cardPasswordNotNull": "卡密码不能为空！",
			"cardPasswordLessLength": "卡密码不能少于4位！",
			"cardPasswordMustNumber": "卡密码必须为4-10位数字组成！",
			"deleteGroupSuccess": "删除分组{0}成功!",
			"deleteGroupFail": "删除分组{0}失败!",
			"deleteCardsSuccess": "删除卡成功!",
			"deleteCardsFail": "删除卡失败!",
			"deleteAllConfirm": "确认批量删除?",
			"deleteConfirm": "确认删除",
			"selectDeleteCard": "请选择需要删除的卡！",
			"selectDelayCard": "请选择需要延期的卡！",
			"commandSuccess": "发送控制成功！",
			"commandFail": "发送控制失败！",
			"cardReader": "读卡器读卡",
			"feature": "特征",
			"needCardReaderApp": "需要下载读卡器服务程序并安装启动！",
			"downloadCardReaderApp": "下载读卡器服务程序",
			"needFingerprintReaderApp": "需要下载指纹读取服务程序并安装启动！",
			"downloadFingerprintReaderApp": "下载指纹读取服务程序",
			"face": "人脸",
			"fingerprint": "指纹",
			"createCardSuccess": "创建卡成功!",
			"createCardFail": "创建卡失败!",
			"bindFaceSuccess": "人脸录入成功!",
			"bindFaceFail": "人脸录入失败!",
			"bindFingerSuccess": "指纹录入成功!",
			"bindFingerFail": "指纹录入失败!",
			"submitCardInfo": "卡信息將被保存!",
			"deleteFaceSuccess": "人脸信息删除成功!",
			"deleteFaceFail": "人脸信息删除失败!",
			"timeNotExpired": "结束时间不能早于开始时间!"
		},
		"doorManagement": {
			"title": "门设备管理",
			"name": "门设备名称",
			"batchOperate": "批量操作",
			"filterResult": "设备过滤",
			"filterResult2": "结果过滤",
			"doorParam": "门参数",
			"lastCardSwipeRecord": "末次刷卡记录",
			"control": "控制",
			"event": "告警",
			"swipeRecord": "刷卡记录",
			"confirm": "确认",
			"resend": "重发",
			"noSelected": "未选择对象！",
			"doorName": "门名称",
			"doorPassword": "门密码",
			"doorNo": "门编号",
			"openMode": "开门模式",
			"fireSignal": "火警信号",
			"lockErrorCount": "卡封锁错误次数",
			"slotInterval": "非法卡刷卡间隔(秒)",
			"lockTime": "卡封锁时间(秒)",
			"keepTime": "门开保持时间(秒)",
			"openDelay": "门开超时时间(秒)",
			"keepOpen": "常开",
			"keepClose": "常闭",
			"auto": "自动",
			"swipeTime": "刷卡时间",
			"inOutFlag": "进出门",
			"isValid": "刷卡或开门状态",
			"controlName": "控制名称",
			"controlResult": "控制结果",
			"controlExecuterId": "执行人",
			"controlParams": "控制参数",
			"unGroup": "未分组",
			"default": "默认",
			"unset": "未设置",
			"doorPasswordConfirm": "门密码确认",
			"doorPasswordNotMatch": "两次输入密码不一致!",
			"editDoorParams": "编辑门参数",
			"timeGroup": "准进时间组",
			"setDoorNameSuccess": "修改门名称成功!",
			"setDoorNameFailed": "修改门名称失败!",
			"sendControlSuccess": "发送修改控制成功!",
			"sendControlError": "发送修改控制出错！",
			"openDoor": "开门",
			"closeDoor": "关门",
			"byStructure": "实际",
			"byArea": "区域",
			"byDoorGroup": "门分组",
			"byBackups": "备份",
			"confirmSuccess": "确认控制成功!",
			"confirmError": "确认控制出错!",
			"resendSuccess": "重发控制成功!",
			"resendError": "重发控制出错!",
			"resendSomeError": "没有控制结果,无法重发!",
			"eventName": "告警名称",
			"eventSeverity": "告警等级",
			"setTime": "门禁校时",
			"clearCard": "删除所有卡",
			"infoType": "事件类型",
			"eventInfo": "事件信息",
			"infoTime": "事件时间",
			"addTags": "添加标签"
		},
		"cardAuthorization": {
			"title": "卡授权",
			"selectDoor": "选择门",
			"selectCard": "选择卡",
			"authorize": "授权",
			"deleteAuthorize": "删除授权",
			"byGroup": "按分组",
			"byPerson": "按人员",
			"byBackups": "备份",
			"placeholder": "请输入检索关键字",
			"authorizeSuccess": "发送授权控制成功!",
			"authorizeError": "发送授权控制出错!",
			"deleteSuccess": "发送删除控制成功!",
			"deleteError": "发送删除控制出错!",
			"modifySuccess": "发送修改授权控制成功!",
			"modifyError": "发送修改授权控制出错!",
			"invalidDate": "日期格式不正确!",
			"deleteCheck": "删除卡授权会对门上所有时间组执行相同操作,是否继续?",
			"needSelectDoor": "请先对门设置准进时间组!",
			"needSelectCard": "请选择门禁卡!",
			"card": "门禁卡",
			"fingerprint": "指纹",
			"face": "人脸",
			"outdate": "有更新",
			"isAuthorize": "确认是否授权!",
			"newabelSelectTips": "纽贝尔门禁设备必须选择一个准进时间组！"
		},
		"controlQueue": {
			"operateTime": "操作时间",
			"deleteQueueSuccess": "移除队列成功!",
			"deleteQUeueError": "移除队列出错!",
			"waiting": "等待",
			"handling": "活动",
			"history": "历史"
		},
		"doorGroup": {
			"title": "门分组管理",
			"name": "门分组",
			"nameNotNull": "门分组名称不能为空",
			"deleteConfirm": "确认删除{{0}}?",
			"deleteDoorAreaSuccess": "删除门分组{{0}}成功!",
			"deleteDoorAreaFail": "删除门分组{{0}}失败!",
			"mountDevicesSuccess": "门分组{{0}}挂载设备成功!",
			"mountDevicesFail": "门分组{{0}}挂载设备失败!",
			"updateDoorAreaSuccess": "修改门分组{{0}}成功!",
			"updateDoorAreaFail": "修改门分组{{0}}失败!"
		},
		"errorCode": {
			"1": "出现的故障不能为空!",
			"2": "处理方式不能为空!",
			"3": "寿命评估不能为空!",
			"4": "投产日期不能为空!",
			"-1": "访问服务发生错误！",
			"-50101": "已存在相同卡号！",
			"-50102": "无效的卡类型！",
			"-50103": "无效的卡分组！",
			"-50104": "无效的卡号！",
			"-50105": "无效的门类型！",
			"-50106": "无效的卡种类！",
			"-50107": "无效的密码！",
			"-50108": "无效的持卡人！",
			"-50109": "无效的局站名称!",
			"-50110": "无效的时间！",
			"-50301": "不允许删除已授权时间组！",
			"-50401": "不允许修改已授权人脸！",
			"-50402": "不允许修改已授权指纹！"
		}
	},
	"linkagestrategy": {
		"name": "策略名称",
		"description": "策略描述",
		"interval": "CRON表达式",
		"usedstatus": "启用状态",
		"group": "策略分组",
		"nameNotNull": "联动策略名称不能为空",
		"triggerIntervalNotNull": "触发间隔不能为空",
		"addGroup": "新增分组",
		"deleteGroupError": "分组下存在策略不能删除！",
		"deviceTree": "设备树",
		"IntegerOperation": "整数运算",
		"FloatOperation": "浮点数运算",
		"RelationalOperation": "关系运算",
		"LogicOperation": "逻辑运算",
		"AggregationOperation": "聚合运算",
		"Event": "事件",
		"LinkageControl": "联动控制",
		"Constant": "常量",
		"add": {
			"groupNotNull": "分组不能为空",
			"triggerType": "触发类型",
			"timeTrigger": "时间触发",
			"eventTrigger": "事件触发",
			"triggerTypeNotNull": "触发类型不能为空",
			"startTime": "开始日期",
			"startTimeNotNull": "开始时期不能为空",
			"endTime": "结束日期",
			"endTimeNotNull": "结束时期不能为空"
		},
		"edit": {
			"strategyLogic": "策略逻辑",
			"beenSet": "已设置",
			"set": "设置",
			"check": "查看"
		},
		"list": {
			"deleteStrategyError": "启用状态的策略不能删除！",
			"strategyList": "策略列表",
			"addStrategy": "新增策略"
		},
		"groupAdd": {
			"addGroup": "添加策略分组",
			"name": "名称",
			"nameNotNull": "策略分组名称不能为空",
			"description": "描述",
			"group": "分组",
			"createSuccess": "创建成功"
		},
		"groupEdit": {
			"editGroup": "编辑策略分组",
			"editSuccess": "修改成功"
		}
	},
	"alarmFitting": {
		"convergenceRate": {
			"title": "收敛率分析",
			"todayConvergenceRate": "今日收敛率",
			"todayAlarms": "今日总告警",
			"todayConvergenceAlarms": "今日收敛告警",
			"eventLineDiagram": "事件分析",
			"event": "告警事件",
			"convergentEvent": "收敛事件",
			"convergenceRate": "收敛率",
			"eventCount": "事件数(条)",
			"convergenceRateCount": "收敛率(%)"
		},
		"filtrationEventQuery": {
			"title": "过滤事件查询",
			"filterRule": "过滤规则",
			"associatedDevice": "关联设备",
			"eventDescription": "事件描述",
			"eventType": "事件类型",
			"eventName": "告警名称"
		},
		"convergenceEvent": {
			"title": "收敛事件分析",
			"eventTimeLine": "收敛事件时间线",
			"eventLevelMatrix": "告警等级点阵",
			"eventDeviceCategory": "告警-设备类统计",
			"eventList": "事件列表",
			"conditionList": "条件列表",
			"num": "序号",
			"birthTime": "开始时间",
			"endTime": "结束时间",
			"rootCauses": "根因",
			"convergenceEvent": "收敛告警",
			"cenvergenceNums": "收敛数量",
			"liveCounts": "未结束数量",
			"convergenceType": "处理分类",
			"impactRangeAnalysis": "影响范围分析",
			"hasDealWith": "已处理",
			"UnDealWith": "未处理",
			"convergenceCountMsg": "共收敛{{count}}条事件",
			"selectedEventToConfirm": "请选择要确认的收敛事件！",
			"batchConfrimEventSuccess": "批量确认成功！",
			"eventName": "告警名称",
			"equipmentName": "设备名称",
			"equipmentType": "设备分类",
			"occurRemark": "事件含义"
		}
	},
	"equipmentManagement": {
		"noPermission": "无操作权限!",
		"cameraManagement": {
			"treeNode": "区域根节点",
			"title": "区域",
			"name": "摄像头名称",
			"byArea": "按区域",
			"groupName": "分组方式",
			"station": "局站",
			"room": "局房",
			"factory": "厂家",
			"model": "设备类型",
			"code": "编码",
			"IPAddress": "IP地址",
			"location": "安装位置",
			"listname": "名称",
			"state": "状态",
			"operatingStatus": "运行状态",
			"belongLabel": "分组方式",
			"labelGroup": "标签组",
			"remark": "备注",
			"preview": "预览",
			"realTimePreview": "实时预览",
			"serialNumber": "序列号",
			"protoName": "协议",
			"configurationPreview": "配置预览",
			"parameter": "参数配置",
			"informationSet": "信息设置",
			"systemSet": "系统设置",
			"internetSet": "网络设置",
			"videoSet": "视频",
			"imageSet": "OSD设置",
			"unmanaged": "请选择未管理的摄像头",
			"endProxyName": "代理终端名称",
			"endProxyCode": "代理终端编码",
			"playFailed": "播放失败！",
			"connectFailed": "摄像头连接失败！",
			"passwordVerification": "请输入正确的编码格式！",
			"pwdRequire": "编码由大小写字母、数字组成！",
			"newCamera": "新摄像头",
			"delCamera": "已删除摄像头",
			"ipCheck": "请输入合法ip!",
			"delCheck": "清空",
			"createTime": "添加日期",
			"createUser": "添加人",
			"total": "总数",
			"dataErrorMessage": "数据错误信息",
			"verifyErrorMessage": "验证错误信息",
			"nvr": "NVR",
			"cvr": "CVR",
			"camera": "摄像头",
			"rtspPort": "RTSP端口",
			"httpPort": "HTTP端口",
			"serverPort": "SDK端口",
			"systemSetForm": {
				"ntp": "NTP校时",
				"hand": "手动校时",
				"equiName": "设备名称",
				"equiNumber": "设备编码",
				"equiModel": "设备型号",
				"equiSerial": "设备序列号",
				"timeZone": "时区",
				"serverAddress": "服务器地址",
				"NTPPort": "NTP端口",
				"timingInterval": "时间间隔",
				"equimentTime": "视频服务器时间",
				"setTime": "设置时间",
				"synchronizaComputer": "与计算机时间同步",
				"minute": "分钟",
				"test": "测试"
			},
			"internetSetForm": {
				"networkCardType": "网卡类型",
				"automatic": "自动获取",
				"iPv4Address": "设备IPv4地址",
				"iPv4SubnetMask": "IPv4子网掩码",
				"iPv4DefaultGateway": "IPv4默认网关",
				"iPv6Model": "IPv6模式",
				"iPv6Address": "设备IPv6地址",
				"iPv6SubnetMask": "IPv6子网掩码",
				"iPv6DefaultGateway": "IPv6默认网关"
			},
			"videoSetForm": {
				"streamType": "码流类型",
				"videoType": "视频类型",
				"resolution": "分辨率",
				"bitRateType": "码率类型",
				"imageQuality": "图像质量",
				"videoFrameRate": "视频帧率",
				"maximumBitRate": "码率上限",
				"videoEncoding": "视频编码",
				"codingComplexity": "编码复杂率",
				"iFrameInterval": "I帧间隔",
				"svc": "SVC",
				"streamSmoothg": "码流平滑"
			},
			"imageSetForm": {
				"showName": "显示名称",
				"showDate": "显示日期",
				"showWeek": "显示星期",
				"channelName": "通道名称",
				"timeFormat": "时间格式",
				"dateFormat": "日期格式",
				"characterOverlay": "字符叠加"
			},
			"unknownError": "未知错误",
			"finishPlay": "播放结束"
		},
		"groupManagement": {
			"name": "名称",
			"addPeople": "添加人",
			"remark": "备注",
			"group": "分组",
			"groupMethod": "分组方式",
			"groupMethodTree": "分组根节点",
			"addGroupMethod": "分组方式",
			"nodeName": "节点名称",
			"bindCamera": "绑定摄像头",
			"selectedCamera": "已选摄像头",
			"unbind": "解除绑定",
			"confirmunbind": "请确认是否解除绑定："
		},
		"nvrManagement": {
			"list": {
				"factory": "厂家",
				"model": "型号",
				"code": "编码",
				"IPAddress": "IP地址",
				"location": "安装位置",
				"listname": "设备名称",
				"state": "状态",
				"operatingStatus": "运行状态",
				"cameraInfo": "摄像头信息",
				"aisleInfo": "通道设置",
				"remark": "备注",
				"recordPlan": "录制计划",
				"recordPlanError": "录制计划设置失败！",
				"eventSetting": "事件设置",
				"motionDetection": "移动侦测",
				"occlusionAlarm": "遮挡告警",
				"videoLoss": "视频丢失",
				"regionalSetting": "区域设置",
				"armingTime": "布防时间",
				"startUse": "启用",
				"clearAll": "清除所有",
				"sensitive": "灵敏度",
				"timing": "定时",
				"event": "事件"
			},
			"add": {
				"title": "新增NVR设备",
				"unmanaged": "未管理设备",
				"owningTerminal": "所属终端",
				"editNvr": "修改设备"
			},
			"camera": {
				"bindCamera": "绑定摄像头",
				"channelNumber": "通道号",
				"ipAddress": "IP通道地址",
				"videoUrl": "视频流地址",
				"userName": "用户名",
				"cameraSearch": "搜索",
				"aisle": "通道名",
				"protocol": "协议",
				"deviceIP": "设备IP",
				"port": "端口",
				"password": "密码",
				"equiInspection": "设备检测",
				"detect": "检测",
				"testSuccessful": "检测成功！",
				"deviceNumber": "设备通道号",
				"searchDevice": "搜索设备",
				"equipmentType": "设备类型",
				"chosen": "已选择",
				"copyOther": "复制到...",
				"aisleChoose": "通道选择",
				"cameraChoose": "请选择摄像头！",
				"selectCamera": "选择摄像机"
			}
		},
		"realCamera": {
			"monitoringPoint": "监控点",
			"viewList": "视图模板",
			"addViewTitle": "新增视图模板",
			"editViewTitle": "编辑视图模板",
			"viewName": "视图模板名",
			"viewNameNotNull": "视图模板名不能为空！",
			"resourceOccupation": "该摄像头控制资源已被占用！",
			"saveTemplate": "保存视图模板",
			"saveTemplateAs": "另存为模板",
			"currentView": "当前视图",
			"customViewport": "自定义视图",
			"confirmSave": "请确认是否提交修改？",
			"standardLayout": "标准布局",
			"customLayout": "自定义布局",
			"layoutSetting": "布局配置",
			"addLayout": "新增布局",
			"editLayout": "编辑布局",
			"layoutName": "布局名称",
			"layoutSize": "尺寸",
			"layoutSplit": "窗口分割",
			"combine": "合并",
			"cancelCombine": "取消合并",
			"closeAllPreviews": "关闭所有预览",
			"fullVideo": "播放窗口已满，请拖动摄像头替换播放窗口！"
		},
		"PTZcontrol": {
			"PTZcontrolName": "视频预览",
			"PTZ": "云台",
			"speed": "速度",
			"focus": "调焦",
			"distance": "调距",
			"controlFailed": "控制失败！"
		},
		"terminalManagement": {
			"title": "名称",
			"code": "编码",
			"location": "位置",
			"state": "状态",
			"remark": "备注",
			"cameraNumber": "摄像头数量",
			"nvrNumber": "NVR设备数量",
			"srsApiPort": "Api端口",
			"srsRtmpPort": "推流端口",
			"srsWebRtcPort": "WebRtc端口",
			"add": {
				"titleNotNull": "名称不能为空！",
				"codeNotNull": "编码不能为空！",
				"numberNotNull": "数量不能为空！",
				"locationNotNull": "位置不能为空！",
				"ipNotNull": "IP不能为空！",
				"srsApiPortNotNull": "Api端口不能为空！",
				"srsRtmpPortNotNull": "推流端口不能为空！",
				"srsWebRtcPortNotNull": "WebRtc端口不能为空！"
			}
		},
		"pollingGroup": {
			"group": "轮巡组",
			"groupName": "轮巡组名称",
			"groupNameNotNull": "轮巡组名称不能为空",
			"pollingInterval": "轮巡间隔(秒)",
			"pollingIntervalNotNull": "轮询间隔不能为空",
			"pollingCamera": "轮巡摄像头",
			"savePollingGroupSuccess": "保存轮巡分组成功",
			"savePollingGroupFailed": "保存轮巡分组失败",
			"byArea": "按区域",
			"loadError": "加载出错",
			"loadErrorRetry": "加载出错，重试中...",
			"notFoundSrc": "未找到源",
			"deletePollingGroupSuccess": "删除轮巡分组成功",
			"deletePollingGroupFailed": "删除轮巡分组失败"
		},
		"eventManagement": {
			"serialNumber": "序号",
			"eventSource": "事件源",
			"eventType": "事件类型",
			"eventTime": "事件时间",
			"eventLevel": "事件级别",
			"eventDetails": "事件细节",
			"state": "状态",
			"eventCamera": "告警摄像头",
			"batchProcess": "批量处理",
			"delete": "清空",
			"voiceOpen": "打开声音",
			"voiceClose": "关闭声音",
			"stopRefresh": "暂停刷新",
			"actionRefresh": "开始刷新",
			"eventDetail": "事件详情",
			"autoPlay": "自动播放视频 图片",
			"processrecords": "处理记录",
			"sendEmail": "发送邮件",
			"process": "处理",
			"processTime": "处理时间",
			"processDetails": "处理结果",
			"video": "视频",
			"noRecords": "无记录",
			"chooseRecord": "请先选择告警记录",
			"captrueVideo": "视频地址获取中...请稍后",
			"refreshTime": "设置刷新时间(分钟)",
			"rePlay": "重播",
			"noVideo": "无视频",
			"all": "全部"
		},
		"faceApp": {
			"faceComparision": "人脸比对",
			"checkDetail": "查看详情",
			"faceCompareInfo": "人脸比对信息",
			"faceSnapInfo": "人脸抓拍信息",
			"historySnap": "历史抓拍",
			"snapTime": "抓拍时间",
			"snapEquipment": "抓拍设备",
			"employeeName": "姓名",
			"departmentName": "组织",
			"email": "邮箱",
			"jobNo": "工号",
			"employeeTitle": "头衔",
			"phone": "手机号码",
			"mismatch": "匹配失败"
		},
		"faceManagement": {
			"peopleChoose": "选择人",
			"deviceChoose": "选择设备",
			"faceSend": "人脸下发",
			"deleteSend": "删除",
			"send": "下发",
			"sendStatus": "下发状态",
			"sendTime": "下发时间",
			"onSend": "已下发",
			"offSend": "下发队列",
			"faceInfo": "人脸信息",
			"errorMessage": "错误信息",
			"revoke": "撤销",
			"revokeAll": "撤销全部",
			"department": "部门",
			"history": "历史",
			"confirmRevoke": "确定要撤销吗？",
			"faceSubmit": "照片提交"
		},
		"logManagement": {
			"operation": {
				"title": "操作日志",
				"objectType": "操作对象类型",
				"object": "操作对象",
				"name": "操作名称",
				"parameter": "操作参数",
				"result": "操作结果",
				"operations": "操作者",
				"time": "操作时间",
				"type": "操作类型",
				"ip": "操作IP",
				"mode": "请求类型",
				"oldValue": "旧值",
				"newValue": "新值"
			},
			"service": {
				"title": "回看日志",
				"name": "回看状态",
				"cameraName": "摄像头名称",
				"parameter": "回看参数",
				"result": "回看结果",
				"ip": "回看IP",
				"time": "回看时间",
				"mode": "请求类型",
				"requestor": "回看者",
				"type": "回看类型"
			}
		},
		"versionService": {
			"versionType": "版本类型",
			"version": "版本名称",
			"dateFormat": "日期格式",
			"time": "更新时间"
		},
		"equipmentLedger": {
			"assetModelRatio": "资产型号占比",
			"assetModelRanking": "资产型号排行",
			"totalEquipment": "设备总数",
			"onlineAlarmEquipment": "在线告警设备数",
			"onlineEquipment": "在线设备数",
			"offlineEquipment": "离线设备数",
			"totalPoints": "点位总数",
			"equipmentStatus": "设备状态",
			"pointStatistics": "点位统计",
			"index": "序号",
			"equipmentCategory": "设备类型",
			"equipmentCategoryName": "设备模板名称",
			"equipmentCount": "设备数量",
			"equipmentOnlineRate": "设备在线率",
			"pointCount": "测点点位数",
			"totalPointCount": "测点点位总数",
			"sum": "总和",
			"export": "导出",
			"exportSummaryTable": "导出统计表",
			"exportFullPointTable": "导出全量点表",
			"manufacturerContact": "厂家联系人",
			"manufacturerPhone": "厂家联系方式",
			"serialNumber": "设备序列号",
			"factoryDate": "设备出厂时间",
			"purchaseDate": "设备采购时间",
			"warrantyPeriod": "设备质保时间",
			"warrantyExpiration": "质保到期时间",
			"lastMaintenanceDate": "上次保养时间",
			"nextMaintenanceDate": "下次保养时间",
			"assetDepartmentName": "资产归属部门名称",
			"assetManagerName": "资产负责人名称",
			"managerPhone": "负责人联系方式",
			"syncSuccess": "同步成功",
			"syncFailed": "同步失败，请联系管理员重置资产扩展字段配置",
			"syncSuccessNoNew": "同步完成，无新增数据",
			"syncSuccessWithNew": "同步成功，新增 {count} 条数据",
			"confirmSync": "确认同步",
			"syncLongTimeWarning": "该操作执行时间较长，确认开始同步？",
			"syncLongTimeWarningWithOverride": "该操作执行时间较长，确认开始同步，部分更改属性可能被同步操作覆盖？",
			"syncError": "发生错误，请稍后再试。",
			"syncDataLarge": "由于数据量较大，接口执行时间较长，请稍后刷新页面查看最新数据。",
			"signalName": "测点名称",
			"parameterNumber": "参数号",
			"parameterID": "参数ID",
			"loopNo": "回路号",
			"signalPoint": "测点点位 ",
			"indexCount": "指标点位 ",
			"syncFromEqu": "从设备同步",
			"unCatogory": "未分类",
			"signalPointDetail": "点位详情",
			"selectEquipmentCategory": "请选择设备类型"
		}
	},
	"energyPanorama": {
		"panoramaTitle": "能耗全景图",
		"pueTitle": "PUE",
		"powerPieTitle": "用电类型占比",
		"waterPieTitle": "用水类型占比",
		"powerBarTitle": "用电量",
		"waterBarTitle": "用水量",
		"airBarTitle": "用气量",
		"timeCheck": "所选时间: ",
		"periodPower": "本期总用电量",
		"consumptionPower": "同比总用电量",
		"chainPower": "环比总用电量",
		"periodWater": "本期总用水量",
		"consumptionWater": "同比总用水量",
		"chainWater": "环比总用水量",
		"periodAir": "本期总用气量",
		"consumptionAir": "同比总用气量",
		"chainAir": "环比总用气量",
		"noPueTitle": "能耗",
		"nodeName": "当前节点: ",
		"resourceType": "层级选择",
		"otherTitle": "其他信息",
		"alarmMode": "预警模式",
		"hisAlarmTop": "历史预警排名",
		"powerEnergyCurve": "用电曲线",
		"waterEnergyCurve": "用水曲线",
		"pueCurve": "PUE曲线",
		"wueCurve": "WUE曲线",
		"TierRank": "层级用电量排名",
		"WaterTierRank": "层级用水量排名",
		"tierSelect": "选层级",
		"rankSelect": "选排名",
		"costCurve": "电费曲线",
		"constType": "电费类型",
		"alarmInfo": {
			"meanings": "告警含义",
			"preAlarmSeverityName": "预警等级",
			"triggerValue": "触发值",
			"startTime": "开始时间",
			"endTime": "结束时间",
			"confirmTime": "确认时间"
		},
		"hierarchicaltreeconfig": {
			"projectName": "方案名称",
			"describe": "描述",
			"status": "状态",
			"hierarchicalList": "多维度方案",
			"hierarchicalTree": "方案详情",
			"operation": "操作",
			"insertSuccess": "新增成功",
			"off": "停用",
			"on": "启用",
			"delete": "删除",
			"copyOption": "复制选项",
			"copyOption_value1": "仅复制名称",
			"copyOption_value2": "引用节点",
			"isSaveTree": "保留树结构",
			"yes": "是",
			"no": "否",
			"defaultEnergy": "默认层级"
		},
		"saveEnergy": {
			"saveEnergyInfo": "节能措施信息",
			"energyName": "措施名称",
			"startTime": "施工开始时间",
			"endTime": "施工结束时间",
			"structureCount": "包含站点数量",
			"constructionUnit": "施工单位",
			"operator": "施工联系人",
			"contact": "联系电话",
			"notes": "措施介绍",
			"measuresDetails": "措施详情",
			"adoptedSite": "待采用站点",
			"usedSite": "已采用站点",
			"beforeAndAfterMeasuresReport": "节能措施前后站点用电量报表",
			"measuresSitePowerReport": "节能措施与否站点用电量报表",
			"measuresRankReport": "节能措施效果排名报表",
			"stationConfig": "配置站点",
			"parentResourceStructureName": "所属层级",
			"resourceStructureName": "层级名称",
			"isMark": "是否标杆站"
		},
		"energyReport": {
			"reportBeforeAndAfterMeasures": "措施前后用电量",
			"measuresOrNotPowerReport": "措施与否用电量",
			"rankingReportOfMeasures": "措施效果排名",
			"energyName": "措施名称",
			"structureName": "层级名称",
			"constructionPeriod": "施工期:",
			"timePeriod1": "措施前",
			"timePeriod1Power": "措施前用电量",
			"timePeriod2": "措施后",
			"timePeriod2Power": "措施后用电量",
			"powerConsumptionDifference": "节能量",
			"timePeriod": "时间段",
			"periodPower": "时间段用电量",
			"avgValue1": "已采用层级平均用电量",
			"avgValue2": "未实施层级平均用电量",
			"avgRate": "节能率",
			"viewAdoptPower": "已采用层级用电量详情",
			"viewNoAdoptPower": "未采用层级用电量详情",
			"ndaysBeforeAndAfter": "前后N天",
			"energySaving": "节能量",
			"energySavingRate": "节能率"
		},
		"energyEfficiency": {
			"configName": "配置名称",
			"totalPowerExpression": "总用电量表达式",
			"deviceExpression": "信息设备用电量表达式",
			"deviceRunLoadExpression": "信息设备运行负载表达式",
			"outerDryTemperatureExpression": "外侧干球温度表达式",
			"outerWetTemperatureExpression": "外侧湿球温度表达式",
			"innerDryTemperatureExpression": "内侧干球温度表达式",
			"innerWetTemperatureExpression": "内侧湿球温度表达式",
			"dataInterval": "数据采集间隔",
			"operatePoint": "工况点",
			"outDryTemperature": "数据中心外侧干球温度(℃)",
			"outWetTemperature": "数据中心外侧湿球温度(℃)",
			"innerDryTemperature": "数据中心内侧干球温度(℃)",
			"innerWetTemperature": "数据中心内侧湿球温度(℃)",
			"deviceRunLoad": "数据中心信息设备实际运行负载(%)",
			"deviceConsumptionPower": "信息设备消耗功率(kW)",
			"totalConsumptionPower": "数据中心总消耗功率(kW)"
		},
		"operatelog": {
			"operateloginfo": "操作日志信息",
			"serialNo": "序号",
			"operator": "操作人",
			"updateDate": "变更日期",
			"operationContent": "操作内容",
			"historyProject": "历史方案",
			"detail": "详情",
			"requestFail": "请求失败"
		},
		"electricityfeesEdit": {
			"schemeName": "方案名",
			"appliedRange": "应用范围",
			"stepPricing": "阶梯定价",
			"fengpinggu": "峰平谷",
			"enableDate": "启用时间",
			"deactivateDate": "停用时间",
			"schemeStatus": "方案状态",
			"stepName": "阶梯名称",
			"upperLimit": "用量上限",
			"upperLimitTitle": "用量上限",
			"feng": "峰",
			"ping": "平",
			"gu": "谷",
			"effectiveStart": "峰平谷生效时段(始)",
			"effectiveEnd": "峰平谷生效时段(终)",
			"fixAPrice": "定价(元)",
			"enable": "启用",
			"addBtnText": "新 增",
			"selectStructure": "选择层级",
			"hasSelected": "已选",
			"schemeNameNotNull": "方案名不能为空",
			"appliedRangeNotNull": "请选择应用范围",
			"enableDateNotNull": "启用时间不能为空",
			"deactivateDateNotNull": "停用时间不能为空",
			"stepPricingNotNull": "阶梯定价不能为空",
			"fengpingguNotNull": "峰平谷不能为空",
			"appliedMonthNotNull": "适用月份不能为空",
			"appliedMonth": "适用月份",
			"appliedMonthInvalid": "开始月份不能大于结束月份",
			"effectiveTimeNotNull": "时段值不能为空",
			"effectiveTimeInvalid": "时段始不能晚于时段终",
			"schemeIdNotNull": "[异常：主键为空]",
			"atLeastOneStep": "需至少保留一条阶梯定价",
			"yuan": "元",
			"du": "度",
			"pattern": {
				"0": "仅允许由数字、字母、汉字、底划线组成！",
				"1": "仅允许由数字和字母（a-f或A-F）组成！",
				"2": "仅允许数值！",
				"3": "IP地址格式不正确！",
				"4": "端口号无效!",
				"5": "邮箱格式不正确！",
				"6": "仅允许整数！",
				"7": "请输入时间格式HH:mm:ss"
			},
			"feecontrolperiod": "费控时段",
			"feecontrolperiodNotNull": "费控时段不能为空"
		},
		"electricityfeesInfo": {
			"operate": "操作",
			"view": "查看",
			"enableDisableDate": "启用/禁用时间",
			"resourceTable": "资源列表",
			"keyFilter": "关键字过滤",
			"currentLocation": "当前位置",
			"confirmMultiDel": "确定要执行此批量删除吗？",
			"confirmDel": "确定要删除吗？",
			"info": "信息",
			"addScheme": "新增方案",
			"getAll": "查看全部",
			"requestFail": "请求失败",
			"deleteFail": "删除失败",
			"deleteSuccess": "删除成功",
			"resDataIsNull": "返回数据为空",
			"all": "全部"
		},
		"message": {
			"energyNameNotNull": "措施名称不能为空",
			"timeNotValid": "结束时间必须大于开始时间",
			"timeNotNull": "开始时间和结束时间都不能为空",
			"startTimeNotValid": "开始时间不能为空",
			"endTimeNotValid": "结束时间不能为空",
			"time1NotValid": "时间段1的结束时间必须大于开始时间",
			"time2NotValid": "时间段2的结束时间必须大于开始时间",
			"structureNotNull": "层级不能为空，请先选择层级",
			"measuresNotNull": "措施不能为空，请先选择措施",
			"selstructureNotNull": "已采用层级不能为空,请先选择层级",
			"selNotstructureNotNull": "未采用层级不能为空，请先选择层级",
			"dayNumberNotNull": "请选择查询天数",
			"dayNotZero": "查询天数必须大于0",
			"confirmDelete": "请确认是否删除",
			"notSelected": "未选择任何节点",
			"setMaskConfirm": "确认设为标杆站",
			"removeMaskConfirm": "确认取消标杆站",
			"saveSuccess": "保存成功",
			"saveFailed": "保存失败",
			"exportSuccess": "导出成功",
			"exportFailed": "导出失败",
			"nodeNotDrill": "此节点下无子节点，不能钻取"
		},
		"button": {
			"refresh": "刷新",
			"add": "新增",
			"modify": "修改",
			"delete": "删除",
			"adoptionSite": "设置采用站点",
			"Viewadoptionsite": "查看已采用站点",
			"nolyShowCheck": "只显示选中",
			"cancelModify": "取消修改",
			"save": "保存",
			"close": "关闭",
			"ok": "确定",
			"setMark": "设为标杆站",
			"removeMark": "取消标杆站",
			"cancel": "返回",
			"selectResource": "选择层级",
			"checkSelectStructure": "选择已采用层级",
			"checkNotSelectStructure": "选择未采用层级",
			"selectMeasures": "选择措施",
			"search": "查询",
			"export": "导出",
			"viewAdoptPower": "查看已采用层级用电量详情",
			"viewNoAdoptPower": "查看未采用层级用电量详情",
			"drillNode": "向下钻取",
			"realtimeInfo": "实时信息",
			"preAlarmInfo": "预警信息",
			"filterConfig": "过滤设置",
			"energyEfficiency": "能效分析",
			"costAnalysis": "电费分析",
			"collect": "采集",
			"calculate": "计算",
			"select": "选择",
			"clear": "清空"
		},
		"energyconfig": {
			"addentry": "新增字典分类",
			"editentry": "编辑字典分类",
			"additem": "新增字典项目",
			"edititem": "编辑字典分类",
			"entry": "字典分类",
			"item": "字典项目",
			"itemId": "项目编码",
			"entryId": "编码",
			"entryName": "名称",
			"description": "描述",
			"entryItemId": "序号",
			"itemValue": "名称",
			"entryextendField1": "扩展1",
			"extendField1": "Ta",
			"extendField2": "Tb",
			"extendField3": "Tc",
			"extendField4": "Td",
			"extendField5": "Te"
		},
		"multidimensionalConfig": {
			"schemeName": "层级方案名称",
			"state": "状态",
			"addChildNode": "新增子节点",
			"indicatorName": "指标名称",
			"businessType": "业务分类",
			"isConfig": "是否完成配置"
		}
	},
	"notifyManagement": {
		"notifyHost": {
			"serverName": "通知器实例名称",
			"runState": "运行状态",
			"notifySet": "通知器设置",
			"notifyTest": "通知测试"
		},
		"notifyServer": {
			"name": "名称",
			"stationType": "局站类型",
			"alarmStandardName": "告警标准名",
			"delayTime": "延时(秒)",
			"retryCount": "重试次数",
			"eventStartTimeLower": "事件开始时间下限",
			"eventStartTimeUpper": "事件开始时间上限",
			"useEomsNotify": "使用EOMS派单通知",
			"eomsServer": "Eoms服务器",
			"eomsReceiver": "接受者",
			"useOtherNotify": "使用其他通知方式",
			"maskProjectState": "屏蔽工程状态事件(勾选屏蔽)",
			"alarmName": "告警名",
			"startTimeLower": "告警开始时间下限",
			"startTimeUpper": "告警开始时间上限",
			"equipmentLogicalClass": "设备逻辑分类",
			"alarmLogicalClass": "告警逻辑分类",
			"alarmLevel": "告警等级",
			"notifyType": "通知类型",
			"serverName": "服务器",
			"receiceAddress": "接收地址",
			"addFilter": "新建过滤条件",
			"alarmSequenceId": "告警SequenceId",
			"birthTime": "生成时间",
			"stationId": "局站ID",
			"stationName": "局站名称",
			"equipmentId": "设备ID",
			"equipmentName": "设备名称",
			"eventId": "告警ID",
			"eventConditionId": "告警条件ID",
			"eventUniqueId": "告警唯一ID",
			"eventName": "告警名称",
			"eventSeverity": "告警等级",
			"severityName": "告警等级名称",
			"eventStatus": "事件状态",
			"overturn": "告警翻转",
			"meaning": "告警含义",
			"startTime": "开始时间",
			"endTime": "结束时间",
			"confirmTime": "确认时间",
			"updateTimeFormat": "更新时间格式",
			"confirmUserId": "确认用户ID",
			"confirmUserName": "确认用户名称",
			"centerName": "中心名称",
			"stationState": "局站状态",
			"equipmentCategoryName": "设备类别名称",
			"equipmentVendorName": "设备供应商名称",
			"equipmentLogicCategory": "设备逻辑类别",
			"logicCategory": "逻辑类别",
			"subLogicCategory": "子逻辑类别",
			"infectionToEquipment": "对设备影响",
			"infectionToBusiness": "对业务影响",
			"standardAlarmName": "标准告警名称",
			"standardNameId": "标准告警ID",
			"alarmComment": "告警备注",
			"netAlarmId": "网络告警ID",
			"notifyServerId": "通知服务ID",
			"notificationType": "通知类型",
			"setting": "设置",
			"notificationRecieverId": "通知接收ID",
			"retryTimes": "重试次数",
			"eventFilterDelay": "事件过滤延迟",
			"notifyResult": "通知结果",
			"instructionId": "派单号",
			"eventStart": "开始事件",
			"eventEnd": "结束事件",
			"eventConfirm": "确认结束事件",
			"notifySuccess": "投递成功",
			"notifyFail": "通知失败",
			"renotifyFail": "追加通知失败",
			"batchExecution": "批量执行",
			"starttime": "开始时间（h）",
			"timeSlot": "执行周期（h）",
			"timeBound": "同步区间",
			"config": "自动补发参数设置",
			"chooseFilter": "选择筛选条件",
			"start": "开始",
			"end": "结束",
			"endConfirm": "结束并确认",
			"save": "保存",
			"saveSuccess": "保存成功",
			"saveFailed": "保存失败",
			"timeerror": "执行周期须小于同步区间",
			"startSuccess": "开启成功",
			"startFail": "开启失败",
			"all": "全部",
			"deleteSuccess": "删除成功",
			"deleteFail": "删除失败",
			"eventStartTime": "事件开始时间",
			"allLevel": "所有等级",
			"level1": "一级告警",
			"level2": "二级告警",
			"level3": "三级告警",
			"level4": "四级告警",
			"dian": "点",
			"timeFilterTitle": "二级时间区域告警过滤",
			"timeSpan": "选择时间段",
			"filterRule": "过滤规则"
		},
		"button": {
			"detailhostset": "查看或修改通知器设置",
			"start": "启动",
			"stop": "停止",
			"test": "测试",
			"close": "关闭",
			"save": "保存",
			"find": "查询",
			"delete": "删除",
			"execute": "执行",
			"modify": "修改",
			"confirm": "确认",
			"cancel": "取消"
		},
		"message": {
			"modifyNotifySet": "修改通知器设置",
			"startNotify": "确认启动该通知器?",
			"startSucceed": "启动成功",
			"startFailed": "启动失败",
			"stopNotify": "确认停止该通知器?",
			"stopSucceed": "停止成功",
			"stopFailed": "停止失败",
			"notStart": "该通知器已启动,请勿再操作",
			"notStop": "该通知器已停止,请勿再操作",
			"notTest": "该通知器已停止,请勿测试",
			"testSucceed": "测试成功",
			"testFailed": "测试失败",
			"sendSucceed": "发送成功",
			"alert": "正在操作，请勿退出当前页面...",
			"sendFailed": "发送过程异常",
			"timeNotNull": "请选择查询的时间段",
			"hasNull": "录入项不能为空",
			"deleteFilterRule": "删除该过滤规则",
			"saveFilterRule": "保存该过滤规则",
			"updateFilterRule": "修改该过滤规则",
			"serverAndReciverNotNull": "服务器和接收地址不能为空",
			"upperMustLargeDown": "告警开始时间上限必须大于下限"
		}
	},
	"linkmanagement": {
		"import": "导入",
		"export": "导出",
		"linkId": "链路Id",
		"linkName": "链路显示名称",
		"linkType": "链路类型",
		"local": "本地对象",
		"localObjectId": "本地对象Id",
		"localObject": "本地对象名称",
		"localObjectType": "本地对象类型",
		"localPort": "本地端口",
		"localModelPort": "本地3D模型端口",
		"remote": "远程对象",
		"remoteObjectId": "远程对象Id",
		"remoteObject": "远程对象名称",
		"remoteObjectType": "远程对象类型",
		"remotePort": "远程端口",
		"remoteModelPort": "远程3D模型端口",
		"staticPropertys": "静态属性",
		"remark": "备注",
		"addLink": "新增链路",
		"editLink": "编辑链路",
		"label": "属性",
		"value": "值",
		"currentEq": "当前选择: ",
		"localEmpty": "本地对象有空缺属性!",
		"remoteEmpty": "远程对象有空缺属性!",
		"linkNameEmpty": "链接名称未填写!",
		"staticEmpty": "静态属性有空缺属性!",
		"objectIdEqual": "本地对象和远程对象不能为同一个!",
		"updateSuccess": "更新成功",
		"updateError": "更新失败",
		"deleteLink": "确认删除链路 ",
		"linkTemp": "链路列表模板",
		"linkList": "链路列表"
	},
	"focussignal": {
		"over10000": "查询数量超过一万，请重新设置查询条件"
	},
	"videomessage": {
		"addSuccess": "添加成功",
		"saveSuccess": "保存成功",
		"deleteSuccess": "删除成功",
		"updateSuccess": "更新成功",
		"uploadSuccess": "上传成功",
		"modifySuccess": "修改成功",
		"syncSuccess": "同步成功",
		"addFail": "添加失败",
		"deleteFail": "删除失败",
		"uploadFail": "上传失败",
		"updateFail": "修改失败",
		"operationFiled": "操作失败",
		"pleaseAddViewOperate": "请添加视图之后操作！",
		"enumerate": {
			"0": "操作成功",
			"1001": "请求参数错误",
			"1002": "主键分配错误",
			"1003": "新增错误",
			"1004": "更新错误",
			"1005": "删除错误",
			"1006": "方法未实现",
			"1008": "名称已存在",
			"1009": "用户鉴权失败",
			"1010": "操作进行中",
			"1011": "大数据异常",
			"1012": "存在依赖",
			"1014": "IP端口和通道号重复",
			"1015": "流地址播放超时",
			"2001": "角色未找到错误",
			"2002": "用户未找到错误",
			"2003": "登录id重复",
			"2004": "账户别名错误",
			"4001": "发送控制失败",
			"4002": "签名为空",
			"4003": "时间戳为空",
			"4004": "随机数为空",
			"4005": "签名过期错误",
			"4006": "签名非法错误",
			"5001": "摄像头通道用户名密码错误",
			"5002": "摄像头通道打开失败",
			"5003": "摄像头通道参数错误",
			"5004": "找不到可用的流媒体服务器",
			"5005": "调用流媒体服务器失败",
			"5006": "摄像头通道网络不可达",
			"5007": "摄像头通道流地址找不到",
			"5008": "摄像头通道视频流已存在",
			"5009": "未解析的流媒体服务器错误",
			"5010": "摄像头通道已经删除",
			"-50401": "不允许删除已授权的人脸数据",
			"-50402": "不允许删除已授权的指纹数据"
		},
		"featureList": {
			"msePlayback": "基本播放器不可以在您的设备上运行",
			"mseLivePlayback": "HTTP MPEG2-TS/FLV 直播流不可以在您的设备上运行",
			"nativeMP4H264Playback": "您的设备不支持 H.264 MP4 视频文件",
			"nativeMP4H265Playback": "您的设备不支持 H.265 MP4 视频文件",
			"nativeWebmVP8Playback": "您的设备不支持 WebM VP8 视频文件",
			"nativeWebmVP9Playback": "您的设备不支持 WebM VP9 视频文件",
			"found": " _ 未找到播放源",
			"unable": " _ 网络错误,无法连接服务器",
			"reconnect": " _ 网络错误,正在重新连接…",
			"notDecipher": " _ 当前设备不支持解码视频",
			"mediaErrors": " _ 媒体错误，正在初始化播放器…"
		}
	},
	"faultRecording": {
		"tree": {
			"placeHolder": "输入关键字以检索"
		},
		"charts": {
			"effectiveValue": "有效值",
			"min": "最小值",
			"max": "最大值",
			"phaseAngle": "相位角",
			"preGroup": "上一组",
			"nextGroup": "下一组",
			"totalHarmonics": "总谐波",
			"evenHarmonics": "偶谐波",
			"oddHarmonics": "奇谐波"
		},
		"info": {
			"title": "故障录波",
			"startTime": "开始时间",
			"triggerTime": "触发时间",
			"recordingFrequency": "录波频率",
			"ratedFrequency": "额定频率",
			"symmetricalComponent": "对称分量",
			"positiveSequenceVoltage": "正序电压",
			"negativeSequenceVoltage": "负序电压",
			"zeroSequenceVoltage": "零序电压",
			"voltageNegativeSequenceUnbalance": "电压负序不平衡度",
			"voltageZeroSequenceUnbalance": "电压零序不平衡度",
			"positiveSequenceCurrent": "正序电流",
			"negativeSequenceCurrent": "负序电流",
			"zeroSequenceCurrent": "零序电流",
			"currentNegativeSequenceUnbalance": "电流负序不平衡度",
			"currentZeroSequenceUnbalance": "电流零序不平衡度",
			"timeRange": "请设置时间段",
			"lastWeek": "最近一周",
			"lastMonth": "最近一个月",
			"last3Months": "最近三个月",
			"last6Months": "最近半年",
			"lastYear": "最近一年",
			"custom": "自定义",
			"viewList": "查看列表",
			"showMode": "功能显示",
			"multiAxis": "多轴显示",
			"singleAxis": "单轴显示",
			"harmonic": "谐波显示",
			"rms": "RMS显示",
			"compSetting": "控件设置",
			"paramSelect": "参数选择",
			"channelNaming": "通道命名",
			"printSetting": "打印设置",
			"vectorGraphicsSetting": "矢量图设置",
			"export": "导出",
			"exportImg": "导出图片",
			"exportData": "导出数据",
			"connectTriangle": "连接成三角形",
			"showVectorLine": "显示矢量直线"
		},
		"table": {
			"no": "序号",
			"triggerTime": "触发时间",
			"eqName": "设备名称",
			"eqPosition": "设备位置"
		},
		"modal": {
			"vectorConnection": "矢量连接设置",
			"vectorLine": "矢量直线设置",
			"groupSetting": "分组显示设置",
			"showCurrent": "显示电流",
			"showVoltage": "显示电压"
		}
	}
}