import { Component, Input, OnInit } from '@angular/core';
import { ToastService } from '@app/communal/providers';
import { CoreeventseverityService } from '@app/communal/providers/coreeventseverity.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import * as _ from 'lodash';

@Component({
    selector: 'app-alarm-reason-modal',
    templateUrl: './alarm-reason-modal.component.html',
    styleUrls: ['./alarm-reason-modal.component.scss']
})
export class AlarmReasonConfigModalComponent implements OnInit {

    @Input()data: any;

    constructor(
        private activeModal: NgbActiveModal,
        private toast: ToastService,
        private coreeventseverityService: CoreeventseverityService,
        private translate: TranslateService) { }

    ngOnInit() {

    }

    close(result?: any) {
        this.activeModal.close(result);
    }
}