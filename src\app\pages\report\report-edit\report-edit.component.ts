
import { fork<PERSON>oin } from 'rxjs';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { FindReportCompService } from '../service/find-report-comp.service';
import { Location } from '@angular/common';
import { BaseCRUDService } from '@app/communal/providers/baseCRUDService/baseCRUDService';
import { HttpClient } from '@angular/common/http';
import { ReportModel } from '../model/report.model';
import { IOption } from 'ng-select';
import { ToastService } from '@app/communal/providers';
import { TranslateService } from '@ngx-translate/core';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { AbstractControl, UntypedFormGroup, UntypedFormBuilder, Validators } from '@angular/forms';
import { TreeSelectorComponent } from '@app/communal/components/tree-selector/tree-selector.component';
import { IndicatorSelectorComponent } from '@app/communal/components/indicator-selector/indicator-selector.component';
import { TreeEventSelectorComponent } from '@app/communal/components/tree-event-selector/tree-event-selector.component';
import { RackPointSelector } from '@app/communal/components/rack-point-selector/rack-point-selector.component';
import { NewRackSelectorComponent } from '@app/communal/components/new-rack-selector/new-rack-selector.component';
import { DevicePointSelectorComponent } from '@app/communal/components/device-point-selector/device-point-selector.component';
import { TreeEquipmentSelectorComponent } from '@app/communal/components/tree-equipment-selector/tree-equipment-selector.component';
import { BasicSignalSelectorComponent } from '@app/communal/components/basic-signal-selector/basic-signal-selector.component';
import { TreeAirSelectorComponent } from '@app/communal/components/tree-aircondition-selector/tree-aircondition-selector.component';
import { I } from '@angular/cdk/keycodes';
import { SessionService } from '@app/core/services/session.service';
import { HierarchySelectorComponent } from '@app/communal/components/hierarchy-selector/hierarchy-selector.component';
import { isArray } from 'jquery';
import { EquipmentCategorySelectorComponent } from '@app/communal/components/equipment-category-selector/equipment-category-selector.component';
import { GlobalState } from '@app/global.state';
@Component({
    selector: 'report-edit',
    templateUrl: 'report-edit.component.html',
    styleUrls: ['report-edit.component.scss']
})

export class ReportEditComponent extends BaseCRUDService implements OnInit {
    // 查询参数parameterControlId与控件对应关系
    // 1 -- 开始时间日期选择控件  2 -- 结束时间日期选择控件  3 -- 设备类型选择控件  4 -- 设备选择控件  5-- 测点选择控件  6 -- 时间粒度选择控件

    reportDetailData: ReportModel = new ReportModel();
    langKeys: Array<string> = [
        'ngForm.update.success',
        'ngForm.update.error'
    ];
    langValues: any;
    selectOptionDataObj: any;
    backendSelectorOption: any;
    selectRequestOriginList: Array<any>;
    selectDeviceList: Array<any> = [];
    selectEqCategoryList: Array<any> = [];
    selectAirGroupList: Array<any> = [];
    selectAirStationList: Array<any> = [];
    selectCorePointList: Array<any> = [];
    selectIndicatorList: Array<any> = [];
    selectRackPointList: Array<any> = [];
    selectRackList: Array<any> = [];
    selectEventList: Array<any> = [];
    selectBasicTypeList: Array<any> = [];
    startTimeControl: AbstractControl;
    endTimeControl: AbstractControl;
    form: UntypedFormGroup;
    options: any;
    ngFormValid = true;
    formControls: any = {};
    selectedData: any = {};

    reportOpen = true;
    isCreateUser: boolean = false;

    regex = /^(?!'|\u2018|\u2019)(^((?!\\|\/|:|\?|\*|\[|\]).){1,30}$)(?<!'|\u2018|\u2019)$/;

    //监听报表15的搜索模式来切换显示内容
    showHierarchy: boolean = false;
    showEquipment: boolean = false;
    showEquipmentCategory: boolean = false;
    eqcIDs: any;

    constructor(private activatedRoute: ActivatedRoute,
        private modal: NgbModal,
        private reportService: FindReportCompService,
        private location: Location,
        private http: HttpClient,
        private toast: ToastService,
        private sessionService: SessionService,
        private translate: TranslateService,
        private formBuilder: UntypedFormBuilder,
        private _state: GlobalState,
    ) {
        super(http, 'reports')
    }

    ngOnInit() {
        this.initTranslate();
        this.activatedRoute.params.subscribe(param => {
            if (param['id']) {
                this.initData(param['id']);
            }
        })
    }

    createUser() {
        let currentUser = this.sessionService.getUserId();
        if (currentUser == this.reportDetailData.createUserId || this.reportDetailData.createUserId == null) {
            this.isCreateUser = true;
        }
    }

    initTranslate() {
        this.translate.get(this.langKeys).subscribe(values => {
            this.langValues = values;
        })
    }

    initControlOptions(timeList) {
        const obj = {};

        timeList.forEach((item) => {
            Object.assign(obj, { [item['reportSchemaQueryParameter']['reportSchemaQueryParameterName']]: ['', Validators.compose([Validators.required])] });
        });
        this.form = this.formBuilder.group(obj);
        timeList.forEach((item) => {
            this.formControls[item['reportSchemaQueryParameter']['reportSchemaQueryParameterName']] = this.form.controls[item['reportSchemaQueryParameter']['reportSchemaQueryParameterName']];
        });
        let lang = localStorage.getItem('lang');
        if (lang == 'zh') {
            this.options = DateUtil.getDatePickerOptions();
        } else {
            this.options = DateUtil.getTranslateDatePickerOptions(this.translate);
        }
    }

    initData(reportId) {
        forkJoin([this.getAll('reports/' + reportId)]).subscribe(res => {
            this.reportDetailData = res[0];
            if (this.reportDetailData) {
                this.reportDetailData.reportParameterPresetList.forEach(item => {
                    let name = 'reportSchema.queryParameter.param' + item['reportSchemaQueryParameter']['reportSchemaQueryParameterId'];
                    item['reportSchemaQueryParameter']['reportSchemaQueryParameterTitle'] = this.translate.instant(name);
                })

                if (this.reportDetailData.reportExportParameterPresetList.length > 0) {
                    this.reportDetailData.reportExportParameterPresetList.forEach(item => {
                        let name = 'reportSchema.exportParameter.param' + item['reportSchemaExportParameter']['reportSchemaExportParameterId'];
                        item['reportSchemaExportParameter']['reportSchemaExportParameterTitle'] = this.translate.instant(name);
                    })
                }

                if (this.reportDetailData.overt) {
                    this.reportOpen = true;
                } else {
                    this.reportOpen = false;
                }

                const timeList = this.reportDetailData.reportParameterPresetList.filter(obj => obj.reportSchemaQueryParameter.parameterControlId === '1' || obj.reportSchemaQueryParameter.parameterControlId === '2');
                if (timeList.length > 0) this.initControlOptions(timeList);
                this.parseReportDetailData(this.reportDetailData);
                this.getSelectRequestList(this.reportDetailData);
                this.createUser();
                if (this.reportDetailData.reportSchemaId === 15) {
                    let paramList = this.reportDetailData.reportParameterPresetList;
                    let index = paramList.findIndex(item => item.reportSchemaQueryParameterId === 280);
                    if (paramList[index] && paramList[index].value) {
                        this.checkType(paramList[index]);
                    }
                }
            }
        });
    }

    parseReportDetailData(data: ReportModel) {
        if (data && data.reportParameterPresetList) {
            //接收的数据格式化
            // selectEventList处理选择的事件
            const selectEventList = data.reportParameterPresetList.filter(item => item.reportSchemaQueryParameter.reportSchemaQueryParameterName === 'signalIds' || item.reportSchemaQueryParameter.reportSchemaQueryParameterName === 'eventIds');
            if (selectEventList.length > 0) {
                if (selectEventList[0].value === "") {
                    selectEventList[0].value = "[]";
                }
                this.selectEventList = JSON.parse(selectEventList[0].value);
            }
        }
    }

    getSelectRequestList(data) {
        if (data && data.reportParameterPresetList) {
            const selectWidgetData = {},
                arr = [];
            const selectWidgetList = data.reportParameterPresetList.filter(item => +item.reportSchemaQueryParameter.parameterControlId === 3);
            if (selectWidgetList && selectWidgetList.length > 0) {
                selectWidgetList.forEach(param => {
                    const obj = {};
                    obj['multipleBool'] = false;
                    obj['value'] = param.value && param.value.split(',');
                    const dataSourceExpression = param.reportSchemaQueryParameter.dataSourceExpression,
                        labelValue = JSON.parse(param.reportSchemaQueryParameter.dataSourceReturnTableName);
                    const pairs: Array<string> = dataSourceExpression.split(";");
                    let dataSource: string;
                    pairs.map((item: string) => {
                        if (item === "type=multiple") {
                            obj['multipleBool'] = true;
                        } else {
                            dataSource = item;
                        }
                    })
                    const index = dataSource.indexOf('=');
                    const sourceStr = [];
                    sourceStr[0] = dataSource.slice(0, index);
                    if (sourceStr[0] === 'json') {
                        let name = 'reportSchema.selectOption.option' + param.reportSchemaQueryParameter.reportSchemaQueryParameterId;
                        let translateStr = this.translate.instant(name);
                        sourceStr[1] = translateStr.replaceAll("\'", "\"");
                    } else {
                        sourceStr[1] = dataSource.slice(index + 1);
                    }
                    obj['sourceType'] = sourceStr[0] || '';
                    obj['labelKey'] = labelValue['display'] || '';
                    obj['valueKey'] = labelValue['value'] || '';
                    obj['dataSource'] = obj['sourceType'] === 'json' ? this.parseSelectOptionData(JSON.parse(sourceStr[1]), obj['valueKey'], obj['labelKey']) : sourceStr[1];
                    obj['name'] = param.reportSchemaQueryParameter.reportSchemaQueryParameterName;
                    selectWidgetData[param.reportSchemaQueryParameter.reportSchemaQueryParameterName] = obj;
                    arr.push(obj);
                })
                this.selectRequestOriginList = arr.filter(item => item.sourceType === 'api');
                if (this.selectRequestOriginList && this.selectRequestOriginList.length > 0) {
                    const requestList = this.selectRequestOriginList.map(item => this.getAll(item.dataSource));
                    this.requestSelectData(requestList, selectWidgetData);
                } else {
                    this.selectOptionDataObj = selectWidgetData;
                }
                const selectorBeta = data.reportParameterPresetList.filter(item => +item.reportSchemaQueryParameter.parameterControlId === 18);
                if (selectorBeta && selectorBeta.length > 0) {
                    this.http.get('dataitems?entryId=7').subscribe(res => {
                        let list = [];
                        if (res && isArray(res) && res.length > 0) {
                            res.forEach(item => {
                                const obj = {
                                    label: item.itemValue,
                                    value: item.itemId
                                }
                                list.push(obj);
                            })
                        }
                        this.backendSelectorOption = list;
                    })
                    this.eqcIDs = JSON.parse(selectorBeta[0].value).objectId;
                    let ids = [];
                    if (this.eqcIDs && this.eqcIDs.length > 0) {
                        ids = this.eqcIDs.split(',');
                        ids = ids.map(obj => Number(obj));
                    }
                    let index = this.reportDetailData.reportParameterPresetList.findIndex(thg => Number(thg.reportSchemaQueryParameter.parameterControlId) === 18);
                    this.reportDetailData.reportParameterPresetList[index].value = ids;
                }
            }
            const selectDevice = data.reportParameterPresetList.filter(item => +item.reportSchemaQueryParameter.parameterControlId === 4);
            if (selectDevice && selectDevice.length > 0) {
                this.selectDeviceList = selectDevice[0].value ? JSON.parse(selectDevice[0].value) : [];
            }
        }
    }

    updateEqc(item) {
        if (item.value.length > 0) {
            this.eqcIDs = item.value.join(',');
        } else {
            this.eqcIDs = '';
        }
    }

    checkType(item) {
        if (this.reportDetailData.reportSchemaId !== 15) return;
        if (item.value === '1') {
            this.showHierarchy = true;
            this.showEquipment = false;
            this.showEquipmentCategory = false;
        }
        if (item.value === '2') {
            this.showHierarchy = false;
            this.showEquipment = true;
            this.showEquipmentCategory = false;
        }
        if (item.value === '3') {
            this.showHierarchy = false;
            this.showEquipment = false;
            this.showEquipmentCategory = true;
        }
        if (!item.value) {
            this.showHierarchy = false;
            this.showEquipment = false;
            this.showEquipmentCategory = false;
        }
    }

    requestSelectData(list: any[], data) {
        forkJoin(list).subscribe(res => {
            res.forEach((item, index) => {
                this.selectRequestOriginList[index].dataSource = item;
            })
            this.setSelectOptionDataList(this.selectRequestOriginList, data);
        })
    }

    setSelectOptionDataList(list: any[], data) {
        for (const item in data) {
            const findData = list.find(s => s.name === data[item].name);
            if (findData) {
                data[item].dataSource = findData['dataSource'];
                data[item].dataSource = this.parseSelectOptionData(data[item].dataSource, data[item].valueKey, data[item].labelKey)
            }
        }
        this.selectOptionDataObj = data;
    }

    parseSelectOptionData(data, valueStr: string, lableStr: string) {
        const sources = Array<IOption>();
        if (data) {
            data.forEach(element => {
                const sourceItem: IOption = {
                    value: String(element[valueStr]),
                    label: element[lableStr]
                };
                sources.push(sourceItem);
            });
        }
        return sources;
    }

    setTime(value, param) {
        param.value = DateUtil.dateToString(value.picker.startDate['_d']);
        if (this.reportDetailData.maxQueryInterval !== null && this.reportDetailData.maxQueryInterval !== undefined) {
            let startDate = new Date(this.reportDetailData.reportParameterPresetList[0].value);
            let endDate = new Date(this.reportDetailData.reportParameterPresetList[1].value);
            let diffHour = (endDate.getTime() - startDate.getTime()) / 1000 / 60 / 60;

            if (!Number.isNaN(diffHour)) {
                if (this.reportDetailData.maxQueryInterval < Math.abs(diffHour)) {
                    this.toast.showToast('error', this.translate.instant('ngForm.create.timeIntervalError') + '(' + this.reportDetailData.maxQueryInterval + this.translate.instant('ngForm.create.hour') + ')', null);
                    param.value = '';
                }
            }
        }
    }

    clearTime(param) {
        param.value = null;
    }

    getCurrentParamVal(paramName: string) {
        let value = null;
        if (this.selectOptionDataObj) {
            const param = this.selectOptionDataObj[paramName];
            value = param && param.value;
        }
        return value;
    }

    selectDevice(param) {
        const expression = param && param.reportSchemaQueryParameter && param.reportSchemaQueryParameter.dataSourceExpression;
        let modal;
        if (expression && expression.indexOf('tree') > -1) {
            modal = this.modal.open(DevicePointSelectorComponent, { backdrop: 'static' });
        } else {
            modal = this.modal.open(TreeEquipmentSelectorComponent, { backdrop: 'static' });
            modal.componentInstance.multipleBool = expression && expression.indexOf('multiple') > -1 ? true : false;
            if (this.reportDetailData.reportSchemaId == 3 || this.reportDetailData.reportSchemaId == 39) {
                let bsList = this.reportDetailData.reportParameterPresetList.find(item =>
                    item.reportSchemaQueryParameterId == 150 || item.reportSchemaQueryParameterId == 263
                )
                if (bsList.value) {
                    this.selectBasicTypeList = JSON.parse(bsList.value);
                }
                modal.componentInstance.baseSignalTypes = this.selectBasicTypeList;
            }
            const deviceCategoryId = this.getCurrentParamVal('baseEquipmentIds');
            if (deviceCategoryId) {
                modal.componentInstance.filterDeviceCategoryId = +deviceCategoryId;
            }
        }
        if (param.value) {
            this.selectDeviceList = JSON.parse(param.value);
        }
        modal.componentInstance.selectDeviceList = this.selectDeviceList;

        modal.result.then((res) => {
            if (res) {
                this.selectDeviceList = res;
                param.value = res.length > 0 ? JSON.stringify(res) : null;
            }
        });
    }

    clearDeviceList(param) {
        this.selectDeviceList = [];
        param.value = null;
    }
    selectEquipmentCategory(param) {
        this.selectEqCategoryList = param.value ? JSON.parse(param.value) : {};
        const modal = this.modal.open(EquipmentCategorySelectorComponent, { backdrop: 'static' });
        modal.componentInstance.multipleBool = true;
        modal.componentInstance.equipmentCategories = this.selectEqCategoryList && this.selectEqCategoryList
        modal.result.then((res) => {
            if (res) {
                this.selectEqCategoryList = res;
                param.value = res && res.length > 0 ? JSON.stringify(res) : null;
            }
        })
    }
    clearEquipmentCategoryList(param) {
        this.selectEqCategoryList = [];
        param.value = null;
    }
    selectGroup(param, parameterControlId) { // 群控分组控件
        let parameterId = Number(parameterControlId)
        parameterId === 15 && this.setSelectGroup(param, 'selectAirGroupList', parameterId, 'communalComponent.pointSelector.selectDevice', 'virtualEquipmentId', 'groupName')
        parameterId === 16 && this.setSelectGroup(param, 'selectAirStationList', parameterId, 'communalComponent.pointSelector.selectStation', 'stationId', 'stationName')
    }
    setSelectGroup(param, selectListName, parameterControlId, title, chosedChildId, chosedChildName) {
        const expression = param && param.reportSchemaQueryParameter && param.reportSchemaQueryParameter.dataSourceExpression;
        let modal = this.modal.open(TreeAirSelectorComponent, { backdrop: 'static' });
        modal.componentInstance.airSelectorTitle = title
        modal.componentInstance.chosedChildId = chosedChildId
        modal.componentInstance.chosedChildName = chosedChildName
        modal.componentInstance.multipleBool = expression && expression.indexOf('multiple') > -1 ? true : false;
        modal.componentInstance.parameterControlId = parameterControlId
        modal.componentInstance.saveParentNodeIdsNames = parameterControlId === 15 ? 'saveDeviceList' : 'saveStationList'

        if (param.value) {
            this[selectListName] = JSON.parse(param.value);
        }
        console.log(this[selectListName], '回显的');
        modal.componentInstance.selectAirGroupList = this[selectListName];

        modal.result.then((res) => {
            if (res) {
                this[selectListName] = res;
                const list = [];
                this[selectListName].forEach(item => {
                    const obj = {
                        nodeId: item.nodeId,
                        nodeName: item.nodeName,
                        position: item.position,
                        positionId: item.positionId
                    }
                    obj[chosedChildId] = item[chosedChildId]
                    obj[chosedChildName] = item[chosedChildName]
                    list.push(obj)
                })
                param.value = res.length > 0 ? JSON.stringify(list) : null;
            }
        });
    }
    clearGroupList(param, parameterControlId) {
        Number(parameterControlId) === 15 ? this.selectAirGroupList = [] : this.selectAirStationList = []
        param.value = null
    }
    selectEvent(param) {
        let modal;
        if (param.dataSourceExpression && param.dataSourceExpression.indexOf('tree') > -1) {
            modal = this.modal.open(DevicePointSelectorComponent, { backdrop: 'static' });
        } else {
            modal = this.modal.open(TreeEventSelectorComponent, { backdrop: 'static' });
        }
        modal.componentInstance.selectEventList = this.selectEventList;

        modal.result.then(data => {
            if (data) {
                this.selectEventList = data;
                const eventList = [];
                data.forEach(item => {
                    const obj = {
                        eventId: item.eventId,
                        eventIds: item.eventIds,
                        eventName: item.eventName,
                        eqId: item.equipmentId,
                        eqName: item.equipmentName,
                        equipmentId: item.equipmentId,
                        equipmentName: item.equipmentName,
                        title: item.title,
                        position: item.position,
                        positionId: item.positionId,
                        key: item.key
                    };
                    eventList.push(obj);
                })
                param.value = data.length > 0 ? JSON.stringify(eventList) : null;
            }
        })
    }

    clearEventList(param) {
        param.value = null;
        this.selectEventList = [];
    }

    selectPoint(param) {
        const expression = param && param.reportSchemaQueryParameter && param.reportSchemaQueryParameter.dataSourceExpression;
        let modal;
        if (expression && expression.indexOf('tree') > -1) {
            modal = this.modal.open(DevicePointSelectorComponent, { backdrop: 'static' });
        } else {
            modal = this.modal.open(TreeSelectorComponent, { backdrop: 'static' });
            if (expression && expression.length > 0) {
                const strList = expression.split('&');
                strList.forEach(str => {
                    if (str.indexOf('standardId') > -1) {
                        const standardId = str.split('=')[1];
                        modal.componentInstance.standardId = standardId;
                    }
                })
                modal.componentInstance.expandDeviceBol = expression.indexOf('expand') > -1 ? false : true;
            }
        }
        if (param.value) {
            this.selectCorePointList = JSON.parse(param.value);
        }
        modal.componentInstance.selectCorepointList = this.selectCorePointList;

        modal.result.then(data => {
            if (data) {
                console.log(data)
                this.selectCorePointList = data;
                param.value = data.length > 0 ? JSON.stringify(data) : null;
            }
        })
    }

    clearCorePointList(param) {
        param.value = null;
        this.selectCorePointList = [];
    }

    selectIndicator(param) {
        const modal = this.modal.open(IndicatorSelectorComponent, { backdrop: 'static' });
        if (param.value) {
            this.selectIndicatorList = JSON.parse(param.value);
        }
        modal.componentInstance.selectedIndicatorList = this.selectIndicatorList;
        modal.result.then(data => {
            if (data) {
                this.selectIndicatorList = data;
                param.value = data.length > 0 ? JSON.stringify(data) : null;
            }
        })
    }

    clearIndicatorList(param) {
        param.value = null;
        this.selectIndicatorList = [];
    }

    selelctRackPoint(param) {
        const expression = param && param.reportSchemaQueryParameter && param.reportSchemaQueryParameter.dataSourceExpression;
        const modal = this.modal.open(RackPointSelector, { backdrop: 'static' });
        if (param.value) {
            this.selectRackPointList = JSON.parse(param.value);
        }
        modal.componentInstance.selectedPointList = this.selectRackPointList;
        modal.componentInstance.selectCloumnCabinetBol = expression && expression.indexOf('columnCabinet') > -1 ? true : false;
        modal.result.then(data => {
            if (data) {
                this.selectRackPointList = data;
                const list = [];
                data.forEach(item => {
                    const obj = {
                        corePointId: item.corePointId,
                        pointName: item.pointName
                    };
                    list.push(obj);
                })
                param.value = data.length > 0 ? JSON.stringify(list) : null;
            }
        })
    }

    clearRackPointList(param) {
        param.value = null;
        this.selectRackPointList = [];
    }

    selectRack(param) {
        const modal = this.modal.open(NewRackSelectorComponent, { backdrop: 'static' });
        if (param.value) {
            this.selectRackList = JSON.parse(param.value);
        }
        modal.componentInstance.selectedList = this.selectRackList;
        modal.result.then(data => {
            if (data) {
                this.selectRackList = data;
                param.value = data.length > 0 ? JSON.stringify(data) : null;
            }
        })
    }

    clearRackList(param) {
        param.value = null;
        this.selectRackList = [];
    }

    selectBasicSignal(param) {
        const modal = this.modal.open(BasicSignalSelectorComponent, { backdrop: 'static' });
        let baseEquipmentIds;
        if (this.selectDeviceList.length) {
            const ids = this.selectDeviceList.flatMap(({ eqId }) => eqId !== null ? [eqId] : []).join(',');
            this.http.get(`equipmentbasetypes?equipmentIds=${ids}`).subscribe(
                (res: any) => {
                    baseEquipmentIds = res.flatMap(({ baseEquipmentId }) => baseEquipmentId !== null ? [baseEquipmentId] : []);
                    modal.componentInstance.filterBaseEquipmentList = [...new Set(baseEquipmentIds)];
                });
        }
        if (param.value) {
            this.selectBasicTypeList = JSON.parse(param.value);
        }
        modal.componentInstance.selectedList = this.selectBasicTypeList;
        modal.componentInstance.isReport = true;
        modal.componentInstance.multipleBool = true;
        modal.result.then(data => {
            if (data) {
                this.selectBasicTypeList = data;
                param.value = data.length > 0 ? JSON.stringify(data) : null;
            }
        })
    }

    clearBasicSignal(param) {
        param.value = null;
        this.selectBasicTypeList = [];
    }

    selectHierarchy(param) {
        this.selectedData = JSON.parse(param.value);
        const modal = this.modal.open(HierarchySelectorComponent, { backdrop: 'static' });
        modal.componentInstance.multipleBool = true;
        modal.componentInstance.objectId = this.selectedData && this.selectedData['objectId'] || [];
        modal.componentInstance.objectType = this.selectedData && this.selectedData['objectType'] || [];
        modal.componentInstance.objectPosition = this.selectedData && this.selectedData['objectPosition'] || [];
        modal.componentInstance.objectName = this.selectedData && this.selectedData['objectName'] || [];

        modal.result.then((res: {
            objectId: any[],
            objectType: number[],
            objectPosition: any[],
            objectName: any[],
            objectResult: any
        }) => {
            if (res) {
                console.log(res)
                this.selectedData = res;
                param.value = res && res['objectId'].length > 0 ? JSON.stringify(res) : null;
            }
        })
    }

    clearHierarchy(param) {
        param.value = null;
        this.selectedData = {};
    }

    selectField(item) {
        item.selected = !item.selected;
    }

    getFileObj(param): any {
        let fileObj: any = null;
        if (param.value) {
            const url = param.value.split('/');
            fileObj = {};
            fileObj['filePath'] = url[0];
            fileObj['fileName'] = url[1];
        }
        return fileObj;
    }

    getFilePath(file, param) {
        if (!file) return;
        param.value = file.data['filePath'] + '/' + file.data['fileName'];
    }

    clearFile(param) {
        param.value = null;
    }

    isValidOfQueryParam(): boolean {
        let valid = true;
        for (const item of this.reportDetailData.reportParameterPresetList) {
            if ((!item.reportSchemaQueryParameter.isNull) && (!item.value) && (!item.display)) {
                valid = false;
                break;
            }
        }
        return valid;
    }

    returnPrevious() {
        this.location.back();
    }

    submit(formValid) {
        this.ngFormValid = formValid;
        if (!this.ngFormValid || !this.isValidOfQueryParam()) {
            return;
        }
        if (!this.regex.test(this.reportDetailData.reportName)) {
            this.toast.showToast('error', this.translate.instant('ngForm.create.illegalName'), null)
            return;
        }
        this.reportDetailData.reportParameterPresetList.forEach(item => {
            // 数据传输前的格式化
            if (item.reportSchemaQueryParameter.parameterControlId === '3') {
                const value = item.value = this.selectOptionDataObj && this.selectOptionDataObj[item.reportSchemaQueryParameter.reportSchemaQueryParameterName] && this.selectOptionDataObj[item.reportSchemaQueryParameter.reportSchemaQueryParameterName].value;
                if (Array.isArray(value)) {
                    item.value = value.join(',');
                } else {
                    item.value = value;
                }
            }
        })

        // if(!this.reportOpen) {
        //     const id = this.sessionService.getUserId();
        //     this.reportDetailData.createUserId = id;
        // }else {
        //     this.reportDetailData.createUserId = null;
        // }
        this.reportDetailData.overt = this.reportOpen;
        if (this.reportDetailData.reportSchemaId === 15) {
            let index = this.reportDetailData.reportParameterPresetList.findIndex(item => item.reportSchemaQueryParameter.reportSchemaQueryParameterId === 282);
            if (index >= 0) {
                if (this.eqcIDs && this.eqcIDs.length > 0) {
                    this.reportDetailData.reportParameterPresetList[index].value = JSON.stringify({
                        objectId: this.eqcIDs
                    })
                } else {
                    this.reportDetailData.reportParameterPresetList[index].value = JSON.stringify({
                        objectId: []
                    })
                }
            }
        }

        this.update(this.reportDetailData).subscribe(res => {
            this.toast.showToast('success', this.langValues['ngForm.update.success'], null);
            this._state.notifyDataChanged('refreshReportTree');
            this.location.back();
        }, () => {
            this.toast.showToast('error', this.langValues['ngForm.update.error'], null)
        })

        // if (this.reportDetailData.maxQueryInterval !== null) {
        //     let startDate = new Date(this.reportDetailData.reportParameterPresetList[0].value);
        //     let endDate = new Date(this.reportDetailData.reportParameterPresetList[1].value);
        //     let diffHour = (endDate.getTime() - startDate.getTime()) / 1000 / 60 / 60;
        //     if (this.reportDetailData.maxQueryInterval < diffHour || Number.isNaN(diffHour)) {
        //         this.toast.showToast('error', this.translate.instant('ngForm.create.timeIntervalError') + '(' + this.reportDetailData.maxQueryInterval + ' hours' + ')', null)
        //     } else {
        //         this.update(this.reportDetailData).subscribe(res => {
        //             this.toast.showToast('success', this.langValues['ngForm.update.success'], null);
        //             this.location.back();
        //         }, () => {
        //             this.toast.showToast('error', this.langValues['ngForm.update.error'], null)
        //         })
        //     }
        // } else {
        //     this.update(this.reportDetailData).subscribe(res => {
        //         this.toast.showToast('success', this.langValues['ngForm.update.success'], null);
        //         this.location.back();
        //     }, () => {
        //         this.toast.showToast('error', this.langValues['ngForm.update.error'], null)
        //     })
        // }
    }

}
