import { Component, ElementRef, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-alarmconfig',
  templateUrl: './alarmconfig.component.html',
  styleUrls: ['./alarmconfig.component.scss']
})
export class AlarmConfigComponent implements OnInit {

  langKeys = [
    'featuremanage.alarmconfig.eventSeverityConfig',
    'featuremanage.alarmconfig.eventReason'
  ];
  languageValues: any = [];

  rootJson: any = [];

  constructor(
    public router: Router,
    public activatedRoute: ActivatedRoute,
    private translateService: TranslateService,
    public element: ElementRef
  ) {
  }

  ngOnInit() {
    this.initTranslateMessage();
  }

  initTranslateMessage(): void {
    this.translateService.get(this.langKeys).subscribe(res => {
      this.languageValues = res;
      this.initData();
    });
  }

  initData() {
    const _url = this.router.url;
    const _json = [
      { name: this.languageValues['featuremanage.alarmconfig.eventSeverityConfig'], url: '/entry/featuremanage/monitor/alarmconfig', select: true },
      { name: this.languageValues['featuremanage.alarmconfig.eventReason'], url: '/entry/featuremanage/monitor/alarmconfig/alarm-reason', select: false }
    ];
    _json.forEach(item => {
      if (item['url'] === _url) {
        item['select'] = true;
      } else {
        item['select'] = false;
      }
    });
    this.rootJson = _json;
  }

  goRoute(url, index) {
    const _url = url || '';
    this.selectRouter(index);
    this.router.navigate([_url], {});
  }

  selectRouter(index) {
    for (let i = 0; i < this.rootJson.length; i++) {
      const item = this.rootJson[i];
      if (index === i) {
        item['select'] = true;
      } else {
        item['select'] = false;
      }
    }
  }

}
