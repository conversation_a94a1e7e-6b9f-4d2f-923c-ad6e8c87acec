<div class="report-tree-container m-3">
  <!-- 使用flex布局控制侧边栏和主内容区 -->
  <div class="d-flex w-100">
    <nz-sider [nzWidth]="400" [nzCollapsed]="isCollapsed" [nzCollapsedWidth]="0" nzTrigger="null">
      <!-- 将折叠按钮放在侧边栏内部右侧边缘 -->
      <div class="sider-collapse-btn" (click)="isCollapsed = !isCollapsed">
        <i *ngIf="isCollapsed" class="iconfont icon-show-l unfold"></i>
        <i *ngIf="!isCollapsed" class="iconfont icon-show-l fold"></i>
      </div>

      <ba-card baCardClass="viewport100 with-scroll border-top-radius-none" class="w-100">
        <div>
          <div class="d-flex justify-content-between m-3">
            <button class="btn btn-primary" (click)="openGroup('add')"
              [disabled]="checkData && checkData['type'] === 'report'">
              <span class="iconfont icon-add"></span>
              {{'report.common.addGroup' | translate}}
            </button>
            <button class="btn btn-primary" (click)="openTemplate('add')"
              [disabled]="((!checkData) || (checkData && checkData['type'] === 'report'))">
              <span class="iconfont icon-add"></span>
              {{'report.common.addTemplate' | translate}}
            </button>
          </div>

          <div class="report-tree-search phoenix-search m-3">
            <input type="text" class="form-control" name="searchText"
              placeholder="{{ 'settings.placeholder' | translate }}" [(ngModel)]="searchText"
              (ngModelChange)="search()" />
            <span class="input-group-addon"><i class="ion-ios-search-strong"></i></span>
          </div>
          <div class="nz_tree">
            <nz-tree [nzData]="nodes" #nzTreeReport (nzClick)="onNodeSelect($event)" nzVirtualHeight="670px" nzDraggable
              nzBlockNode (nzOnDrop)="nzDrop($event)">
              <ng-template #nzTreeTemplate let-node let-origin="origin">
                <span class="tree-node-content">
                  <span class="node-title" [title]="origin.title">
                    <span *ngIf="origin.type === 'folder'" class="iconfont icon-directory"></span>
                    <span *ngIf="origin.type === 'report'" class="iconfont icon-tubiao"></span>
                    {{ origin.title }}</span>
                  <span class="node-actions">
                    <span *ngIf="origin.key && origin.reportType == 5"
                      title="{{ 'general.common.download' | translate }}"
                      (click)="downloadRealReport($event,origin.id,child.reportParameterPresetList)"
                      class="iconfont icon-move-down"></span>
                    <span *ngIf="origin.key && origin.reportType == 6"
                      title="{{ 'general.common.download' | translate }}" (click)="downloadReport($event,origin.id)"
                      class="iconfont icon-move-down"></span>
                    <span *ngIf="origin.key" title="{{ 'general.common.edit' | translate }}"
                      (click)="editGroup('edit', $event, origin)" class="iconfont icon-edit1 ml-1"></span>
                    <span *ngIf="origin.key && node.isLeaf" title="{{ 'general.common.delete' | translate }}"
                      (click)="deleteGroup($event, origin)" class="iconfont icon-delete2 ml-1"></span>
                  </span>
                </span>
              </ng-template>
            </nz-tree>
          </div>
        </div>
      </ba-card>
      <!-- 添加底部操作按钮区域 -->
      <div class="tree-actions" *ngIf="checkData">
        <button class="btn btn-primary" (click)="openHierarchyEdit(checkData)">
          <span class="iconfont icon-edit1"></span>
          {{'report.common.editHierarchy' | translate}}
        </button>
        <button class="btn btn-primary ml-2" (click)="editGroup('edit', $event, checkData)">
          <span class="iconfont icon-edit1"></span>
          {{'general.common.edit' | translate}}
        </button>
        <button class="btn btn-danger ml-2" *ngIf="!checkData?.children?.length"
          (click)="deleteGroup($event, checkData)">
          <span class="iconfont icon-delete2"></span>
          {{'general.common.delete' | translate}}
        </button>
      </div>
    </nz-sider>

    <!-- 主内容区 -->
    <div class="right_content" [ngClass]="{'ml-3': !isCollapsed}">
      <ba-card baCardClass="viewport100 with-scroll border-top-radius-none" class="w-100 h-100">
        <router-outlet></router-outlet>
      </ba-card>
    </div>
  </div>
</div>