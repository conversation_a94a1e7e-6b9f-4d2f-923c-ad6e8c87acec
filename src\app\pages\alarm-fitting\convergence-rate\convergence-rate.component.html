<div class="convergence-rate-container">
  <div class="rate-now-content">
    <app-phoenix-panel [title]="'alarmFitting.convergenceRate.todayConvergenceRate'" [panelClass]="'w-100 h-100'">
      <div class="rate-content show-content">
        <div class="icon-pie-content" echarts [initOpts]="{ renderer: 'svg' }" [options]="pieChartOptions"></div>
        <span class="content-value">{{ currentStatistics?.convergenceRate || '--' }}%</span>
      </div>
    </app-phoenix-panel>
    <app-phoenix-panel [title]="'alarmFitting.convergenceRate.todayAlarms'" [panelClass]="'w-100 h-100'">
      <div class="alarm-content show-content">
        <span class="iconfont icon-document icon-content"></span>
        <span class="content-value alarm-value">{{ showNumCount(currentStatistics?.totalCount)
          }}<span class="content-unit">{{
            'featuremanage.alarmconfig.autoconfirmunit' | translate
            }}</span></span>
      </div>
    </app-phoenix-panel>
    <app-phoenix-panel [title]="'alarmFitting.convergenceRate.todayConvergenceAlarms'" [panelClass]="'w-100 h-100'">
      <div class="convergence-alarm-content show-content">
        <span class="iconfont icon-alarm icon-content"></span>
        <span class="content-value">{{ showNumCount(currentStatistics?.convergenceCount)
          }}<span class="content-unit">{{
            'featuremanage.alarmconfig.autoconfirmunit' | translate
            }}</span></span>
      </div>
    </app-phoenix-panel>
  </div>

  <div class="rate-curve-content">
    <app-phoenix-panel [title]="'alarmFitting.convergenceRate.eventLineDiagram'" [panelClass]="'w-100 h-100'">
      <div class="curve-date-content">
        <ng-container *ngTemplateOutlet="rangePicker"></ng-container>
        <button class="btn btn-primary ml-1 mr-1" (click)="searchCurveDatas()">
          {{ 'general.common.find' | translate }}
        </button>
      </div>
      <div class="curve-content" echarts [initOpts]="{ renderer: 'svg' }" [options]="curveOptions"></div>
    </app-phoenix-panel>
  </div>
</div>

<ng-template #rangePicker>
  <div class="input-group bs-daterangepicker">
    <label class="control-label">{{'s2.shieldevent.starttime' | translate}}</label>
    <div class="input-group">
      <input
        class="form-control"
        name="startTime"
        type="text"
        readonly
        [(ngModel)]="startTime"
        placeholder="{{ 'general.mask.startTime' | translate }}"
        (applyDaterangepicker)="selectTime($event, 'startTime')"
        daterangepicker
        [options]="options"
        [disabled]="dateDisabled"
      />
    </div>
    <label class="control-label">{{'s2.shieldevent.endtime' | translate}}</label>
    <div class="input-group">
      <input
        class="form-control"
        name="endTime"
        type="text"
        readonly
        [(ngModel)]="endTime"
        placeholder="{{ 'general.mask.endTime' | translate }}"
        (applyDaterangepicker)="selectTime($event, 'endTime')"
        daterangepicker
        [options]="options"
        [disabled]="dateDisabled"
      />
    </div>
  </div>
</ng-template>