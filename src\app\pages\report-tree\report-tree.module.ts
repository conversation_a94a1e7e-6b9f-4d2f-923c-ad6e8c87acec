import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CommunalModule } from '@app/communal/communal.module';
import { ReportTreeComponent } from './report-tree.component';
import { CoreModule } from '@app/core/core.module';
import { NzTreeModule } from 'ng-zorro-antd/tree';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { routing } from './report-tree.routing';
import { GroupAddEditComponent } from './group-add-edit/group-add-edit.component';
import { ReportCatagoryService } from '../report/report-category/report-catagory.service';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { SelectModule } from 'ng-select';
import { ReportTreeService } from './report-tree.service';
import { ReportAddComponent } from './report-add/report-add.component';
import { Daterangepicker } from 'ng2-daterangepicker';
import { ReportModule } from '../report/report.module';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { HierarchyEditComponent } from './hierarchy-edit/hierarchy-edit.component';

@NgModule({
  declarations: [
    ReportTreeComponent,
    GroupAddEditComponent,
    ReportAddComponent,
    HierarchyEditComponent,
  ],
  imports: [
    CommonModule,
    CommunalModule,
    CoreModule.forRoot(),
    NzLayoutModule,
    NzTreeModule,
    routing,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    NzSelectModule,
    SelectModule,
    Daterangepicker,
    ReportModule,
    NzTreeSelectModule
  ],
  providers:[
    ReportCatagoryService,
    ReportTreeService
  ]
  
})
export class ReportTreeModule { }