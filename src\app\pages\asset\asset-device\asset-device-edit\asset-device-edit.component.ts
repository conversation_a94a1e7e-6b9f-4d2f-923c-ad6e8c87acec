
import { forkJoin, Observable } from 'rxjs';
import { Component, OnInit, Injector } from '@angular/core';
import { AssetDevice } from '../asset-device.model';
import { AssetDeviceService } from '../asset-device.service';
import { BaseEditComponent } from '@app/communal/components/base-edit/baseEditComponent';
import { IOption } from 'ng-select';
import * as _ from 'lodash';
import { DateUtil } from '@app/communal/utils/DateUtil';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TreeEquipmentSelectorComponent } from '@app/communal/components/tree-equipment-selector/tree-equipment-selector.component';

@Component({
  selector: 'app-asset-device-edit',
  templateUrl: './asset-device-edit.component.html',
  styleUrls: ['./asset-device-edit.component.scss']
})
export class AssetDeviceEditComponent extends BaseEditComponent implements OnInit {
  assetDevice: AssetDevice = new AssetDevice();
  langKeys: any = [];
  assetTypeList: any = [];
  assetCategory: string;
  assetExtend: any = [];
  // 加载状态映射，通过扩展字段ID区分不同字段的加载状态
  isLoadingMap: { [extId: number]: boolean | string } = {};
  // 选项列表映射，通过扩展字段ID区分不同的选项列表
  optionListMap: { [extId: number]: any[] } = {};
  defaultOptionMap: { [extId: number]: any } = {};
  // 页码映射，通过扩展字段ID区分不同的页码
  pageNumberMap: { [extId: number]: number } = {};
  // 搜索值
  keyWord: string = '';
  dateOptions: any = {
    singleDatePicker: true,
    locale: {
      format: 'YYYY-MM-DD',
      cancelLabel: this.translateService.instant('s2.reportmanagement.alert.cancel'),
      applyLabel: this.translateService.instant('s2.reportmanagement.alert.confirm'),
      daysOfWeek: [
        this.translateService.instant('s2.calendar.Sunday'),
        this.translateService.instant('s2.calendar.Monday'),
        this.translateService.instant('s2.calendar.Tuesday'),
        this.translateService.instant('s2.calendar.Wednesday'),
        this.translateService.instant('s2.calendar.Thursday'),
        this.translateService.instant('s2.calendar.Friday'),
        this.translateService.instant('s2.calendar.Saturday'),
      ],
      monthNames: [
        this.translateService.instant('s2.calendar.January'),
        this.translateService.instant('s2.calendar.February'),
        this.translateService.instant('s2.calendar.March'),
        this.translateService.instant('s2.calendar.April'),
        this.translateService.instant('s2.calendar.May'),
        this.translateService.instant('s2.calendar.June'),
        this.translateService.instant('s2.calendar.July'),
        this.translateService.instant('s2.calendar.August'),
        this.translateService.instant('s2.calendar.September'),
        this.translateService.instant('s2.calendar.October'),
        this.translateService.instant('s2.calendar.November'),
        this.translateService.instant('s2.calendar.December'),
      ],
    },
  };

  constructor(
    injector: Injector,
    private assetService: AssetDeviceService,
    private modal: NgbModal,
  ) {
    super(injector, assetService, {
      baseUrl: 'idcassetdevices',
      translatePrefix: 'asset.assetDevice'
    });
    this.initTranslateMessage(this.langKeys);
  }

  ngOnInit() {
    this.initRowData();
  }

  initRowData() {
    forkJoin([
      this.assetService.get(this.activatedRoute.snapshot.paramMap['params']['id']),
      this.assetService.getAll('idcassetcategorys'),
      this.assetService.getAll('extfieldconfigurations?extTable=assetDevice&extTablePkId=' + this.activatedRoute.snapshot.paramMap['params']['id'])
    ]).subscribe(res => {
      if (res[0]) {
        this.assetDevice = res[0];
        this.assetTypeList = this.getAssetType(res[1]);
        this.assetCategory = String(res[0].assetCategoryId);
      }
      this.assetExtend = res[2].filter(field => !field.extCode || !field.extCode.includes("_noshow"));
      if (this.assetExtend && this.assetExtend.length > 0) {
        _.map(this.assetExtend, item => {
          if (!item['extValueConfiguration']) {
            item['extValueConfiguration'] = {};
            item['extValueConfiguration']['extId'] = item['extId'];
            item['extValueConfiguration']['extTable'] = item['extTable'];
            item['extValueConfiguration']['extTablePkId'] = this.activatedRoute.snapshot.paramMap['params']['id'];
            item['extValueConfiguration']['extValue'] = '';
          } else {
            if (item['extDataType'] === 'equipment' && item['extValueConfiguration']['extValue']) {
              item['extValueConfiguration']['eqName'] = JSON.parse(item['extValueConfiguration']['extValue'])[0]['eqName']
            }
          }
          if (item.extDataType === 'table') {
            item['labelValue'] = item['extDataSource'] ? JSON.parse(item['extDataSource']) : {};
            this.pageNumberMap[item.extId] = 1;
            this.optionListMap[item.extId] = [];
            // 立即加载选项数据以支持回显
            this.assetService.loadExtFieldById(item.extValueConfiguration.extValId).subscribe(data => {
              this.defaultOptionMap[item.extId] = { label: data, value: item.extValueConfiguration.extValue };
            })
          }
        })
      }
    });
  }

  beforeSubmitAction() {
    this.assetDevice.assetCategoryId = parseInt(this.assetCategory);
    _.map(this.assetTypeList, item => {
      if (item['value'] === this.assetCategory) {
        this.assetDevice.assetCategoryName = item['label'];
      }
    })
    if (this.assetExtend && this.assetExtend.length > 0) {
      this.assetDevice.extValueConfigurationList = [];
    }
    _.map(this.assetExtend, item => {
      item.extValueConfiguration.extDataType = item.extDataType;
      this.assetDevice.extValueConfigurationList.push(item.extValueConfiguration);
    })
    return this.assetDevice;
  }

  getAssetType(assetTypeData) {
    if (!assetTypeData) {
      return null;
    }
    const sources = Array<IOption>();
    assetTypeData.forEach(element => {
      const sourceItem: IOption = {
        value: String(element.assetCategoryId),
        label: element.assetCategoryName
      };
      sources.push(sourceItem);
    });
    return sources;
  }

  onValueChange(e, item) {
    item['extValueConfiguration']['extValue'] = DateUtil.shortDateToString(
      e.picker.startDate['_d']
    );
  }

  selectDevice(item) {
    let modal;
    modal = this.modal.open(TreeEquipmentSelectorComponent, { backdrop: 'static' });
    modal.componentInstance.multipleBool = false;
    modal.componentInstance.selectDeviceList = item.extValue ? JSON.parse(item.extValue) : [];
    modal.result.then((res) => {
      if (res) {
        const list = [];
        list.push({
          eqId: res[0].eqId,
          eqName: res[0].eqName,
          roomName: res[0].roomName,
          rId: res[0].rId,
          positionId: res[0].positionId,
        })
        this.assetDevice.settingPosition = res[0].fullPath;
        item.eqName = res[0].eqName;
        item.extValue = res.length > 0 ? JSON.stringify(list) : null;
      }
    });
  }

  // 搜索
  onSearch(event: string, extId: number): void {
    // 清理输入：去除首尾空格
    this.keyWord =  event ? event.trim() : '';
    // 解决输入第一个文字，还未输入完成时会触发搜索问题
    if (event !== ' ') {
      this.pageNumberMap[extId] = 1;
      this.optionListMap[extId] = [];
      this.loadMore(extId, true);
    }
  }

  // 加载更多
  loadMore(extId: number, search: boolean = false): void {
    // 如果已经加载完，并且不是搜索，则不加载
    if (this.isLoadingMap[extId] === 'noMore' && !search) {
      return;
    }
    this.isLoadingMap[extId] = true;
    this.assetService.loadExtFieldOptionData(extId, this.keyWord, this.pageNumberMap[extId], 10).subscribe({
      next: (data) => {
        this.isLoadingMap[extId] = false;
        this.pageNumberMap[extId]++;
        this.optionListMap[extId] = [...this.optionListMap[extId], ...data];
        if (data.length === 0) {
          this.isLoadingMap[extId] = 'noMore';
        }
      },
      error: () => {
        this.isLoadingMap[extId] = false;
      }
    });
  }

}
