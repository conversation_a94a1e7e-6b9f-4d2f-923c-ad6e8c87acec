import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

import { SmartTableServerDataSource } from '@app/communal/smart-table-data-source/smart-table-server.data-source';
import { CustomServerSourceConf } from '@app/communal/components/custom-smart-table/data-source/custom-server-conf';
import { FocusPanelServerDataSource } from './alarm-focus-panel-datasource.service';
@Injectable({
    providedIn: 'root' // Angular 会自动将该服务注册到根级 Injector
  })
export class AlarmFocusPanelService {
    public selectedItems: any[] = [];
	public serverDataSource: SmartTableServerDataSource;
	public lastPageStatus: any;
    constructor(private http: HttpClient) { }

    getData(type: number, page: number, size: number, keyword_like: string): Observable<any> {
        return this.http.get('activeeventdtos', {
            params :{
                page: page,
                size: size,
                eventSeverityIds: type == 1 ? '1,2' : '3,4',
                sort:'startTime,desc',
                focusPanelKeywords: keyword_like || ''
            }
        });
    }
    getSourceData( type: number ): FocusPanelServerDataSource {
        const eventSeverityList = type == 1? '1,2' : '3,4';
        const serverConf: CustomServerSourceConf = new CustomServerSourceConf({
            endPoint: 'activeeventdtos',
            method: 'get',
            dataKey: 'content',
            totalKey: 'totalElements',
            pagerPageKey: 'page',
            pagerLimitKey: 'size',
            filterParams: {
                eventSeverityIds: eventSeverityList,
                sort:'startTime,desc',
            },      
        });
        
        const source = new FocusPanelServerDataSource(this.http, serverConf);
        return source;
    }
    public getAlarmReasonList() {
		return this.http.get(`api/eventreasontypes`);
	}
}