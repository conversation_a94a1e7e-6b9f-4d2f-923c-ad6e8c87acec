{"reportSchema": {"title": {"tit1": "当前告警报表", "tit2": "历史告警报表", "tit3": "历史数据报表", "tit4": "tit1", "tit5": "tit1", "tit6": "tit1", "tit7": "告警操作日志报表(设备)", "tit8": "告警操作日志报表(告警事件)", "tit9": "历史放电报表", "tit10": "历史指标报表", "tit11": "tit1", "tit12": "tit1", "tit13": "控制记录报表", "tit14": "即时报表", "tit15": "告警数量统计报表", "tit16": "用电统计报表", "tit17": "tit1", "tit18": "通用定制报表", "tit19": "tit1", "tit20": "tit1", "tit21": "tit1", "tit22": "tit1", "tit23": "告警屏蔽报表", "tit24": "用户操作日志报表", "tit25": "门禁刷卡记录日志", "tit26": "告警通知发送记录报表", "tit27": "历史预警数据查询报表", "tit28": "历史数据报表(5分钟存储)", "tit29": "机架变更记录报表", "tit30": "审计报表", "tit31": "安全日志报表", "tit32": "设备历史运行状态报表", "tit33": "批量控制分组变更报表", "tit34": "批量下发控制命令历史记录报表", "tit35": "群控设备分组变更报表", "tit36": "BA控制命令报表", "tit37": "所有告警报表", "tit38": "设备报表", "tit39": "历史数据报表(原始数据)", "tit40": "告警分类统计报表", "tit41": "串口信息报表"}, "description": {"des1": "展示所需查询当前告警报表数据结果", "des2": "展示所需查询历史告警报表数据结果", "des3": "展示所需查询历史数据报表数据结果", "des4": "des1", "des5": "des1", "des6": "des1", "des7": "展示所需查询告警操作日志报表数据结果", "des8": "展示所需查询告警操作日志报表数据结果", "des9": "展示所需查询设备的历史放电结构", "des10": "展示所需查询历史指标报表数据结果", "des11": "des1", "des12": "des1", "des13": "展示所需查询的控制记录", "des14": "展示所需查询的实时数据", "des15": "展示所需查询的告警数量统计", "des16": "展示所需查询的用电情况统计", "des17": "des1", "des18": "展示所需查询的每日用电量情况统计", "des19": "des1", "des20": "des1", "des21": "des1", "des22": "des1", "des23": "展示所需查询告警屏蔽报表数据结果", "des24": "用户的操作日志包含登录及配置更改等操作", "des25": "展示所需查询门禁刷卡记录报表数据结果", "des26": "查询告警通知发送记录", "des27": "展示所需查询历史预警数据结果", "des28": "展示所需查询历史数据报表数据结果", "des29": "展示所需查询机架变更记录结果", "des30": "展示所需查询审计记录", "des31": "展示所需查询安全日志记录", "des32": "展示所需查询空调群控设备下属设备历史运行状态", "des33": "展示批量控制分组的变更日志记录", "des34": "展示历史批量下发过的控制命令记录", "des35": "展示群控设备分组及相关联数据变更日志记录", "des36": "展示BA下发指令并同步到s6的控制记录", "des37": "展示所需查询所有告警报表据结果", "des38": "展示所需查询的设备清单", "des39": "展示所需查询历史数据报表数据结果", "des40": "展示所需查询的异常告警和施工告警数量统计", "des41": "展示所需查询的串口信息数据"}, "categoryName": {"category1": "告警", "category2": "历史数据", "category3": "指标", "category4": "日志", "category5": "其他", "category6": "电池", "category7": "审计", "category8": "空调"}, "queryParameter": {"param1": "开始时间", "param2": "结束时间", "param3": "设备基类", "param4": "设备", "param7": "开始时间", "param8": "结束时间", "param10": "时间粒度", "param12": "开始时间", "param13": "结束时间", "param14": "设备", "param16": "操作员", "param17": "操作", "param18": "开始时间", "param19": "结束时间", "param21": "事件", "param22": "操作员", "param23": "操作", "param25": "设备", "param26": "开始时间", "param27": "结束时间", "param28": "指标", "param29": "时间粒度", "param38": "开始时间", "param39": "结束时间", "param41": "开始时间", "param42": "结束时间", "param43": "设备基类", "param44": "设备", "param45": "开始时间", "param46": "结束时间", "param47": "开始时间", "param48": "结束时间", "param49": "信号", "param50": "时间粒度", "param55": "开始时间", "param56": "结束时间", "param57": "模板文件", "param58": "配置文件", "param59": "时间粒度", "param78": "屏蔽开始时间", "param79": "屏蔽结束时间", "param80": "开始时间", "param81": "结束时间", "param82": "操作类型", "param83": "事件", "param84": "操作员", "param85": "设备信号", "param86": "开始时间", "param87": "结束时间", "param88": "操作员", "param89": "区域", "param90": "设备", "param91": "卡号", "param92": "卡名称", "param93": "持卡人", "param94": "卡分组", "param95": "卡状态", "param96": "刷卡开始时间", "param97": "刷卡结束时间", "param98": "进出门标志", "param99": "刷卡或开门状态", "param101": "告警时间起", "param102": "告警时间止", "param103": "发送时间起", "param104": "发送时间止", "param105": "发送方式", "param106": "发送结果", "param109": "告警等级", "param110": "事件名", "param111": "确认人", "param112": "关键字", "param113": "告警等级", "param114": "事件名", "param115": "关键字", "param116": "确认人", "param117": "事件", "param118": "事件", "param119": "开始时间", "param120": "结束时间", "param121": "预警分类", "param122": "预警等级", "param123": "资源层级", "param124": "资源名称", "param125": "开始时间", "param126": "结束时间", "param127": "时间粒度", "param128": "设备信号", "param129": "是否带单位", "param130": "取值方式", "param150": "信号基类", "param151": "存储类型", "param152": "是否带单位", "param153": "设备", "param154": "取值方式", "param200": "开始时间", "param201": "结束时间", "param203": "资源位置", "param204": "机架名称", "param205": "IT设备名称", "param206": "状态", "param210": "开始时间", "param211": "结束时间", "param212": "操作账户", "param213": "内容", "param214": "客户端ip", "param215": "状态", "param216": "开始时间", "param217": "结束时间", "param218": "操作账户", "param219": "类别", "param220": "描述", "param221": "客户端ip", "param222": "开始时间", "param223": "结束时间", "param224": "群控设备", "param225": "开始时间", "param226": "结束时间", "param227": "变更类型", "param228": "开始时间", "param229": "结束时间", "param230": "设备", "param231": "命令类型", "param232": "开始时间", "param233": "结束时间", "param234": "局站", "param235": "变更类型", "param236": "审计级别", "param237": "控制命令名称", "param238": "开始时间", "param239": "结束时间", "param240": "设备基类", "param241": "设备", "param242": "告警等级", "param243": "事件名", "param244": "关键字", "param245": "确认人", "param246": "事件", "param247": "层级", "param248": "设备基类", "param260": "开始时间", "param261": "结束时间", "param262": "设备信号", "param263": "信号基类", "param264": "存储类型", "param265": "设备", "param280": "统计类型", "param281": "层级", "param282": "设备类型", "param283": "设备", "param284": "开始时间", "param285": "结束时间", "param286": "设备基类", "param287": "设备", "param288": "设备状态", "param292": "注释", "param293": "注释", "param300": "局站", "param301": "监控单元名称", "param302": "串口名称", "param303": "导出时显示设备位置", "param400": "设备类型", "param401": "告警分类", "param500": "设备类型", "param501": "告警分类", "param600": "设备类型", "param601": "告警分类", "param700": "设备类型", "param310": "层级名称", "param311": "设备名称", "param312": "设备类型", "param313": "IP地址", "param314": "采集单元名称", "param315": "设备状态", "param316": "设备模板名称", "param317": "是否显示系统ID", "param290": "告警等级"}, "selectOption": {"option10": "[{'name': '1分钟','value': '1m-start'},{'name': '5分钟','value': '5m-start'},{'name': '10分钟','value': '10m-start'},{'name': '30分钟','value': '30m-start'},{'name': '1小时','value': '1h-start'},{'name': '3小时','value': '3h-start'},{'name': '6小时','value': '6h-start'},{'name': '12小时','value': '12h-start'},{'name': '1天','value': '1d-start'},{'name': '周','value': '1w-start'},{'name': '月','value': 'month-start'},{'name': '年','value': 'year-start'}]", "option17": "[{'name':'全部', 'value':'0'},{'name':'强制结束告警', 'value':'强制结束告警'}, {'name':'确认告警', 'value':'确认告警'}, {'name':'告警备注', 'value':'告警备注'}]", "option23": "[{'name':'全部', 'value':'0'},{'name':'强制结束告警', 'value':'强制结束告警'}, {'name':'确认告警', 'value':'确认告警'}, {'name':'告警备注', 'value':'告警备注'}]", "option29": "[{'name': '原始数据','value': '0'},{'name': '10分钟','value': '10m'},{'name': '30分钟','value': '30m'},{'name': '1小时','value': '1h'},{'name': '3小时','value': '3h'},{'name': '6小时','value': '6h'},{'name': '12小时','value': '12h'},{'name': '1天','value': '1d'},{'name': '周','value': '1w'},{'name': '月','value': 'month'}]", "option50": "[{'name': '日-始','value': 'start'},{'name': '日-终','value': 'end'},{'name': '日-差值','value': 'difference'}]", "option59": "[{'name': '最小值','value': 'min'},{'name': '最大值','value': 'max'},{'name': '初始值','value': 'start'},{'name': '末尾值','value': 'end'},{'name': '平均值','value': 'avg'},{'name': '差值','value': 'difference'},{'name': '最大差值','value': 'maxdifference'},{'name': '月差值','value': 'monthdifference'}]", "option98": "[{'name': '进门','value': '进门'},{'name': '出门','value': '出门'}]", "option105": "[{'name':'全部', 'value':'0'},{'name':'短信', 'value':'短信'}, {'name':'邮件', 'value':'邮件'}, {'name':'电话语音(短信)', 'value':'电话语音(短信)'}, {'name':'企业微信应用通知', 'value':'企业微信应用通知'}]", "option106": "[{'name':'全部', 'value':'0'},{'name':'发送成功', 'value':'发送成功'}, {'name':'发送失败', 'value':'发送失败'}]", "option127": "[{'name': '原始数据','value': '0'},{'name': '10分钟','value': '10m-start'},{'name': '30分钟','value': '30m-start'},{'name': '1小时','value': '1h-start'},{'name': '3小时','value': '3h-start'},{'name': '6小时','value': '6h-start'},{'name': '12小时','value': '12h-start'},{'name': '1天','value': '1d-start'},{'name': '周','value': '1w-start'},{'name': '月','value': 'month-start'}]", "option129": "[{'name':'否','value':'0'},{'name':'是','value':'1'}]", "option130": "[{'name': '最大值','value': 'max'},{'name': '最小值','value': 'min'},{'name': '平均值','value': 'mean'},{'name': '第一个值','value': 'first'},{'name': '最后一个值','value': 'last'}]", "option151": "[{'name':'统计数据－最大值','value':'0'},{'name':'统计数据－最小值','value':'1'},{'name':'统计数据－平均值','value':'2'},{'name':'事件','value':'3'},{'name':'变化幅值','value':'4'},{'name':'存储周期','value':'5'},{'name':'抄表数据','value':'6'},{'name':'定时存储','value':'7'}]", "option152": "[{'name':'否','value':'0'},{'name':'是','value':'1'}]", "option154": "[{'name': '最大值','value': 'max'},{'name': '最小值','value': 'min'},{'name': '平均值','value': 'mean'},{'name': '第一个值','value': 'first'},{'name': '最后一个值','value': 'last'}]", "option206": "[{'name': '所有','value': '0'},{'name': '上架','value': '1'},{'name': '下架','value': '2'}]", "option215": "[{'name': '最小级别','value': '1'},{'name': '基本级别','value': '2'},{'name': '详细级别','value': '3'},{'name': '未定义级别','value': '4'}]", "option219": "[{'name': '身份用户鉴别','value': '1'},{'name': '攻击检测','value': '2'},{'name': '暴力破解','value': '3'},{'name': '完整性检测','value': '4'}]", "option227": "[{'name':'全部', 'value':'-1'},{'name':'新增', 'value':'1'},{'name':'修改', 'value':'2'},{'name':'删除', 'value':'3'}]", "option231": "[{'name':'全部', 'value':'-1'},{'name':'远程开机', 'value':'1'},{'name':'远程关机', 'value':'2'},{'name':'温度设置', 'value':'3'},{'name':'工作模式切换', 'value':'4'}]", "option235": "[{'name':'全部', 'value':'-1'},{'name':'新增', 'value':'1'},{'name':'修改', 'value':'2'},{'name':'删除', 'value':'3'}]", "option264": "[{'name':'统计数据－最大值','value':'0'},{'name':'统计数据－最小值','value':'1'},{'name':'统计数据－平均值','value':'2'},{'name':'事件','value':'3'},{'name':'变化幅值','value':'4'},{'name':'存储周期','value':'5'},{'name':'抄表数据','value':'6'},{'name':'定时存储','value':'7'}]", "option280": "[{'name': '房间级','value': '1'},{'name': '设备级','value': '2'},{'name': '设备类型级','value': '3'}]", "option288": "[{'name': '离线','value': '0'},{'name': '在线','value': '1'},{'name': '未注册','value': '2'}]", "option303": "[{'name':'否', 'value':'-1'},{'name':'是', 'value':'1'}]", "option315": "[{'name':'离线', 'value':'0'},{'name':'在线', 'value':'1'},{'name':'未注册', 'value':'2'}]", "option317": "[{'name':'否','value':'0'},{'name':'是','value':'1'}]"}, "timeType": {"time1": "存储时间", "time2": "一小时前", "time3": "一天前", "time4": "一周前", "time5": "一月前"}, "storageCycle": {"cycle15": "每小时", "cycle16": "每12小时", "cycle17": "每天", "cycle18": "每周", "cycle19": "每月", "cycle21": "每天8点", "cycle22": "每天20点", "cycle35": "每天02分", "cycle36": "每周02分", "cycle37": "每月02分", "cycle39": "每年"}, "exportParameter": {"param1": "最大值", "param2": "最小值", "param3": "累加值", "param4": "平均值", "param5": "最大值", "param6": "最小值", "param7": "累加值", "param8": "平均值", "param9": "一级告警", "param10": "二级告警", "param11": "三级告警", "param12": "四级告警", "param13": "最大值", "param14": "最小值", "param15": "累加值", "param16": "平均值", "param17": "最大值", "param18": "最小值", "param19": "累加值", "param20": "平均值", "param21": "最大值", "param22": "最小值", "param23": "累加值", "param24": "平均值", "param25": "首值", "param26": "尾值"}}, "reportStatus": {"generatReportFailed": "生成报表失败!", "dowloadFailed": "下载报表文件失败!", "dowloadSucced": "下载报表文件成功!"}}