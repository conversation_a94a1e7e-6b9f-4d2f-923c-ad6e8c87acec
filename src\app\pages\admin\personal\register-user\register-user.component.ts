import { Component, Injector } from '@angular/core';
import { Account } from '@app/pages/admin/human/account/Account';
import { RegisterUserService } from './register-user.service';
import { SessionService } from '@core/services/session.service';
import { BaseEditComponent } from '@components/base-edit/baseEditComponent';
import { AuthenticationService } from '@core/services/authentication.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { ToastService } from '@communal/providers/toast.service';
import * as CryptoJS from 'crypto-js';
import { TranslateService } from '@ngx-translate/core';

const sha1 = require("sha1");
@Component({
  selector: 'app-register-user',
  templateUrl: './register-user.component.html',
  styleUrls: ['./register-user.component.scss'],
})
export class RegisterUserComponent extends BaseEditComponent {
  account = {
    "personName": '',
    "name": '',
    "password": '',
    "phone": '',
    "describe": '',
  };
  langKeys = ['passwordIsError', 'password', 'newpassword', 'confirmPassword', 'passwordIsConsistent', 'pwdrequire',
    'passwordNotEmpty', 'passwordIsInconsistent', 'passwordNotLessThan', 'passwordIsCurrent', 'passwordcheck', 'low', 'middle', 'high', 'meetRequirements',
    'error1', 'error2', 'error3', 'error4', 'success', 'telephoneNotEmpty', 'describeNotEmpty',  'nameNotEmpty', 'phoneFormatError'];
  grade = 0;
  gradeText: string;
  imgCodeSrc: string;
  imgKey: string;
  interval: number;
  sendCodeBtnEnabled = true;
  buttonText = '';
  codeTimer: any;
  isJSReview;

  // 手机号验证相关
  phoneValidationMessage: { type: string, message: string } | null = null;
  phoneValidationTimer: any;

  // 附件上传相关
  selectedFiles: File[] = [];
  uploadedAttachments: any[] = []; // 存储上传成功后的文件信息
  fileValidationMessage: string = '';
  isUploading: boolean = false; // 上传状态标识
  maxFileSize = 1024 * 1024; // 1MB
  allowedFileTypes = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif',
    'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  constructor(injector: Injector,
    private sessionService: SessionService,
    public toastService: ToastService,
    private authenticationService: AuthenticationService,
    private http: HttpClient,
    private translate: TranslateService,
    private registerService: RegisterUserService) {
    super(injector, registerService, { baseUrl: 'accounts', translatePrefix: 'admin.account' });
    this.initTranslateMessage(this.langKeys);
  }

  ngOnInit() {
    
  }

  //取代checkRegxp，仅判断密码是否符合要求
  NewcheckRegxp() {
    const rule = /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[^A-Za-z0-9\s]).{8,}$/;
    if (rule.test(this.account.password)) {
      this.grade = 3;
      this.gradeText = this.langMap.get('admin.account.meetRequirements');
    } else {
      const rule2 = /^(?=.*[^A-Z]).{8,}$|(?=.*[^a-z]).{8,}$|(?=.*[^0-9]).{8,}$|(?=.*[^^A-Za-z0-9\s]).{8,}$/;
      if (rule2.test(this.account.password)) {
        this.grade = 2;
        this.gradeText = this.langMap.get('admin.account.passwordcheck');
      } else {
        this.grade = 0;
      }
    }
  }

  // 文件选择处理
  onFileSelected(event: any) {
    const files = event.target.files;
    this.fileValidationMessage = '';

    for (let i = 0; i < files.length; i++) {
      const file = files[i];

      // 验证文件类型
      if (!this.allowedFileTypes.includes(file.type)) {
        this.fileValidationMessage = '不支持的文件类型，请选择图片、Word或Excel文件';
        return;
      }

      // 验证文件大小
      if (file.size > this.maxFileSize) {
        this.fileValidationMessage = `文件 ${file.name} 超过1MB大小限制`;
        return;
      }

      // 检查是否已存在同名文件
      const existingFile = this.selectedFiles.find(f => f.name === file.name);
      if (!existingFile) {
        this.selectedFiles.push(file);
      }
    }

    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  }

  // 移除文件
  removeFile(index: number) {
    this.selectedFiles.splice(index, 1);
    this.fileValidationMessage = '';

    // 如果移除了文件，也清除对应的上传信息
    if (this.uploadedAttachments.length > index) {
      this.uploadedAttachments.splice(index, 1);
    }
  }

  // 获取选中文件的显示文本
  getSelectedFilesText(): string {
    if (this.selectedFiles.length === 0) {
      return '';
    } else if (this.selectedFiles.length === 1) {
      return this.selectedFiles[0].name;
    } else {
      return `已选择 ${this.selectedFiles.length} 个文件`;
    }
  }

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  submit(ngForm: any): any {
        // 如果表单无效，不提交
        if (!ngForm) {
          return;
        }

        // 如果有附件，需要先上传附件
        if (this.selectedFiles.length > 0) {
          this.uploadFilesAndSubmit();
        } else {
          // 没有附件，直接提交
          this.submitForm([]);
        }
  }

  fileInputclick(){
      // 触发文件选择对话框
     this.uploadSingleFile()
  }


uploadSingleFile(){
  console.log(this.selectedFiles)
    if (this.selectedFiles.length === 0) {
      this.toastService.showToast('error', '请先选择文件', null);
      return;
    }

    this.isUploading = true;
    this.fileValidationMessage = '';

    this.registerService.uploadSingleFile(this.selectedFiles[0]).subscribe(
      (res: any) => {
        this.isUploading = false;
        console.log('上传成功:', res);
        this.toastService.showToast('success', '文件上传成功', null);
      },
      (error) => {
        this.isUploading = false;
        console.error('上传失败:', error);
        this.toastService.showToast('error', '文件上传失败', null);
      }
    );
  }
  // 上传文件并提交表单
  uploadFilesAndSubmit() {
    this.isUploading = true;
    this.fileValidationMessage = '';

    this.registerService.uploadFiles(this.selectedFiles).subscribe(
      (res: any) => {
        this.isUploading = false;
        console.log('上传响应:', res);

        if (res && res.code === 200 && res.data) {
          // 上传成功，保存文件信息
          this.uploadedAttachments = res.data.map((file: any) => ({
            originalFileName: file.originalFileName,
            fileSize: file.fileSize,
            id: file.id
          }));

          console.log('文件上传成功:', this.uploadedAttachments);

          // 提交表单
          this.submitForm(this.uploadedAttachments);
        } else {
          console.error('上传失败，响应格式不正确:', res);
          this.toastService.showToast('error', res?.msg || '文件上传失败', null);
        }
      },
      (error) => {
        this.isUploading = false;
        console.error('文件上传请求失败:', error);
        console.error('错误状态:', error.status);
        console.error('错误信息:', error.error);
        console.error('完整错误对象:', error);

        let errorMessage = '文件上传失败，请重试';
        if (error.status === 500) {
          errorMessage = '服务器内部错误，请检查文件格式和大小';
        } else if (error.status === 413) {
          errorMessage = '文件过大，请选择较小的文件';
        } else if (error.error && error.error.msg) {
          errorMessage = error.error.msg;
        }

        this.toastService.showToast('error', errorMessage, null);
      }
    );
  }




  // 提交表单数据
  submitForm(attachments: any[]) {
    const params = {
      "processDefinitionKey": "s6-account-register", // 固定写死
      "processInstanceName": `${this.account.personName}发起的账号申请`, // 使用personName
      "externalInitiate": true,
      "variables": {
        "data": [
          {
            "businessID": "", // 留空或者不传
            "businessName": "", // 留空或者不传
            "formType": "LOCAL", // 固定写死
            "formKey": "s6-account-register", // 固定写死
            "businessData": {
              "personName": this.account.personName,
              "username": this.account.name,
              "password": this.account.password,
              "phone": this.account.phone,
              "description": this.account.describe,
              "attachments": attachments
            }
          }
        ]
      }
    };

    console.log('提交参数:', params);

    this.registerService.registerUser(params).subscribe(
      (res: any) => {
        if (res.code == 200) {
          this.toastService.showToast('success', res.msg, null);
          // 提交成功后清空表单
          this.resetForm();
        } else {
          this.toastService.showToast('error', res.msg, null);
        }
      },
      (error) => {
        console.error('提交失败:', error);
        this.toastService.showToast('error', '提交失败，请重试', null);
      }
    );
  }

  // 重置表单
  resetForm() {
    this.account = {
      "personName": '',
      "name": '',
      "password": '',
      "phone": '',
      "describe": '',
    };
    this.selectedFiles = [];
    this.uploadedAttachments = [];
    this.fileValidationMessage = '';
    this.phoneValidationMessage = null;
  }







}
