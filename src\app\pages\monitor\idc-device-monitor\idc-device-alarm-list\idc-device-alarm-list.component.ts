import { Component, Input, OnInit, <PERSON><PERSON><PERSON><PERSON>, ViewChild, AfterViewChecked, ChangeDetectorRef } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LocalDataSource } from 'ng2-smart-table';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { Ng2SmartTableComponent } from 'ng2-smart-table';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { ToastService } from '@communal/providers';
import { cloneDeep } from 'lodash';
import { SessionService } from '@app/core/services/session.service';
import { DomSanitizer } from '@angular/platform-browser';
import { TableBaseCellComponent } from '@app/communal/components/base-list-view/table-base-cell/table-base-cell.component';
import { GlobalState } from '@app/global.state';
import { CustomColumnsComponent } from '@app/pages/site/components/custom-columns/custom-columns.component';

// import { AlarmListSeverityImgRenderComponent } from './alarmList-severity-img-render/alarmList-severity-img-render-componet';
// import { ActiveAlarmNoteModalComponent } from '../active-alarm-note-modal/active-alarm-note-modal.component';
// import { AlarmListEventSetOperationComponent } from './alarm-list-event-set/alarm-list-event-set-operation.component';
// import { ShieldConfigComponent } from './shield-config/shield-config.component';

import { IdcDeviceAlarmService } from './idc-device-alarm-list.service';
import { IdcDeviceMonitorService } from '../idc-device-monitor.service';
import { IdcAlarmListSeverityImgComponent } from '../idc-device-monitor-render/idc-alarm-list-severity-img/idc-alarm-list-severity-img.component';
import { IdcAlarmListEventSetOperationComponent } from '../idc-device-monitor-render/idc-alarm-list-event-set-operation/idc-alarm-list-event-set-operation.component';
import { OperationPermession } from '@app/communal/models/operationPermession';
import { AlarmListService } from '@app/pages/monitor/alarm-list/alarm-list.service';
import { AlarmCommonCommentComponent } from '@app/core/components/alarm-commoncomment';
import { SetMaskComponent } from '@app/communal/components/set-mask/set-mask.component';
import { IdcAlarmJumpToDetailComponent } from './idc-alarm-jump-to-detail/idc-alarm-jump-to-detail.component';
import { DeviceShieldConfigComponent } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.component';
import { DeviceShieldEventService } from '@app/pages/mask/device-bulk-block/device-shield-config.component/device-shield-config.service';
import { IdcAlarmListDevuiComponent } from './idc-alarm-list-devui/idc-alarm-list-devui.component';
import { UntypedFormControl } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { IdcEventListDevuiComponent } from './idc-event-list-devui/idc-event-list-devui.component';
import { AuthenticationService } from '@app/core/services/authentication.service';

@Component({
  selector: 'app-idc-device-alarm-list',
  templateUrl: './idc-device-alarm-list.component.html',
  styleUrls: ['./idc-device-alarm-list.component.scss']
})
export class IdcDeviceAlarmListComponent implements OnInit {

  IDC_ALARM_FILTER_TYPE = 'idc-device-alarm-colunm-filter-type';
  // IDC_ALARM_FILTER_TYPE_ZH = 'idc-device-alarm-colunm-filter-type-zh';
  // IDC_ALARM_FILTER_TYPE_EN = 'idc-device-alarm-colunm-filter-type-en';

  @ViewChild('table') table: Ng2SmartTableComponent;

  @Input() searchTextEvent;
  @Input() stationId;
  @Input() houseId;
  @Input() isBInterface;
  @Input() objectType;
  @Input() currentDeviceCategoryId;
  _deviceId: string;
  @Input('deviceId')
  set deviceId(value: string) {
    this._deviceId = value;
    if (this._currentTab == "alarmTab" && value !== undefined) {
      // this.handleClearRefreshData();
    }
  }

  _currentTab: string;
  @Input('currentTab')
  set currentTab(value: string) {

    this._currentTab = value;
    if (value == "alarmTab" && this.stationId && this._deviceId) {
      // this.handleClearRefreshData();
      // this.globalState.subscribe('IdcAlarmListConditionComponent-postAlarmTest', () => {
      //   this.handleClearRefreshData();
      // });
    }
    else {
      this.clearEventTimer();
      this.clearRealTimeTimer();

      // this.globalState.unSubscribe('IdcAlarmListConditionComponent-postAlarmTest');
    }
  }

  refreshSpan = 5000;
  EventSetTimeoutId = 0;
  RealTimeSetTimeoutId = 0;

  // primaryKey = ['uniqueId'];
  primaryKey = null;
  selectedItems: any[] = [];

  primaryKeyCfg = ['uniqueId'];
  selectedCfgItems: any[] = [];

  canConfirm = false;
  showConfirm = false;

  canEnd = false;
  showEnd = false;

  canNote = false;
  showNote = false;

  canShield = false;


  points: Array<object>;//活动事件 array
  lanKeys: string[];//活动事件对应key
  lanStrings: {};//活动事件
  settings: any = {
    pager: {
      perPage: 5,
      display: true,
    }
  };//活动事件settings

  pointsConfig: Array<object>;//事件配置 array
  lanKeysConfig: string[];//事件配置对应key
  lanStringsConfig: {};//事件配置
  settingsConfig = {};//事件配置settings

  shieldDisplay = false;
  eventDisplay = false;

  source: LocalDataSource = new LocalDataSource();//活动事件 source
  sourceConfig: LocalDataSource = new LocalDataSource();//事件配置source

  // @ViewChild('table', { static: true }) ng2SmartTable: Ng2SmartTableComponent;
  // @ViewChild('tableConfig', { static: true }) ng2SmartTableConfig: Ng2SmartTableComponent;

  subscription: Subscription;
  bsModalRef: BsModalRef;

  @Input() isFullscreen: boolean;

  pSettings = {};
  initColumns: any;
  pInitColumns: any;
  showColumns: any;
  pShowColumns: any = {};
  columns: any;

  nameFieldMap = [];

  alarmTotal = 100;

  suscribeId: number = 0;

  selectedConfigEventId: number = null;
  isByteDance = false;

  @ViewChild('idcAlarmListDevui') idcAlarmListDevuiComponent: IdcAlarmListDevuiComponent;
  @ViewChild('idcEventListDevui') idcEventListDevuiComponent: IdcEventListDevuiComponent;

  searchTextAlarm;
  inputControlAlarm = new UntypedFormControl();
  alarmReason: any = [];

  constructor(
    private activatedRoute: ActivatedRoute,
    private translate: TranslateService,
    // private alarmListService: AlarmListService,
    private ngbModal: NgbModal,
    // private rtAlarmService: RealTimeAlarmService,
    private toastService: ToastService,
    private cdr: ChangeDetectorRef,
    private authenticationService: AuthenticationService,
    private sessionService: SessionService,
    private sanitizer: DomSanitizer,
    private globalState: GlobalState,
    // private realtimeService: RealTimeDataService,
    private idcAlarmservice: IdcDeviceAlarmService,
    private idcMonitorService: IdcDeviceMonitorService,
    protected alarmListservice: AlarmListService,
    private shieldService: DeviceShieldEventService,
  ) {
    this.initColumns = [
      { title: 'general.alarm.severityName', columnKey: 'eventSeverity' },
      { title: 'general.alarm.deviceName', columnKey: 'equipmentName' },
      { title: 'general.alarm.devicePosition', columnKey: 'equipmentPosition' },
      { title: 'general.alarm.deviceCategory', columnKey: 'baseEquipmentName' },
      { title: 'general.alarm.corePointName', columnKey: 'eventName' },
      { title: 'notifyManagement.notifyServer.meaning', columnKey: 'meanings' },
      { title: 'general.alarm.occurValue', columnKey: 'eventValue' },
      { title: 'general.alarm.endValue', columnKey: 'endValue' },
      { title: 'general.alarm.birthTime', columnKey: 'startTime' },
      { title: 'general.alarm.confirmTime', columnKey: 'confirmTime' },
      { title: 'general.alarm.clearTime', columnKey: 'endTime' },
    ];
  }

  ngOnInit() {
    this.authenticationService.getAuthenticationJson().subscribe(resJson => {
      this.isByteDance = resJson['bytedace.web.enable'];
    });
    let setedSpan = this.sessionService.getSysconfigByKey('realmonitor.refresh.span');
    if (setedSpan && setedSpan['systemConfigValue']) {
      this.refreshSpan = parseInt(setedSpan['systemConfigValue']);
      this.refreshSpan = this.refreshSpan * 1000;
    }
    this.getOperationPrivilege();
    this.getLanStringsConfig();
    this.getLanStrings();

    this.alarmListservice.getAlarmReasonList().subscribe(res => {
			this.alarmReason = res;
		})

    this.inputControlAlarm.valueChanges
      .pipe(distinctUntilChanged(), debounceTime(300))
      .subscribe((value: string) => {
        this.searchTextAlarm = value;
      });
  }

  handleClearSearchTextAlarm() {
    this.inputControlAlarm.setValue('');
    this.searchTextAlarm = '';
  }

  getOperationPrivilege() {
    this.showConfirm = this.sessionService.hasPermission(OperationPermession.ConfirmEvent);
    this.showEnd = this.sessionService.hasPermission(OperationPermession.EndEvent);
    this.showNote = this.sessionService.hasPermission(OperationPermession.RemarkEvent);
    this.shieldDisplay = this.sessionService.hasPermission(OperationPermession.EventMask);
    this.eventDisplay = true;
  }

  ngAfterViewChecked() {
    this.refreshSelectedStatus();
    this.cdr.detectChanges();
  }

  getQueryParams() {
    this.activatedRoute.queryParams.subscribe(params => {
      if (params['deviceId']) {
        this.deviceId = params['deviceId'];
      }
    });
  }

  getLanStringsConfig(): void {
    this.lanKeysConfig = [
      's2.devicemonitor.NoData',

      's2.devicemonitor.alarmColumns.operation',
      's2.devicemonitor.alarmColumns.eventSeverity',
      's2.devicemonitor.alarmColumns.equipmentName',
      's2.devicemonitor.alarmColumns.eventName',
      's2.devicemonitor.alarmColumns.startTime',
      's2.devicemonitor.alarmColumns.meaning',

      's2.devicemonitor.oneLevelAlarm',
      's2.devicemonitor.secondLevelAlarm',
      's2.devicemonitor.threeLevelAlarm',
      's2.devicemonitor.fourLevelAlarm',
      's2.devicemonitor.noAlarm',
      's2.devicemonitor.maskedAlarm',

      'general.alarm.severityName',
      'general.alarm.deviceName',
      'general.alarm.devicePosition',
      'general.alarm.deviceCategory',
      'general.alarm.corePointName',
      'notifyManagement.notifyServer.meaning',
      'general.alarm.occurValue',
      'general.alarm.endValue',
      'general.alarm.birthTime',
      'general.alarm.confirmTime',
      'general.alarm.clearTime',
    ];

    this.translate.get(this.lanKeysConfig).subscribe(values => {
      this.lanStringsConfig = values;
      this.loadConfigColumnSettings();
      // this.ng2SmartTableConfig.initGrid();
    });
  }


  //事件配置字段
  loadConfigColumnSettings(): void {
    this.settingsConfig = {
      actions: false,
      pager: {
        display: true,
        perPage: 6,
      },
      noDataMessage: this.lanStringsConfig['s2.devicemonitor.NoData'],
      columns: {
        operate: {
          title: this.lanStringsConfig['s2.devicemonitor.alarmColumns.operation'],
          filter: false,
          type: 'custom',
          renderComponent: IdcAlarmListEventSetOperationComponent,
          valuePrepareFunction: (cell, row) => {
            row.stationId = this.stationId;
            // row.houseId = this.houseId;
            row.equipmentId = this._deviceId;
            row.shieldDisplay = this.shieldDisplay;
            row.eventDisplay = this.eventDisplay;
            // row.isBInterface= this.isBInterface;
            return row;
          },
        },
        eventSeverity: {
          title: this.lanStringsConfig['s2.devicemonitor.alarmColumns.eventSeverity'],
          type: 'custom',
          renderComponent: IdcAlarmListSeverityImgComponent,
          valuePrepareFunction: (cell, row) => {
            if (row['mask']) {
              return -10;
            } else {
              return row['eventSeverity']
              if (row['eventSeverity'] != null) {
                return row['eventSeverity'];
              }
              else {
                return 0;//1;
              }
            }

          },
        },
        equipmentName: {
          title: this.lanStringsConfig['s2.devicemonitor.alarmColumns.equipmentName'],
          type: 'html',
          valuePrepareFunction: data => `<div title="` + data + `" class="cell_limit">` + data + `</div>`,
        },
        eventName: {//eventName
          title: this.lanStringsConfig['s2.devicemonitor.alarmColumns.eventName'],
          type: 'html',
          valuePrepareFunction: data => `<div title="` + data + `" class="cell_limit">` + data + `</div>`,
        },
        startTime: {
          title: this.lanStringsConfig['s2.devicemonitor.alarmColumns.startTime'],
          type: 'custom',
          renderComponent: TableBaseCellComponent,
        },
        meaning: {
          title: this.lanStringsConfig['s2.devicemonitor.alarmColumns.meaning'],
          type: 'custom',
          renderComponent: TableBaseCellComponent,
        },
      },
    };
  }

  getLanStrings(): void {

    this.lanKeys = [
      's2.devicemonitor.NoData',

      's2.alarm.isConfirmed',
      's2.alarm.eventSeverity',
      's2.alarm.stationStateName',
      's2.alarm.equipmentName',
      's2.alarm.equipmentStateName',
      's2.alarm.name',

      's2.alarm.standardTypeName',
      's2.alarm.meaning',
      's2.alarm.overturn',
      's2.alarm.startTime',
      's2.alarm.endTime',
      's2.alarm.confirmTime',

      's2.alarm.confirmUserName',
      's2.alarm.instructionId',
      's2.alarm.triggerValue',
      's2.alarm.memo',
      's2.alarm.stdSignalDescription',
      's2.alarm.stdSignalMeanings',

      's2.alarm.stdNote',
      's2.alarm.equipmentLogicCategory',
      's2.alarm.alarmLogicCategory',

      's2.alarm.stationName',
      's2.alarm.groupName',
      's2.alarm.centerName',
      's2.alarm.stationCategoryName',
      's2.alarm.monitoringUnitName',
      's2.alarm.eventCategoryName',

      's2.common.yes',
      's2.common.no',

      'general.alarm.severityName',
      'general.alarm.deviceName',
      'general.alarm.devicePosition',
      'general.alarm.deviceCategory',
      'general.alarm.corePointName',
      'notifyManagement.notifyServer.meaning',
      'general.alarm.occurValue',
      'general.alarm.endValue',
      'general.alarm.birthTime',
      'general.alarm.confirmTime',
      'general.alarm.clearTime',
      'general.common.confirm',
      'general.alarm.remark',
      'general.common.end',
      'general.common.action'
    ];

    this.translate.get(this.lanKeys).subscribe(values => {
      this.lanStrings = values;
      //this.loadTableColumnSettings();

      //取定制列对应关系--
      this.getColNameFieldMap();
      this.getInitColumn();
      this.getCustomSettings();

    });

  }

  getRealTimeData() {
    if (this.stationId && this._deviceId) {
      this.idcAlarmservice.getAlarmList(this._deviceId).subscribe(res => {
        if (res && res.content) {

          this.generatePageData(res.content);

          this.clearAndGetRealTimeTimeout();
        }
      }, err => {
        this.clearAndGetRealTimeTimeout();
      })

    }
  }

  clearAndGetRealTimeTimeout() {
    this.RealTimeSetTimeoutId && window.clearTimeout(this.RealTimeSetTimeoutId);
    this.RealTimeSetTimeoutId = window.setTimeout(() => {
      this.RefreshRealTimeDt();
    }, this.refreshSpan)
  }

  getEventConfig() {
    if (this.stationId && this._deviceId) {
      this.idcAlarmservice.getConfigEvents(this._deviceId, this.stationId)
        .subscribe(res => {
          console.log('getConfigEvents', res)
          this.generatePageDataConfig(res);

          this.clearAndGetEventTimeout();
        }, err => {
          this.clearAndGetEventTimeout();
        })
    }
  }

  clearAndGetEventTimeout() {
    this.EventSetTimeoutId && window.clearTimeout(this.EventSetTimeoutId);
    this.EventSetTimeoutId = window.setTimeout(() => {
      this.RefreshEventConfig();
    }, this.refreshSpan)
  }

  public generatePageData(ps) {
    this.alarmTotal = ps.length
    let points = [];
    if (this.selectedConfigEventId) {
      ps.forEach(item => {
        if (item.eventId === this.selectedConfigEventId) {
          points.push(item);
        }
      });
    } else {
      points = ps;
    }

    this.points = points;
    this.source.load(this.points);
  }

  public generatePageDataConfig(configData) {
    this.pointsConfig = null;
    // this.pointsConfig = configData.configEvents;
    this.pointsConfig = configData;
    this.globalState.notifyDataChanged('alarmCommandSignalData', { total: this.pointsConfig.length });

    this.sourceConfig.load(this.pointsConfig);


  }

  handleSelectRowAfterRefresh() {

  }

  handleClearRefreshData() {
    this.clearEventTimer();
    this.clearRealTimeTimer();

    this.refreshData();

    this.selectedItems = [];
  }

  refreshData() {
    //this.getEventConfig();
    //this.getRealTimeData();

    this.RefreshEventConfig();
    this.RefreshRealTimeDt();

  }

  private RefreshEventConfig() {
    this.getEventConfig();

    // this.EventSetTimeoutId = window.setTimeout(() => {
    //   this.RefreshEventConfig();
    // }, this.refreshSpan)
  }

  private RefreshRealTimeDt() {
    this.getRealTimeData();

    // this.RealTimeSetTimeoutId = window.setTimeout(() => {
    //   this.RefreshRealTimeDt();
    // }, this.refreshSpan)
  }

  clearEventTimer() {

    clearTimeout(this.EventSetTimeoutId);
  }

  clearRealTimeTimer() {

    clearTimeout(this.RealTimeSetTimeoutId);
  }

  public sortEventSeverity(x: any, y: any) {
    return x.eventSeverity - y.eventSeverity;
  }

  ngOnDestroy() {
    if (this.subscription) {
      this.subscription.unsubscribe();
    }

    this.clearEventTimer();
    this.clearRealTimeTimer();
  }

  test($event) {
    const _val = $event.heading;
  }

  onUserRowSelect(event: any) {
    this.refreshButton();
  }

  refreshSelectedStatus() {
  }

  refreshButton() {
    if (!this.selectedItems.length) {
      this.canNote = false;
      this.canConfirm = false;
      this.canEnd = false;
      this.canShield = false;
    } else if (this.selectedItems.length === 1) {
      this.canNote = !this.selectedItems[0]['endTime'];
      this.canConfirm = !this.selectedItems[0]['confirmTime'];
      this.canEnd = !this.selectedItems[0]['endTime'];
      this.canShield = true;
      if (this.isByteDance) {
        this.canConfirm = (this.canConfirm && this.sessionService.hasPermission(130 + this.selectedItems[0].eventLevel));
        this.canShield = this.sessionService.hasPermission(120 + this.selectedItems[0].eventLevel)
      }
    } else {
      this.canNote = true;
      this.canConfirm = this.selectedItems.filter(item => !item['confirmTime']).length > 0;
      this.canEnd = true;
      this.canShield = true;
      if (this.isByteDance) {
        let canlen = this.selectedItems.filter(item => (!item['confirmTime'] && this.sessionService.hasPermission(130 + item['eventLevel']))).length;
        this.canConfirm = (this.canConfirm && canlen > 0);
        let canShieldLen = this.selectedItems.filter(item => this.sessionService.hasPermission(120 + item['eventLevel'])).length;
        this.canShield = canShieldLen > 0;
      }
    }
  }

  shieldEvent() {
    if (!this.selectedItems || !this.selectedItems.length) {
      return;
    }

    if (this.selectedItems.length === 1) {
      const eventmasksId = this.stationId + ',' + this._deviceId + ',' + this.selectedItems[0].eventId;
      this.shieldService.getEventMask(eventmasksId).subscribe(res => {
        if (res) {
          const time = {
            timeGroupCategory: res['timeGroupCategory'],
            timeGroupSpans: res['timeGroupSpans'],
            InputstartTime: res['startTime'],
            InputendTime: res['endTime'],
            Inputreason: res['reason'],
          }
          this.handleOpenShieldOneEvent(time);
        } else {
          this.handleOpenShieldOneEvent();
        }
      }, err => {
        this.handleOpenShieldOneEvent();
      })
    } else {
      this.handleOpenShieldEvents();
    }

  }

  handleOpenShieldOneEvent(timeRecover?) {
    const modal = this.ngbModal.open(DeviceShieldConfigComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });

    modal.componentInstance.equipmentId = this._deviceId;
    modal.componentInstance.stationId = this.stationId;
    modal.componentInstance.eventId = this.selectedItems[0].eventId;

    if (timeRecover) {
      modal.componentInstance.timeGroupCategory = timeRecover.timeGroupCategory;
      modal.componentInstance.timeGroupSpans = timeRecover.timeGroupSpans;
      modal.componentInstance.InputstartTime = timeRecover.InputstartTime;
      modal.componentInstance.InputendTime = timeRecover.InputendTime;
      modal.componentInstance.Inputreason = timeRecover.Inputreason;
    }

    modal.result.then((res) => {
      if (res) {
        let maskUrl = 'eventmasks';
        this.shieldService.saveMask(res, maskUrl).subscribe(
          res => {
            this.toastService.showToast('success', this.translate.instant('s2.shieldevent.saveSuccess'), '');
          },
          error => {
            this.toastService.showToast('error', this.translate.instant('s2.shieldevent.saveError'), '');
          }
        )
      }
    });
  }

  handleOpenShieldEvents() {
    const modal = this.ngbModal.open(DeviceShieldConfigComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });

    const eventIds = [], stationIds = [], equipmentIds = [];
    if (this.isByteDance) {
      let allLen = this.selectedItems.length;
      let canlen = this.selectedItems.filter(item => this.sessionService.hasPermission(120 + item['eventLevel'])).length;
      if (allLen != canlen) {
        this.toastService.showToast('warning', this.translate.instant('general.alarm.hasNoPermissionMask'), '', { timeOut: 3000 });
      }
      this.selectedItems.filter(fitem => this.sessionService.hasPermission(120 + fitem['eventLevel'])).forEach(item => {
        eventIds.push(item.eventId);
        equipmentIds.push(this._deviceId);
        stationIds.push(this.stationId);
      })
    } else {
      this.selectedItems.forEach(item => {
        eventIds.push(item.eventId);
        equipmentIds.push(this._deviceId);
        stationIds.push(this.stationId);
      })
    }

    modal.result.then((res) => {
      if (res) {
        const params = {
          ...res,
          equipmentIds: [...equipmentIds],
          stationIds: [...stationIds],
          eventIds: [...eventIds]
        }
        let maskUrl = 'eventmasks/batchcreate';
        this.shieldService.saveMask(params, maskUrl).subscribe(
          res => {
            this.toastService.showToast('success', this.translate.instant('s2.shieldevent.saveSuccess'), '');
            this.selectedItems = [];
          },
          error => {
            this.toastService.showToast('error', this.translate.instant('s2.shieldevent.saveError'), '');
          }
        )
      }
    });
  }

  saveEventMask(param, maskUrl) {
  }

  //事件确认
  confirmEvent() {
    if (!this.selectedItems.length) {
      return false;
    }

    let selteds = this.selectedItems
      .filter(item => !item['confirmTime'])

    if (this.isByteDance)
      selteds = this.selectedItems
        .filter(item => (!item['confirmTime'] && this.sessionService.hasPermission(130 + item['eventLevel'])))

    if (!selteds || !selteds.length) {
      return false;
    }

    let allLen = this.selectedItems.filter(item => !item['confirmTime']).length;
    let canlen = allLen;
    if (this.isByteDance)
      canlen = this.selectedItems.filter(item => (!item['confirmTime'] && this.sessionService.hasPermission(130 + item['eventLevel']))).length;
    if (allLen != canlen) {
      this.toastService.showToast('warning', this.translate.instant('general.alarm.hasConfirmed'), '', { timeOut: 3000 });
    }

    this.alarmListservice.selectedItems = selteds;
    const modal = this.ngbModal.open(AlarmCommonCommentComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.opType = 'confirm';
    modal.componentInstance.theTitle = this.lanStrings['general.common.confirm'];
    modal.componentInstance.isBatch = true;
    modal.componentInstance.reasonList = this.alarmReason;

    modal.result.then(res => {
      if (res && res.ok) {
        this.selectedItems = [];
        this.canConfirm = false;
        this.canEnd = false;
        this.canNote = false;
        this.canShield = false;
      }
    });
  }

  //事件强制结束
  endEvent() {
    this.alarmListservice.selectedItems = this.selectedItems;
    const modal = this.ngbModal.open(AlarmCommonCommentComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.opType = 'end';
    modal.componentInstance.theTitle = this.lanStrings['general.common.end'];
    modal.componentInstance.isBatch = true;

    modal.result.then(res => {
      if (res && res.ok) {
        this.selectedItems = [];
        this.canConfirm = false;
        this.canEnd = false;
        this.canNote = false;
        this.canShield = false;
      }
    });
  }

  noteEvent() {
    this.alarmListservice.selectedItems = this.selectedItems;
    const modal = this.ngbModal.open(AlarmCommonCommentComponent, { backdrop: 'static', backdropClass: 'bounced-hierarchy', windowClass: 'window-hierarchy' });
    modal.componentInstance.opType = 'comment';
    modal.componentInstance.theTitle = this.lanStrings['general.alarm.remark'];
    modal.componentInstance.isBatch = true;

    modal.result.then(res => {
      if (res && res.ok) {
        this.selectedItems = [];
        this.canConfirm = false;
        this.canEnd = false;
        this.canNote = false;
        this.canShield = false;
      }
    });
  }

  showToast(type, msg) {
    this.toastService.showToast(type, this.translate.instant(msg), '');
  }

  //设置列功能
  getColNameFieldMap(): void {

    this.nameFieldMap = [
      { colName: this.lanStrings['general.alarm.severityName'], colField: "eventSeverity", colKey: 'general.alarm.severityName' },
      { colName: this.lanStrings['general.alarm.deviceName'], colField: "equipmentName", colKey: 'general.alarm.deviceName' },
      { colName: this.lanStrings['general.alarm.devicePosition'], colField: "equipmentPosition", colKey: 'general.alarm.devicePosition' },
      { colName: this.lanStrings['general.alarm.deviceCategory'], colField: "baseEquipmentName", colKey: 'general.alarm.deviceCategory' },
      { colName: this.lanStrings['general.alarm.corePointName'], colField: "eventName", colKey: 'general.alarm.corePointName' },
      { colName: this.lanStrings['notifyManagement.notifyServer.meaning'], colField: "meanings", colKey: 'notifyManagement.notifyServer.meaning' },
      { colName: this.lanStrings['general.alarm.occurValue'], colField: "eventValue", colKey: 'general.alarm.occurValue' },
      { colName: this.lanStrings['general.alarm.endValue'], colField: "endValue", colKey: 'general.alarm.endValue' },
      { colName: this.lanStrings['general.alarm.birthTime'], colField: "startTime", colKey: 'general.alarm.birthTime' },
      { colName: this.lanStrings['general.alarm.confirmTime'], colField: "confirmTime", colKey: 'general.alarm.confirmTime' },
      { colName: this.lanStrings['general.alarm.clearTime'], colField: "endTime", colKey: 'general.alarm.clearTime' },

    ];
  }

  getCustomSettings(): void {
    this.getShowColumns().then((customCols: any) => this.createSettings(customCols));
  }

  customColumns() {
    this.idcAlarmListDevuiComponent.setCustomColunms();
    return;
    this.getShowColumns().then((showColumns: any) => {

      const modal = this.ngbModal.open(CustomColumnsComponent, {
        container: 'app-idc-device-alarm-list'
      });
      modal.componentInstance.columns = cloneDeep(showColumns.slice(0));
      modal.componentInstance.footer = {
        left: [
        ],
        right: [
          {
            label: 'general.common.cancel',
            type: 'secondary',
            onClick: componentInstance => componentInstance.close()
          },
          {
            label: 'general.common.ok',
            type: 'primary',
            onClick: componentInstance =>
              componentInstance.close(componentInstance.columns)
          }
        ]
      };
      modal.result.then(config => {
        if (config) {
          // customContent.CustomContent.Columns = res;
          const newShowColumns = config.slice(0);
          this.idcMonitorService
            .putSaveCustomColumns({
              filterType: this.IDC_ALARM_FILTER_TYPE,
              content: JSON.stringify({
                CustomContent: { Column: newShowColumns },
              }),
              templateName: 'not_used_here'
            })
            .subscribe(res => {
              if (!res.err_code) {
                this.showColumns = newShowColumns;
                this.createSettings(newShowColumns);
                this.showToast('success', 's2.alarm.customColumnsSuccess');
              } else {
                this.showToast('error', 's2.alarm.customColumnsFailed');
              }
            });
        }
      });
    });
  }

  getShowColumns() {

    return this.showColumns
      ? new Promise((resolve, reject) => resolve(this.showColumns))
      : this.idcMonitorService
        .getCusomColumns(this.IDC_ALARM_FILTER_TYPE)
        .toPromise()
        .then((res: any) => {
          if (res.length) {

            this.showColumns = JSON.parse(res[0].content, (key, value) => {
              if (value === 'true') return true;
              if (value === 'false') return false;
              return value;
            }).CustomContent.Column;
            this.showColumns.forEach(item => {
              item['headerText'] = this.lanStrings[item.columnKey];
            })
            return this.showColumns;
          } else {

            return this.translate
              .get([
                's2.alarm.operation',
                's2.alarm.warningLight',
                ...this.initColumns
                  .map(col => col.title)
                  .filter(title => title)
              ])
              .toPromise()
              .then(val => {
                this.showColumns = [

                ].concat(
                  ...this.initColumns.map(col => ({
                    headerText: val[col.title],
                    visible: true,
                    columnKey: col.title
                  }))
                );

                return this.showColumns;
              });
          }
        });
  }
  getInitColumn(): void {
    this.pInitColumns = {
      eventSeverity: {
        title: this.lanStrings['general.alarm.severityName'],
        type: 'custom',
        renderComponent: IdcAlarmListSeverityImgComponent,
        // valuePrepareFunction: (cell, row) => {
        //   if (row['eventSeverity'] != null) {
        //     let severity: number;
        //     switch (row['eventSeverity']) {
        //       case this.lanStringsConfig['s2.devicemonitor.oneLevelAlarm']: {
        //         severity = 1;
        //         break;
        //       }
        //       case this.lanStringsConfig['s2.devicemonitor.secondLevelAlarm']: {
        //         severity = 2;
        //         break;
        //       }
        //       case this.lanStringsConfig['s2.devicemonitor.threeLevelAlarm']: {
        //         severity = 3;
        //         break;
        //       }
        //       case this.lanStringsConfig['s2.devicemonitor.fourLevelAlarm']: {
        //         severity = 4;
        //         break;
        //       }
        //       default: {
        //         severity = 4 - row['eventSeverity'];
        //         break;
        //       }
        //     }
        //     return severity;
        //   }
        //   else {
        //     return 0;//1;
        //   }
        // },
      },
      equipmentName: {
        title: this.lanStrings['general.alarm.deviceName'],
        type: 'html',
        valuePrepareFunction: data => `<div title="` + data + `" class="cell_limit">` + data + `</div>`,
      },
      equipmentPosition: {
        title: this.lanStrings['general.alarm.devicePosition'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
      baseEquipmentName: {
        title: this.lanStrings['general.alarm.deviceCategory'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
      eventName: {
        title: this.lanStrings['general.alarm.corePointName'],
        type: 'html',
        valuePrepareFunction: data => `<div title="` + data + `" class="cell_limit">` + data + `</div>`,
      },
      meanings: {
        title: this.lanStrings['notifyManagement.notifyServer.meaning'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
      eventValue: {
        title: this.lanStrings['general.alarm.occurValue'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
      endValue: {
        title: this.lanStrings['general.alarm.endValue'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
      startTime: {
        title: this.lanStrings['general.alarm.birthTime'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
      confirmTime: {
        title: this.lanStrings['general.alarm.confirmTime'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
      endTime: {
        title: this.lanStrings['general.alarm.clearTime'],
        type: 'custom',
        renderComponent: TableBaseCellComponent,
      },
    };
  }

  createSettings(value: any): void {

    if (value != undefined && value != null) {
      this.pShowColumns = {
        customAction: {
          title: this.lanStrings['general.common.action'],
          width: '32px',
          filter: false,
          type: 'custom',
          renderComponent: IdcAlarmJumpToDetailComponent,
          valuePrepareFunction: (cell, row) => {
            return row;
          },
        },
      };
      const objColumns = value.filter(item => item.visible == true);
      objColumns.forEach(objCol => {

        const nfMap = this.nameFieldMap.filter(function (nfm) {
          // return nfm.colName === objCol.headerText;
          return nfm.colKey === objCol.columnKey;
        });
        if (nfMap != undefined && nfMap != null && nfMap.length > 0 && nfMap[0].colField) {

          this.pShowColumns[nfMap[0].colField] = this.pInitColumns[nfMap[0].colField];
        }
      });

      this.settings = {
        selectMode: 'multi',
        //actions: false,
        actions: {
          delete: false,
          add: false,
          edit: false,
          select: false,
        },
        pager: {
          display: true,
          perPage: 5,
        },
        rowClassFunction: (row) => {
          if (row['data']['confirmTime']) {
            if (localStorage.getItem('theme') !== 'classic') {
              return 'alarmconfirm2';
            } else {
              return 'alarmconfirm';
            }
          }
          if (row['data']['endTime']) {
            if (localStorage.getItem('theme') !== 'classic') {
              return 'alarmend2';
            } else {
              return 'alarmend';
            }
          }
          return '';
        },
        noDataMessage: this.lanStrings['s2.devicemonitor.NoData'],
        columns: this.pShowColumns,
        needRefresh: true,
      };


      // this.ng2SmartTable.initGrid();

    }
  }

  handleConfigRowSelect(event) {
  }

  handleSelectedAlarmItems(event) {
    this.selectedItems = event;

    this.refreshButton();
  }

  handleAlarmTotalEvent(event) {
    this.alarmTotal = event;
  }

  customColumnsEvent() {
    this.idcEventListDevuiComponent.setCustomColunms();
  }

}
