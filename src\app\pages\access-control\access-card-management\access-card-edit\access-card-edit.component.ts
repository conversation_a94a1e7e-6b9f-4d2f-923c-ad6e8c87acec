import {Component, OnInit, Injector} from '@angular/core';
import {BaseEditComponent} from '@app/communal/components/base-edit/baseEditComponent';
import {AccessCardManagementService} from '../../services/access-card-management.service';
import {DoorCardInfo, CardStatusEnum, ApplicableTypeOptions} from '../../model/door-card.model';
import {ActivatedRoute} from '@angular/router';
import {DateUtil} from '@app/communal/utils/DateUtil';
import {forkJoin} from 'rxjs';
import {SessionService} from '@app/core/services/session.service';
import {OperationPermession} from '@app/communal/models/operationPermession';
import {NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {AccessCardManagementFaceCollectComponent} from '../access-card-management-face-collect.component';
import {AccessCardManagementFingerprintCollectComponent} from '../access-card-management-fingerprint-collect.component';
import {DomSanitizer} from '@angular/platform-browser';
import { UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {AccessCardManagementFeatureService} from '../access-card-management-feature.service';
import {AlertService} from '@app/communal/providers';

@Component({
    selector: 'app-access-card-edit',
    templateUrl: './access-card-edit.component.html',
    styleUrls: ['./access-card-edit.component.scss'],
})
export class AccessCardEditComponent extends BaseEditComponent implements OnInit {
    SYS_ADMIN_ID = -1;
    DOOR_SYSTEM_TENCENT_TYPE = '12';
    DEFAULT_CARD_TYPE = '2';
    cardId: number;
    cardInfo: DoorCardInfo = {
        cardId: null,
        cardName: '',
        cardType: null,
        cardCode: '',
        cardCodeType: null,
        userId: null,
        cardGroup: null,
        password: '',
        startTime: '',
        endTime: '',
        registerTime: '',
        cardStatus: null,
        description: '',
        fingerprintId: '',
        faceId: null,
        cardGroupName: '',
    };
    cardHolderOptions: any[] = [];
    cardGroupOptions: any[] = [];
    useStateOptions: any[] = [];
    cardTypeOptions: any[] = [];
    cardNoTypeOptions: any[] = [];
    fingerprintList: any[] = [];
    oldCardStatus: number;
    unEdit = false;
    hasControlPermission = true;
    isTencentDoorType = false;
    msg = '';
    fingerprint = '';
    faceimg: any;
    faceBlob: any;
    
    form: UntypedFormGroup;
    public showPswInfo = false;
    private lanKes = [
      's2.devicemonitor.modify', 
      's2.devicemonitor.succeed', 
      's2.devicemonitor.failed',
      "s2.reportmanagement.alert.confirm",
      "s2.reportmanagement.alert.cancel",
  
      "s2.calendar.January",
      "s2.calendar.February",
      "s2.calendar.March",
      "s2.calendar.April",
      "s2.calendar.May",
      "s2.calendar.June",
      "s2.calendar.July",
      "s2.calendar.August",
      "s2.calendar.September",
      "s2.calendar.October",
      "s2.calendar.November",
      "s2.calendar.December",
  
      "s2.calendar.Sunday",
      "s2.calendar.Monday",
      "s2.calendar.Tuesday",
      "s2.calendar.Wednesday",
      "s2.calendar.Thursday",
      "s2.calendar.Friday",
      "s2.calendar.Saturday",
    ];
    private lanValues;
    options: any;
    optionsEnd: any;

    constructor(
        injector: Injector,
        private cardService: AccessCardManagementService,
        private featureSrv: AccessCardManagementFeatureService,
        private activeRoute: ActivatedRoute,
        private sessionService: SessionService,
        private modal: NgbModal,
        private sanitizer: DomSanitizer,
        private formBuilder: UntypedFormBuilder,
        private alertService: AlertService,
    ) {
        super(injector, cardService, {
            baseUrl: 'TencentAccessControl/AccessCard',
            translatePrefix: 'accessControl.cardManagement',
        });
        this.hasControlPermission = this.sessionService.hasPermissionEnum(OperationPermession.EquipmentControl);
        if (this.sessionService.getSysconfigByKey('door.system.type')) {
            this.isTencentDoorType =
                this.DOOR_SYSTEM_TENCENT_TYPE ===
                this.sessionService.getSysconfigByKey('door.system.type').systemConfigValue;
        }
        this.initTranslateMessage([]);
        this.initRowData();
    }

    setStartDate(value, param) {
        param.startTime = DateUtil.shortDateToString(value.picker.startDate['_d']);
    }

    setEndDate(value, param) {
        param.endTime = DateUtil.shortDateToString(value.picker.startDate['_d']);
    }

    ngOnInit() {
        this.activeRoute.paramMap.subscribe(params => {
            this.cardId = params.has('cardId') && Number(params.get('cardId'));
            this.initCardInfo();
        });
        this.translateService.get(this.lanKes).subscribe(
            (values) => {
                this.lanValues = values;
            }
        );
    }

    getDatePickerOptions(startDate) {
        return {
            singleDatePicker: true,
            autoUpdateInput: false,
            showDropdowns: true,
            startDate: startDate,
            minDate: '2000-01-01',
            maxDate: '2050-01-01',
            locale: {
                format: 'YYYY-MM-DD',
                cancelLabel: this.lanValues['s2.reportmanagement.alert.cancel'],
                applyLabel: this.lanValues['s2.reportmanagement.alert.confirm'],
                daysOfWeek: [
                    this.lanValues['s2.calendar.Sunday'],
                    this.lanValues['s2.calendar.Monday'],
                    this.lanValues['s2.calendar.Tuesday'],
                    this.lanValues['s2.calendar.Wednesday'],
                    this.lanValues['s2.calendar.Thursday'],
                    this.lanValues['s2.calendar.Friday'],
                    this.lanValues['s2.calendar.Saturday']],
                monthNames: [
                    this.lanValues['s2.calendar.January'],
                    this.lanValues['s2.calendar.February'],
                    this.lanValues['s2.calendar.March'],
                    this.lanValues['s2.calendar.April'],
                    this.lanValues['s2.calendar.May'],
                    this.lanValues['s2.calendar.June'],
                    this.lanValues['s2.calendar.July'],
                    this.lanValues['s2.calendar.August'],
                    this.lanValues['s2.calendar.September'],
                    this.lanValues['s2.calendar.October'],
                    this.lanValues['s2.calendar.November'],
                    this.lanValues['s2.calendar.December'],
                ]
            }
        }
    }

    initCardInfo() {
        if (!this.cardId) return;
        this.cardService.getCardInfoById(this.cardId).subscribe(res => {
            if (!res || !res.data) {
                return;
            }
            this.dataFormatCardInfo(res.data[0]);
        });

        this.featureSrv.getFaceImageById(this.cardId).subscribe(res => {
            if (!res) {
                return;
            }
            // this.dataFormatCardInfo(res[0].data[0]);
            if (res && /image\/\w+/.test(res.type)) {
                res.text().then(val => (this.faceBlob = val));
                this.faceimg = this.sanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(res));
            }
        });
    }

    initRowData() {
        forkJoin([
            this.cardService.get(null, 'persons'),
            this.cardService.getDoorCardTreeObjects(),
            this.cardService.getCardStatus(),
            this.cardService.getCardType(),
        ]).subscribe((res: any[]) => {
            if (res && res[0] && Array.isArray(res[0])) {
                this.cardHolderOptions = res[0]
                    .filter(item => item.personId !== this.SYS_ADMIN_ID)
                    .map(item => ({
                        label: item.name,
                        value: item.personId.toString(),
                    }));
            }
            if (res && res[1] && Array.isArray(res[1])) {
                this.cardGroupOptions = res[1].map(group => ({
                    label: group.itemValue,
                    value: group.itemId.toString(),
                }));
            }
            if (res && res[2] && Array.isArray(res[2])) {
                this.useStateOptions = res[2].map(status => ({
                    label: status.itemValue,
                    value: status.itemId.toString(),
                }));
            }
            if (res && res[3] && Array.isArray(res[3])) {
                this.cardTypeOptions = res[3].map((type: any) => ({
                    label: type.itemValue,
                    value: type.itemId.toString(),
                }));
            }
        });
        this.cardNoTypeOptions = [
            {
                label: this.translateService.instant('accessControl.cardManagement.decimalism'),
                value: '10',
            },
            {
                label: this.translateService.instant('accessControl.cardManagement.hexadecimal'),
                value: '16',
            },
        ];
    }

    dataFormatCardInfo(data: any) {
        this.cardInfo = data;
        this.cardInfo.cardGroup = data.cardGroup.toString();
        this.cardInfo.userId = data.userId.toString();
        this.cardInfo.cardStatus = data.cardStatus.toString();
        this.cardInfo.cardType = data.cardType ? data.cardType.toString() : this.DEFAULT_CARD_TYPE;
        this.cardInfo.cardCodeType = data.cardCodeType ? data.cardCodeType.toString() : '16';
        this.cardInfo.startTime = data.startTime.length >= 10 ? data.startTime.substr(0, 10) : data.startTime;
        this.cardInfo.endTime = data.endTime.length >= 10 ? data.endTime.substr(0, 10) : data.endTime;
        this.options = this.getDatePickerOptions(this.cardInfo.startTime);
        this.optionsEnd = this.getDatePickerOptions(this.cardInfo.endTime);
        this.oldCardStatus = this.cardInfo.cardStatus;
        this.unEdit = Number(this.cardInfo.cardStatus) === CardStatusEnum['unregister'];
        if (this.cardInfo.fingerprintId !== null) {
            const finger = this.cardInfo.fingerprintId.split(',');
            const fingerList = finger.map(item => item.split('-')[0]);
            this.fingerprint = fingerList
                .map(item => {
                    const finger = ApplicableTypeOptions.find(finger => finger.value === item);
                    if (finger) {
                        return (item = this.translateService.instant(finger.label));
                    }
                })
                .join(' ');
        }

        console.log(this.cardInfo)
    }

    beforeSubmitAction() {
        if (this.cardInfo.cardCode && this.cardInfo.cardCode.length < 10) {
            const zeroLen = 10 - this.cardInfo.cardCode.length;
            const zeroList = new Array(zeroLen).fill(0);
            this.cardInfo.cardCode = zeroList.concat(this.cardInfo.cardCode).join('');
        }
        
        let dateArr = this.cardInfo.startTime.toString().split('-');
        if (dateArr.length >= 3) {
            const startObj = {
                year: Number(dateArr[0]),
                month: Number(dateArr[1]),
                day: Number(dateArr[2])
            }
            this.cardInfo.startTime = startObj
        }
        dateArr = this.cardInfo.endTime.toString().split('-');
        if (dateArr.length >= 3) {
            const endObj = {
                year: Number(dateArr[0]),
                month: Number(dateArr[1]),
                day: Number(dateArr[2])
            }
            this.cardInfo.endTime = endObj
        }

        if (this.cardInfo.startTime) {
            this.cardInfo.startTime['month']--;
            this.cardInfo.startTime = DateUtil.dateToDayLongString(this.cardInfo.startTime);
        }
        if (this.cardInfo.endTime) {
            this.cardInfo.endTime['month']--;
            this.cardInfo.endTime = DateUtil.dateToDayLongString(this.cardInfo.endTime);
        }
        return this.cardInfo;
    }

    submit(ngFormValid, callBackFun?: Function) {
        if (!ngFormValid) {
            this.ngFormValid = ngFormValid;
            return false;
        }
        const param = this.beforeSubmitAction();
        const isCustomerSubmitRouter = Array.isArray(param) && param.length === 2 && !!param[1]['submitUrl'];
        const params = isCustomerSubmitRouter ? param[0] : param;
        const submitedReturnUrl = isCustomerSubmitRouter ? param[1]['submitUrl'] : false;

        this.cardService.update(params).subscribe(
            data => {
                if (data.data) {
                    this.showToast('success', 'ngForm.update.success');
                    if (callBackFun) {
                        callBackFun();
                    }
                    if (submitedReturnUrl) {
                        this.router.navigated = true;
                        this.router.navigate([submitedReturnUrl]);
                        setTimeout(() => {
                            window.location.reload();
                        }, 500);
                    } else {
                        this.cancel();
                    }
                } else {
                    this.showToast('error', 'ngForm.update.error');
                    this.formatDate();
                }
            },
            err => {
                this.showToast('error', 'ngForm.update.error');
            },
        );
    }

    /**
     * 重写cancel方法，保持分页状态
     */
    cancel(): void {
        // 获取当前路由的查询参数
        const queryParams = this.activeRoute.snapshot.queryParams;
        const navigationExtras: any = {
            relativeTo: this.activeRoute
        };

        // 如果有分页参数，则保持这些参数
        if (queryParams.page || queryParams.perPage) {
            navigationExtras.queryParams = {
                page: queryParams.page,
                perPage: queryParams.perPage
            };
        }

        this.router.navigate(['../../'], navigationExtras);
    }

    updateCardNoByType(data) {
        this.cardInfo.cardCodeType = data;
        if (!this.cardInfo.cardCode) {
            return;
        }
        if (data === '16') {
            this.cardInfo.cardCode = parseInt(this.cardInfo.cardCode, 10).toString(16);
        } else {
            this.cardInfo.cardCode = parseInt(this.cardInfo.cardCode, 16).toString();
        }
    }

    formatDate() {
        if (this.cardInfo.startTime) {
            this.cardInfo.startTime = DateUtil.simpleDateToNgbDate(new Date(this.cardInfo.startTime.toString()));
        }
        if (this.cardInfo.endTime) {
            this.cardInfo.endTime = DateUtil.simpleDateToNgbDate(new Date(this.cardInfo.endTime.toString()));
        }
    }

    // 录入人脸
    collectFace() {
        const modal = this.modal.open(AccessCardManagementFaceCollectComponent, {
            size: 'lg',
            backdrop: 'static',
            container: 'app-access-card-edit',
        });
        modal.componentInstance.cardData = this.cardInfo;
        modal.componentInstance.currentFaceImg = this.faceBlob;
        modal.result
            .then(res => {
                if (res) {
                    this.faceBlob = res;
                    this.faceimg = this.sanitizer.bypassSecurityTrustUrl(window.URL.createObjectURL(res));
                }
            })
            .catch(() => void 0);
    }

    // 录入指纹
    collectFingerprint() {
        const modal = this.modal.open(AccessCardManagementFingerprintCollectComponent, {
            size: 'lg',
            backdrop: 'static',
            container: 'app-access-card-edit',
        });
        modal.componentInstance.cardData = this.cardInfo;
        modal.result
            .then(res => {
                if (res) {
                    this.fingerprint =
                        (res.HKshowList.length
                            ? this.translateService.instant('s2.accessControl.Hikvision') + ' '
                            : '') +
                        (res.NBEshowList.length
                            ? this.translateService.instant('s2.accessControl.Newabel') + ' '
                            : '') +
                        (res.VertivShowList.length ? this.translateService.instant('s2.accessControl.Vertiv') : '');
                }
            })
            .catch(() => void 0);
    }

    // 删除人脸
    deleteFace(cardId) {
        const tips =
            this.translateService.instant('general.common.confirmDelete') +
            this.translateService.instant('s2.accessControl.face') +
            '？';
        this.alertService.deleteConfirm(tips).then(res => {
            if (res.value && this.cardId) {
                const param = [this.cardId];
                this.featureSrv.deleteFaceData(param).subscribe(
                    res => {
                        if (res.data) {
                            this.showToast('success', 'accessControl.cardManagement.deleteFaceSuccess');
                            this.faceimg = '';
                        } else {
                            this.showToast('error', 'accessControl.cardManagement.deleteFaceFail');
                        }
                    },
                    err => this.showToast('error', 'accessControl.cardManagement.deleteFaceFail'),
                );
            }
        });
    }

    showToast(type: string, msg: string) {
        this.toastService.showToast(type, this.translateService.instant(msg), '');
    }

    // sendCommand() {
    //   if (!this.cardInfo || !this.cardInfo.cardStatus || this.cardInfo.cardStatus === this.oldCardStatus) {
    //     return;
    //   }

    //   this.cardService.sendCardCommand(this.cardInfo.cardStatus, this.cardInfo.cardId).subscribe((res: DoorAreaOperationRes) => {
    //     if (res && res.data) {
    //       this.toastService.showSuccessToast({ msg: this.translateService.instant('accessControl.cardManagement.commandSuccess'), position: this.toastService.position.top_right });
    //     } else {
    //       this.toastService.showErrorToast({ msg: this.translateService.instant('accessControl.cardManagement.commandFail'), position: this.toastService.position.top_right });
    //     }
    //   });
    // }
}
